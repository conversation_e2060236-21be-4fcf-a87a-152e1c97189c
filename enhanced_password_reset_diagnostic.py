#!/usr/bin/env python3
"""
Enhanced Password Reset Email Diagnostic Script
Comprehensive investigation to understand why password reset emails are not appearing in Brevo logs
"""

import requests
import json
import time
from datetime import datetime
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'masonite-backend-clean'))

def print_header(title):
    """Print formatted section header"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_step(step, description):
    """Print formatted step"""
    print(f"\n🔍 Step {step}: {description}")
    print("-" * 50)

def test_brevo_api_connectivity():
    """Test basic Brevo API connectivity"""
    print_step(1, "Testing Brevo API Connectivity")
    
    # Use the correct API key from .env
    api_key = "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"
    
    headers = {
        'accept': 'application/json',
        'api-key': api_key
    }
    
    try:
        # Test account endpoint first
        response = requests.get(
            'https://api.brevo.com/v3/account',
            headers=headers,
            timeout=10
        )
          print(f"📡 Account API Response: {response.status_code}")
        
        if response.status_code == 200:
            account_info = response.json()
            print(f"✅ Account Connected: {account_info.get('email', 'Unknown')}")
            
            # Handle different response structures
            plan_info = account_info.get('plan', {})
            if isinstance(plan_info, list) and plan_info:
                plan_type = plan_info[0].get('type', 'Unknown')
            elif isinstance(plan_info, dict):
                plan_type = plan_info.get('type', 'Unknown')
            else:
                plan_type = 'Unknown'
                
            print(f"📊 Plan Type: {plan_type}")
            return True
        else:
            print(f"❌ Failed to connect to account: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def send_password_reset_email_with_monitoring(test_email="<EMAIL>"):
    """Send password reset email with enhanced monitoring"""
    print_step(2, f"Sending Password Reset Email with Full Monitoring")
    
    # Use the correct API key from .env
    api_key = "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"
    api_url = "https://api.brevo.com/v3/smtp/email"
    
    # Use current timestamp for unique tracking
    current_time = datetime.now()
    formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
    reset_token = f"test_reset_{int(current_time.timestamp())}"
    
    print(f"🕐 Sending at: {formatted_time}")
    print(f"🔑 Reset Token: {reset_token}")
    print(f"📧 Target Email: {test_email}")
    
    email_data = {
        "sender": {
            "name": "Secure Backend - Diagnostic Test",
            "email": "<EMAIL>"
        },
        "to": [
            {
                "email": test_email,
                "name": "Test User"
            }
        ],
        "subject": f"🔍 Password Reset Test - {formatted_time}",
        "htmlContent": f"""
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; border: 2px solid #007bff; padding: 20px;">
            <h2 style="color: #007bff;">🔍 DIAGNOSTIC: Password Reset Test</h2>
            <p><strong>Test Details:</strong></p>
            <ul>
                <li>Timestamp: {formatted_time}</li>
                <li>Reset Token: {reset_token}</li>
                <li>Test ID: DIAG_{int(current_time.timestamp())}</li>
            </ul>
            <p>This is a diagnostic email to test password reset functionality.</p>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <p><strong>Reset URL (for testing):</strong></p>
                <p style="word-break: break-all; font-family: monospace;">
                    http://localhost:4200/auth/reset-password?token={reset_token}
                </p>
            </div>
            <p style="color: #666; font-size: 12px;">
                This is a diagnostic test. The token is for testing only and will not reset any actual password.
            </p>
        </div>
        """,
        "textContent": f"""
DIAGNOSTIC: Password Reset Test

Test Details:
- Timestamp: {formatted_time}
- Reset Token: {reset_token}
- Test ID: DIAG_{int(current_time.timestamp())}

This is a diagnostic email to test password reset functionality.

Reset URL (for testing):
http://localhost:4200/auth/reset-password?token={reset_token}

This is a diagnostic test. The token is for testing only and will not reset any actual password.
        """,
        # Add tags and custom headers for tracking
        "tags": ["password-reset", "diagnostic", f"test-{int(current_time.timestamp())}"],
        "headers": {
            "X-Diagnostic-Test": "true",
            "X-Test-Timestamp": str(int(current_time.timestamp())),
            "X-Test-Type": "password-reset"
        }
    }
    
    headers = {
        'accept': 'application/json',
        'api-key': api_key,
        'content-type': 'application/json'
    }
    
    print("\n📝 Email Payload:")
    print(f"   Subject: {email_data['subject']}")
    print(f"   From: {email_data['sender']['email']}")
    print(f"   To: {email_data['to'][0]['email']}")
    print(f"   Tags: {email_data['tags']}")
    
    try:
        print("\n🚀 Sending email...")
        send_time = datetime.now()
        
        response = requests.post(
            api_url,
            headers=headers,
            data=json.dumps(email_data),
            timeout=15
        )
        
        response_time = datetime.now()
        duration = (response_time - send_time).total_seconds()
        
        print(f"⏱️  Response received in {duration:.2f} seconds")
        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 201:
            response_data = response.json()
            message_id = response_data.get('messageId')
            
            print(f"✅ EMAIL SENT SUCCESSFULLY!")
            print(f"   Message ID: {message_id}")
            print(f"   Sent at: {send_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   Response at: {response_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            return {
                'success': True,
                'message_id': message_id,
                'sent_time': send_time,
                'response_time': response_time,
                'test_token': reset_token
            }
        else:
            print(f"❌ EMAIL FAILED!")
            try:
                error_data = response.json()
                print(f"   Error Details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"   Raw Response: {response.text}")
            
            return {
                'success': False,
                'error': f"Status {response.status_code}: {response.text}",
                'sent_time': send_time,
                'response_time': response_time
            }
                
    except Exception as e:
        print(f"❌ REQUEST FAILED: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def check_email_logs(message_id=None, timestamp=None):
    """Check Brevo email logs for sent emails"""
    print_step(3, "Checking Brevo Email Activity Logs")
    
    # Use the correct API key from .env
    api_key = "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"
    
    headers = {
        'accept': 'application/json',
        'api-key': api_key
    }
    
    # Check recent email events
    try:
        print("🔍 Checking email events (last 10)...")
        
        events_url = "https://api.brevo.com/v3/emailCampaigns"
        response = requests.get(events_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            campaigns = response.json()
            print(f"📊 Found {campaigns.get('count', 0)} email campaigns")
        else:
            print(f"⚠️  Could not fetch campaigns: {response.status_code}")
    
    except Exception as e:
        print(f"⚠️  Error checking campaigns: {e}")
    
    # Check transactional email stats
    try:
        print("\n🔍 Checking transactional email statistics...")
        
        stats_url = "https://api.brevo.com/v3/smtp/statistics/reports"
        params = {
            'limit': 10,
            'offset': 0
        }
        
        response = requests.get(stats_url, headers=headers, params=params, timeout=10)
        
        if response.status_code == 200:
            stats = response.json()
            print(f"📊 Transactional Email Reports:")
            
            reports = stats.get('reports', [])
            if reports:
                for report in reports[:5]:  # Show first 5
                    print(f"   📅 {report.get('date', 'Unknown date')}: "
                          f"Sent: {report.get('sent', 0)}, "
                          f"Delivered: {report.get('delivered', 0)}, "
                          f"Opens: {report.get('uniqueOpens', 0)}")
            else:
                print("   No reports found")
        else:
            print(f"⚠️  Could not fetch stats: {response.status_code}")
    
    except Exception as e:
        print(f"⚠️  Error checking stats: {e}")

def test_endpoint_password_reset():
    """Test the actual password reset endpoint"""
    print_step(4, "Testing Actual Password Reset Endpoint")
    
    endpoint_url = "http://localhost:8000/api/auth/forgot-password"
    test_email = "<EMAIL>"
    
    payload = {
        "email": test_email
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }
    
    print(f"🎯 Testing endpoint: {endpoint_url}")
    print(f"📧 Test email: {test_email}")
    
    try:
        print("\n🚀 Sending request to endpoint...")
        send_time = datetime.now()
        
        response = requests.post(
            endpoint_url,
            json=payload,
            headers=headers,
            timeout=15
        )
        
        response_time = datetime.now()
        duration = (response_time - send_time).total_seconds()
        
        print(f"⏱️  Response received in {duration:.2f} seconds")
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"✅ ENDPOINT SUCCESS!")
            print(f"   Message: {response_data.get('message', 'No message')}")
            
            return {
                'success': True,
                'response': response_data,
                'sent_time': send_time,
                'response_time': response_time
            }
        else:
            print(f"❌ ENDPOINT FAILED!")
            try:
                error_data = response.json()
                print(f"   Error: {json.dumps(error_data, indent=2)}")
            except:
                print(f"   Raw Response: {response.text}")
            
            return {
                'success': False,
                'error': f"Status {response.status_code}: {response.text}",
                'sent_time': send_time,
                'response_time': response_time
            }
                
    except Exception as e:
        print(f"❌ REQUEST FAILED: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def main():
    """Run complete diagnostic suite"""
    print_header("ENHANCED PASSWORD RESET EMAIL DIAGNOSTIC")
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Test API connectivity
    api_connected = test_brevo_api_connectivity()
    
    if not api_connected:
        print("\n❌ Cannot proceed without API connectivity")
        return
    
    # Step 2: Send direct API email with monitoring
    api_result = send_password_reset_email_with_monitoring()
    
    # Step 3: Check email logs
    message_id = api_result.get('message_id') if api_result.get('success') else None
    timestamp = api_result.get('sent_time') if api_result.get('success') else None
    
    check_email_logs(message_id, timestamp)
    
    # Step 4: Test actual endpoint
    endpoint_result = test_endpoint_password_reset()
    
    # Final summary
    print_header("DIAGNOSTIC SUMMARY")
    
    print(f"🔗 API Connectivity: {'✅ Connected' if api_connected else '❌ Failed'}")
    print(f"📧 Direct API Email: {'✅ Sent' if api_result.get('success') else '❌ Failed'}")
    
    if api_result.get('success'):
        print(f"   Message ID: {api_result.get('message_id')}")
        print(f"   Sent Time: {api_result.get('sent_time')}")
    
    print(f"🎯 Endpoint Test: {'✅ Success' if endpoint_result.get('success') else '❌ Failed'}")
    
    print(f"\n📋 NEXT STEPS:")
    
    if api_result.get('success'):
        print("   1. Check Brevo dashboard manually for the sent email")
        print("   2. Check spam folder in target email inbox")
        print("   3. Verify Brevo account sending reputation")
        print(f"   4. Look for Message ID: {api_result.get('message_id')} in Brevo logs")
        print("   5. Consider checking Brevo webhook events if configured")
    else:
        print("   1. Fix API email sending issues first")
        print("   2. Verify API key and permissions")
        print("   3. Check Brevo account status and limits")
    
    print(f"\n🕐 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
