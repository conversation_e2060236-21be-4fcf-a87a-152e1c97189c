"use strict";(self.webpackChunksecure_frontend=self.webpackChunksecure_frontend||[]).push([[925],{3925:(h,u,r)=>{r.r(u),r.d(u,{DataRestorationComponent:()=>U});var _=r(467),e=r(7241),g=r(6396),i=r(9417),c=r(5596),f=r(1074),l=r(2506),d=r(8834),p=r(9183),v=r(8822),D=r(4843),E=r(3890),P=r(4796),y=r(5333);function C(o,s){if(1&o&&(e.j41(0,"div",15)(1,"mat-icon"),e.EFF(2,"payment"),e.k0s(),e.j41(3,"div")(4,"strong"),e.<PERSON><PERSON>(5,"Payment Data"),e.k0s(),e.j41(6,"div",16),e.<PERSON><PERSON>(7),e.k0s()()()),2&o){const t=e.XpG();e.R7$(7),e.SpI("",t.preservedDataSummary.paymentRecords," records")}}function F(o,s){if(1&o&&(e.j41(0,"div",15)(1,"mat-icon"),e.EFF(2,"history"),e.k0s(),e.j41(3,"div")(4,"strong"),e.EFF(5,"Transaction History"),e.k0s(),e.j41(6,"div",16),e.EFF(7),e.k0s()()()),2&o){const t=e.XpG();e.R7$(7),e.SpI("",t.preservedDataSummary.transactionHistory," transactions")}}function k(o,s){1&o&&(e.j41(0,"div",15)(1,"mat-icon"),e.EFF(2,"account_circle"),e.k0s(),e.j41(3,"div")(4,"strong"),e.EFF(5,"Profile Backup"),e.k0s(),e.j41(6,"div",16),e.EFF(7,"Available"),e.k0s()()())}function x(o,s){if(1&o&&(e.j41(0,"div",15)(1,"mat-icon"),e.EFF(2,"security"),e.k0s(),e.j41(3,"div")(4,"strong"),e.EFF(5,"Security Logs"),e.k0s(),e.j41(6,"div",16),e.EFF(7),e.k0s()()()),2&o){const t=e.XpG();e.R7$(7),e.SpI("",t.preservedDataSummary.securityEvents," events")}}function M(o,s){1&o&&(e.j41(0,"mat-checkbox",24)(1,"strong"),e.EFF(2,"Restore Payment Data"),e.k0s(),e.j41(3,"div",25),e.EFF(4," Restore saved payment methods and billing information "),e.k0s()())}function R(o,s){1&o&&(e.j41(0,"mat-checkbox",26)(1,"strong"),e.EFF(2,"Restore Transaction History"),e.k0s(),e.j41(3,"div",25),e.EFF(4," Restore past transaction records and order history "),e.k0s()())}function O(o,s){1&o&&(e.j41(0,"mat-checkbox",27)(1,"strong"),e.EFF(2,"Restore Profile Data"),e.k0s(),e.j41(3,"div",25),e.EFF(4," Restore your previous profile information and preferences "),e.k0s()())}function b(o,s){1&o&&(e.j41(0,"mat-checkbox",28)(1,"strong"),e.EFF(2,"Restore Security Logs"),e.k0s(),e.j41(3,"div",25),e.EFF(4," Restore login history and security event records "),e.k0s()())}function I(o,s){if(1&o&&(e.j41(0,"form",17)(1,"h4"),e.EFF(2,"Restoration Options"),e.k0s(),e.j41(3,"p",18),e.EFF(4,"Select which data you want to restore:"),e.k0s(),e.j41(5,"div",19),e.DNE(6,M,5,0,"mat-checkbox",20)(7,R,5,0,"mat-checkbox",21)(8,O,5,0,"mat-checkbox",22)(9,b,5,0,"mat-checkbox",23),e.k0s()()),2&o){const t=e.XpG();e.Y8G("formGroup",t.restoreForm),e.R7$(6),e.Y8G("ngIf",t.preservedDataSummary.paymentRecords>0),e.R7$(),e.Y8G("ngIf",t.preservedDataSummary.transactionHistory>0),e.R7$(),e.Y8G("ngIf",t.preservedDataSummary.profileBackup),e.R7$(),e.Y8G("ngIf",t.preservedDataSummary.securityEvents>0)}}function S(o,s){1&o&&(e.j41(0,"div",29)(1,"mat-icon",30),e.EFF(2,"info"),e.k0s(),e.j41(3,"p"),e.EFF(4,"No restorable data was found in your preserved data backup."),e.k0s()())}function T(o,s){1&o&&e.nrm(0,"mat-spinner",31)}function j(o,s){1&o&&(e.j41(0,"mat-icon"),e.EFF(1,"restore"),e.k0s())}let U=(()=>{class o{constructor(t,a,n,m,L,B){this.fb=t,this.accountDeletionService=a,this.authService=n,this.snackBar=m,this.router=L,this.route=B,this.restorationComplete=new e.bkB,this.restorationSkipped=new e.bkB,this.isLoading=!1,this.componentEmail="",this.componentUserId="",this.componentPreservedData=null,this.restoreForm=this.fb.group({restorePaymentData:[!0],restoreTransactionHistory:[!0],restoreProfileData:[!1],restoreSecurityLogs:[!1]})}ngOnInit(){console.log("\u{1f504} Data restoration component initialized");const t=this.router.getCurrentNavigation()?.extras?.state;console.log("\u{1f50d} Navigation state:",t),this.componentEmail=this.route.snapshot.queryParams.email||t?.userEmail||this.email,this.componentUserId=this.route.snapshot.queryParams.userId||t?.userId||this.userId,console.log("\u{1f4e7} Component email:",this.componentEmail),console.log("\u{1f194} Component userId:",this.componentUserId),this.componentPreservedData=t?.preservedData||this.preservedData,console.log("\u{1f4e6} Component preserved data:",this.componentPreservedData),!this.componentPreservedData&&this.componentEmail?this.fetchPreservedData():this.setupForm()}fetchPreservedData(){var t=this;return(0,_.A)(function*(){try{console.log("\u{1f50d} Fetching preserved data for:",t.componentEmail);const a=yield(0,D._)(t.accountDeletionService.checkPreservedData(t.componentEmail));console.log("\u{1f4e6} Fetched preserved data:",a),t.componentPreservedData=a,t.setupForm()}catch(a){console.error("\u274c Error fetching preserved data:",a),t.snackBar.open("Error loading preserved data","Close",{duration:5e3})}})()}setupForm(){if(console.log("\u{1f527} Setting up restoration form"),console.log("\u{1f4ca} Preserved data summary:",this.componentPreservedData?.preservedDataSummary),this.componentPreservedData?.preservedDataSummary){const t=this.componentPreservedData.preservedDataSummary;console.log("\u{1f4cb} Form values being set:",{restorePaymentData:t.paymentRecords>0,restoreTransactionHistory:t.transactionHistory>0,restoreProfileData:!0===t.profileBackup,restoreSecurityLogs:t.securityEvents>0}),this.restoreForm.patchValue({restorePaymentData:t.paymentRecords>0,restoreTransactionHistory:t.transactionHistory>0,restoreProfileData:!0===t.profileBackup,restoreSecurityLogs:t.securityEvents>0})}}restoreData(){var t=this;return(0,_.A)(function*(){t.isLoading=!0;try{console.log("\u{1f504} Starting data restoration process..."),console.log("\u{1f4e7} Email for restoration:",t.componentEmail);const a={restorePaymentData:t.restoreForm.value.restorePaymentData,restoreTransactionHistory:t.restoreForm.value.restoreTransactionHistory,restoreProfileData:t.restoreForm.value.restoreProfileData,restoreSecurityLogs:t.restoreForm.value.restoreSecurityLogs};console.log("\u{1f4e6} Restore options:",a);const n=t.componentUserId||t.userId||"temp-user-id";console.log("\u{1f194} Final user ID for restoration:",n),"temp-user-id"===n&&console.log("\u26a0\ufe0f WARNING: Using temp-user-id, this may cause restoration to fail");const m=yield(0,D._)(t.accountDeletionService.restoreData(n,t.componentEmail,a));console.log("\u2705 Data restoration completed:",m),t.snackBar.open(`Data restoration completed successfully! ${m.message||""}`,"Close",{duration:8e3,panelClass:["snack-bar-success"]}),setTimeout(()=>{t.restorationComplete.emit(m),t.router.navigate(["/auth/login"],{queryParams:{message:"Data restored successfully. You can now log in."}})},2e3)}catch(a){console.error("\u274c Error restoring data:",a),t.snackBar.open(a.error?.message||a.message||"Failed to restore data. Please try again.","Close",{duration:8e3,panelClass:["snack-bar-error"]})}finally{t.isLoading=!1}})()}skipRestoration(){console.log("\u23ed\ufe0f User skipped data restoration"),this.snackBar.open("Data restoration skipped. You can now log in.","Close",{duration:5e3,panelClass:["snack-bar-info"]}),setTimeout(()=>{this.restorationSkipped.emit(),this.router.navigate(["/auth/login"],{queryParams:{message:"You can now log in to your account."}})},1500)}get hasSelectableData(){const t=this.componentPreservedData||this.preservedData;if(!t?.preservedDataSummary)return!1;const a=t.preservedDataSummary;return console.log("\u{1f50d} Checking selectable data with summary:",a),a.paymentRecords>0||a.transactionHistory>0||!0===a.profileBackup||a.securityEvents>0}get preservedDataSummary(){const a=(this.componentPreservedData||this.preservedData)?.preservedDataSummary||{};return console.log("\u{1f50d} Getting preserved data summary:",a),a}static#e=this.\u0275fac=function(a){return new(a||o)(e.rXU(i.ok),e.rXU(E.z),e.rXU(P.u),e.rXU(v.UG),e.rXU(y.Ix),e.rXU(y.nX))};static#t=this.\u0275cmp=e.VBU({type:o,selectors:[["app-data-restoration"]],inputs:{email:"email",userId:"userId",preservedData:"preservedData"},outputs:{restorationComplete:"restorationComplete",restorationSkipped:"restorationSkipped"},decls:35,vars:9,consts:[[1,"data-restoration-container"],[1,"restoration-card"],["color","primary"],[1,"welcome-message"],["color","primary",1,"large-icon"],[1,"preserved-data-summary"],[1,"data-grid"],["class","data-item",4,"ngIf"],[3,"formGroup",4,"ngIf"],["class","warning-note",4,"ngIf"],[1,"actions"],["mat-button","",3,"click"],["mat-raised-button","","color","primary",3,"click","disabled"],["diameter","20",4,"ngIf"],[4,"ngIf"],[1,"data-item"],[1,"data-count"],[3,"formGroup"],[1,"section-description"],[1,"restore-options"],["formControlName","restorePaymentData",4,"ngIf"],["formControlName","restoreTransactionHistory",4,"ngIf"],["formControlName","restoreProfileData",4,"ngIf"],["formControlName","restoreSecurityLogs",4,"ngIf"],["formControlName","restorePaymentData"],[1,"option-description"],["formControlName","restoreTransactionHistory"],["formControlName","restoreProfileData"],["formControlName","restoreSecurityLogs"],[1,"warning-note"],["color","warn"],["diameter","20"]],template:function(a,n){1&a&&(e.j41(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"mat-card-title")(4,"mat-icon",2),e.EFF(5,"restore"),e.k0s(),e.EFF(6," Restore Previous Data "),e.k0s()(),e.j41(7,"mat-card-content")(8,"div",3)(9,"mat-icon",4),e.EFF(10,"info"),e.k0s(),e.j41(11,"div")(12,"h3"),e.EFF(13,"Welcome back!"),e.k0s(),e.j41(14,"p"),e.EFF(15," We found previously preserved data from your deleted account. You can choose to restore this data or permanently delete it. "),e.k0s()()(),e.j41(16,"div",5)(17,"h4"),e.EFF(18,"Available Data"),e.k0s(),e.j41(19,"div",6),e.DNE(20,C,8,1,"div",7)(21,F,8,1,"div",7)(22,k,8,0,"div",7)(23,x,8,1,"div",7),e.k0s()(),e.DNE(24,I,10,5,"form",8)(25,S,5,0,"div",9),e.j41(26,"div",10)(27,"button",11),e.bIt("click",function(){return n.skipRestoration()}),e.j41(28,"mat-icon"),e.EFF(29,"delete_forever"),e.k0s(),e.EFF(30," Delete All Data "),e.k0s(),e.j41(31,"button",12),e.bIt("click",function(){return n.restoreData()}),e.DNE(32,T,1,0,"mat-spinner",13)(33,j,2,0,"mat-icon",14),e.EFF(34," Restore Selected Data "),e.k0s()()()()()),2&a&&(e.R7$(20),e.Y8G("ngIf",n.preservedDataSummary.paymentRecords>0),e.R7$(),e.Y8G("ngIf",n.preservedDataSummary.transactionHistory>0),e.R7$(),e.Y8G("ngIf",n.preservedDataSummary.profileBackup),e.R7$(),e.Y8G("ngIf",n.preservedDataSummary.securityEvents>0),e.R7$(),e.Y8G("ngIf",n.hasSelectableData),e.R7$(),e.Y8G("ngIf",!n.hasSelectableData),e.R7$(6),e.Y8G("disabled",n.isLoading||!n.hasSelectableData),e.R7$(),e.Y8G("ngIf",n.isLoading),e.R7$(),e.Y8G("ngIf",!n.isLoading))},dependencies:[g.MD,g.bT,i.X1,i.qT,i.BC,i.cb,i.j4,i.JD,c.Hu,c.RN,c.m2,c.MM,c.dh,f.m_,f.An,l.g7,l.So,d.Hl,d.$z,p.D6,p.LG,v._T],styles:[".data-restoration-container[_ngcontent-%COMP%]{max-width:700px;margin:0 auto;padding:20px}.restoration-card[_ngcontent-%COMP%]{margin-bottom:20px}.mat-card-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px}.welcome-message[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:15px;padding:20px;background-color:#e3f2fd;border:1px solid #bbdefb;border-radius:8px;margin-bottom:20px}.welcome-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 10px;color:#1565c0}.welcome-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#1976d2}.large-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px}.preserved-data-summary[_ngcontent-%COMP%]{margin:20px 0}.preserved-data-summary[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 15px;color:#495057}.data-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:15px;margin:15px 0}.data-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:15px;background-color:#f8f9fa;border:1px solid #e9ecef;border-radius:8px}.data-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#6c757d}.data-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{display:block;margin-bottom:4px;color:#495057}.data-count[_ngcontent-%COMP%]{font-size:12px;color:#6c757d}.section-description[_ngcontent-%COMP%]{color:#666;margin-bottom:15px}.restore-options[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:15px;margin:20px 0}.option-description[_ngcontent-%COMP%]{font-size:12px;color:#888;margin-top:4px}.warning-note[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;padding:15px;background-color:#fff3cd;border:1px solid #ffeaa7;border-radius:8px;color:#856404;margin:20px 0}.warning-note[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0}.actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-top:20px;gap:15px}.actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}@media (max-width: 768px){.data-restoration-container[_ngcontent-%COMP%]{padding:10px}.data-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.actions[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;justify-content:center}.welcome-message[_ngcontent-%COMP%]{flex-direction:column;text-align:center}}"]})}return o})()},4843:(h,u,r)=>{r.d(u,{_:()=>g});var _=r(9350),e=r(7707);function g(i,c){const f="object"==typeof c;return new Promise((l,d)=>{const p=new e.Ms({next:v=>{l(v),p.unsubscribe()},error:d,complete:()=>{f?l(c.defaultValue):d(new _.G)}});i.subscribe(p)})}}}]);