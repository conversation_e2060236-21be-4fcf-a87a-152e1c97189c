#!/usr/bin/env python3
"""
Test the critical fix - no automatic restoration when no preserved data
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3002/api"

def test_critical_fix():
    """Test that accounts with no preserved data are not automatically restored"""
    email = f"test.critical.{int(time.time())}@example.com"
    password = "TestPassword123!"
    
    print("🧪 Testing Critical Fix: No Automatic Restoration")
    print("=" * 60)
    
    # Step 1: Register user
    print("📝 Step 1: Registering user...")
    register_data = {
        "email": email,
        "password": password,
        "confirmPassword": password,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+**********"
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=register_data)
    if response.status_code not in [200, 201]:
        print(f"❌ Registration failed: {response.text}")
        return False
    
    user_data = response.json()
    user_id = user_data.get('userId') or user_data.get('user', {}).get('id')
    token = user_data.get('token')
    print(f"✅ User registered with ID: {user_id}")
    
    # Step 2: Request deletion with NO data preservation
    print("\n📝 Step 2: Requesting deletion with NO data preservation...")
    deletion_data = {
        "preservePaymentData": False,
        "preserveTransactionHistory": False,
        "preserveProfileData": False,
        "preserveSecurityLogs": False,
        "customRetentionPeriod": 30,
        "reason": "Testing critical fix"
    }
    
    response = requests.post(f"{BASE_URL}/account/request-deletion", 
                           json=deletion_data,
                           headers={'Authorization': f'Bearer {token}'})
    
    if response.status_code != 200:
        print(f"❌ Deletion request failed: {response.text}")
        return False
    
    deletion_response = response.json()
    deletion_id = deletion_response['deletionId']
    confirmation_token = deletion_response['confirmationToken']
    print(f"✅ Deletion requested with ID: {deletion_id}")
    
    # Step 3: Confirm deletion
    print("\n📝 Step 3: Confirming deletion...")
    response = requests.post(f"{BASE_URL}/account/confirm-deletion", 
                           json={'token': confirmation_token})
    
    if response.status_code != 200:
        print(f"❌ Deletion confirmation failed: {response.text}")
        return False
    
    confirm_response = response.json()
    print("✅ Deletion confirmed")
    print(f"📄 Preserved data summary: {confirm_response.get('preservedDataSummary', {})}")
    
    # Step 4: Check preserved data
    print("\n📝 Step 4: Checking preserved data...")
    response = requests.post(f"{BASE_URL}/account/check-preserved-data", 
                           json={'email': email})
    
    if response.status_code != 200:
        print(f"❌ Check preserved data failed: {response.text}")
        return False
    
    check_response = response.json()
    has_preserved = check_response.get('hasPreservedData', False)
    preserved_summary = check_response.get('preservedDataSummary', {})
    
    print(f"📊 Has preserved data: {has_preserved}")
    print(f"📊 Preserved data summary: {preserved_summary}")
    
    if has_preserved:
        print("❌ FAIL: Expected no preserved data but found some")
        return False
    
    print("✅ PASS: No preserved data as expected")
    
    # Step 5: Test signup - should create NEW user, not restore old one
    print("\n📝 Step 5: Testing signup - should create NEW user...")
    signup_data = {
        "email": email,
        "password": password,
        "confirmPassword": password,
        "firstName": "New",  # Different name to verify it's a new user
        "lastName": "User",
        "phone": "+**********"
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=signup_data)
    
    if response.status_code not in [200, 201]:
        print(f"❌ Second registration failed: {response.text}")
        return False
    
    signup_response = response.json()
    new_user_id = signup_response.get('userId') or signup_response.get('user', {}).get('id')
    signup_has_preserved = signup_response.get('hasPreservedData', False)
    signup_preserved_summary = signup_response.get('preservedDataSummary', {})
    user_first_name = signup_response.get('user', {}).get('firstName', '')
    message = signup_response.get('message', '')
    
    print(f"📊 Signup response:")
    print(f"   - New User ID: {new_user_id}")
    print(f"   - Original User ID: {user_id}")
    print(f"   - User First Name: {user_first_name}")
    print(f"   - Has preserved data: {signup_has_preserved}")
    print(f"   - Preserved summary: {signup_preserved_summary}")
    print(f"   - Message: {message}")
    
    # Step 6: Validate expectations
    print("\n📝 Step 6: Validating expectations...")
    
    # Should be a completely new user
    if str(new_user_id) == str(user_id):
        print(f"❌ FAIL: Same user ID returned - old account was restored instead of creating new one")
        print(f"   This means the fix didn't work - system still restoring accounts with no preserved data")
        return False
    
    # Should have new user data
    if user_first_name != "New":
        print(f"❌ FAIL: Expected firstName 'New' but got '{user_first_name}'")
        return False
    
    # Should not have preserved data
    if signup_has_preserved:
        print(f"❌ FAIL: New user should not have preserved data")
        return False
    
    # Message should indicate new registration
    if "restored" in message.lower():
        print(f"❌ FAIL: Message indicates restoration but should be new registration")
        return False
    
    print("✅ PASS: New user created successfully (old account permanently deleted)")
    print("✅ PASS: No preserved data for new user")
    print("✅ PASS: No automatic restoration occurred")
    print("✅ PASS: All validations successful!")
    
    return True

def main():
    """Main test function"""
    print("🚀 Starting Critical Fix Test")
    print("Testing that accounts with no preserved data are not automatically restored")
    print("=" * 80)
    
    success = test_critical_fix()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 CRITICAL FIX TEST PASSED!")
        print("✅ System correctly creates new users instead of restoring accounts with no preserved data")
    else:
        print("❌ CRITICAL FIX TEST FAILED!")
        print("⚠️ System is still automatically restoring accounts even when no data is preserved")
    print("🏁 Test completed!")

if __name__ == "__main__":
    main()
