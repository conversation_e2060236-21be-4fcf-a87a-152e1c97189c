#!/usr/bin/env python3
"""
Check detailed status of test users in database
"""

import psycopg2
from datetime import datetime

# Database connection
try:
    conn = psycopg2.connect(
        host="localhost",
        database="masonite_secure_backend",
        user="postgres",
        password="password"
    )
    cursor = conn.cursor()
    
    print("🔍 DETAILED USER STATUS CHECK")
    print("="*60)
    
    # Check both test users
    test_emails = ['<EMAIL>', '<EMAIL>']
    
    for email in test_emails:
        print(f"\n📧 Checking user: {email}")
        print("-" * 40)
        
        # Get user details
        cursor.execute("""
            SELECT id, email, password, email_verified_at, 
                   email_verification_token, email_verification_expires,
                   created_at, updated_at
            FROM users 
            WHERE email = %s
        """, (email,))
        
        user = cursor.fetchone()
        
        if user:
            user_id, email, password_hash, verified_at, token, expires, created, updated = user
            print(f"   ✅ User found!")
            print(f"   ID: {user_id}")
            print(f"   Email: {email}")
            print(f"   Password Hash: {password_hash[:50]}...")
            print(f"   Email Verified At: {verified_at}")
            print(f"   Verification Token: {token}")
            print(f"   Token Expires: {expires}")
            print(f"   Created: {created}")
            print(f"   Updated: {updated}")
            
            # Check OTP records for this user
            cursor.execute("""
                SELECT id, code, type, expires_at, verified_at, 
                       attempt_count, created_at
                FROM otps 
                WHERE user_id = %s 
                ORDER BY created_at DESC 
                LIMIT 5
            """, (user_id,))
            
            otps = cursor.fetchall()
            
            if otps:
                print(f"   📱 Recent OTP records ({len(otps)}):")
                for otp in otps:
                    otp_id, code, otp_type, expires, verified, attempts, created = otp
                    print(f"      OTP ID: {otp_id}, Code: {code}, Type: {otp_type}")
                    print(f"      Expires: {expires}, Verified: {verified}, Attempts: {attempts}")
                    print(f"      Created: {created}")
                    print("      ---")
            else:
                print("   📱 No OTP records found")
                
        else:
            print(f"   ❌ User not found!")
    
    # Check OTP table structure
    print(f"\n🗄️  OTP Table Structure:")
    print("-" * 40)
    cursor.execute("""
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'otps'
        ORDER BY ordinal_position
    """)
    
    columns = cursor.fetchall()
    for col in columns:
        print(f"   {col[0]}: {col[1]} (Nullable: {col[2]})")
    
    # Show recent OTP activity for debugging
    print(f"\n📱 Recent OTP Activity (All Users):")
    print("-" * 40)
    cursor.execute("""
        SELECT o.id, u.email, o.code, o.type, o.expires_at, 
               o.verified_at, o.attempt_count, o.created_at
        FROM otps o
        JOIN users u ON o.user_id = u.id
        ORDER BY o.created_at DESC
        LIMIT 10
    """)
    
    all_otps = cursor.fetchall()
    for otp in all_otps:
        otp_id, email, code, otp_type, expires, verified, attempts, created = otp
        print(f"   ID: {otp_id} | {email} | Code: {code} | Type: {otp_type}")
        print(f"   Expires: {expires} | Verified: {verified} | Attempts: {attempts}")
        print(f"   Created: {created}")
        print("   ---")
    
    cursor.close()
    conn.close()
    
    print(f"\n✅ Database check completed!")
    
except Exception as e:
    print(f"❌ Database error: {e}")
