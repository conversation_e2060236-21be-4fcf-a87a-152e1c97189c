#!/usr/bin/env python3
"""
Script to PERMANENTLY (HARD) delete specific users by ID
This bypasses soft deletes and removes records from the database completely
"""

import os
import sys

# Add the masonite project to path
sys.path.append(os.path.join(os.path.dirname(__file__)))

# Initialize Masonite application
from wsgi import application

# Now we can import models after app initialization
from app.models.User import User
from masonite.orm import DatabaseManager

def force_delete_users():
    """Permanently delete specific users by ID (hard delete)"""
    user_ids_to_delete = [255, 265]  # IDs from the search results
    
    print("🚨 Starting PERMANENT deletion of test users by ID...")
    print("⚠️  WARNING: This will permanently remove users from the database!")
    
    # Get database connection
    db = DatabaseManager().connection()
    
    for user_id in user_ids_to_delete:
        try:
            # First, try to find the user (including soft-deleted ones)
            user = User.with_trashed().find(user_id)
            
            if user:
                email = user.email
                username = user.name or 'Unknown'
                
                print(f"📍 Found user: {email} (ID: {user_id}, Name: {username})")
                
                # Option 1: Use force_delete() method if available
                try:
                    user.force_delete()
                    print(f"✅ PERMANENTLY deleted user: {email} (ID: {user_id}) using force_delete()")
                except AttributeError:
                    # Option 2: Use direct database deletion if force_delete doesn't exist
                    print(f"🔄 force_delete() not available, using direct DB deletion...")
                    db.table('users').where('id', user_id).delete()
                    print(f"✅ PERMANENTLY deleted user: {email} (ID: {user_id}) using direct DB deletion")
                    
            else:
                print(f"ℹ️  User not found with ID: {user_id} (may already be permanently deleted)")
                
        except Exception as e:
            print(f"❌ Error permanently deleting user ID {user_id}: {str(e)}")
            
            # Try alternative approach with raw SQL
            try:
                print(f"🔄 Trying raw SQL deletion for user ID {user_id}...")
                db.query("DELETE FROM users WHERE id = %s", [user_id])
                print(f"✅ PERMANENTLY deleted user ID {user_id} using raw SQL")
            except Exception as sql_error:
                print(f"❌ Raw SQL deletion also failed: {str(sql_error)}")
    
    print("\n🎉 PERMANENT cleanup completed!")
    print("📋 Verifying deletion results...")
    
    # Verify the users are gone
    for user_id in user_ids_to_delete:
        try:
            user = User.with_trashed().find(user_id)
            if user:
                print(f"⚠️  User ID {user_id} still exists: {user.email}")
            else:
                print(f"✅ User ID {user_id} permanently deleted")
        except Exception as e:
            print(f"✅ User ID {user_id} not found (permanently deleted)")

if __name__ == "__main__":
    force_delete_users()
