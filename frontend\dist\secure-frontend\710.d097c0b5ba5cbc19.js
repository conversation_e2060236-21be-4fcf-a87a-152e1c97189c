"use strict";(self.webpackChunksecure_frontend=self.webpackChunksecure_frontend||[]).push([[710],{5710:(On,X,l)=>{l.r(X),l.d(X,{ProfileModule:()=>Mn});var h=l(6396),dt=l(1736),d=l(9417),p=l(5596),y=l(8834),k=l(1074),mt=l(6687),w=l(6863),x=l(9183),T=l(8822),_t=l(3981),pt=l(8511),U=l(5751),D=l(372),z=l(7336),R=l(7035),ut=l(3174),j=l(4466),F=l(3980),t=l(7241),r=l(8564),_=l(7705),C=l(1413),gt=l(7673),S=l(7786),bt=l(983),ht=l(1985),ft=l(1584),f=l(8359),Ct=l(152),b=l(6977),B=l(9172),vt=l(5558),Pt=l(5245),Mt=l(5964),v=l(6609),P=l(6939),Ot=l(1560),yt=l(7218),kt=l(2318),Y=l(2655),I=l(1715);const A=["*"];function wt(i,c){1&i&&t.SdG(0)}const xt=["tabListContainer"],Tt=["tabList"],Ft=["tabListInner"],It=["nextPaginator"],Et=["previousPaginator"],Dt=["content"];function Rt(i,c){}const jt=["tabBodyWrapper"],St=["tabHeader"];function Bt(i,c){}function At(i,c){if(1&i&&t.DNE(0,Bt,0,0,"ng-template",12),2&i){const e=t.XpG().$implicit;t.Y8G("cdkPortalOutlet",e.templateLabel)}}function Lt(i,c){if(1&i&&t.EFF(0),2&i){const e=t.XpG().$implicit;t.JRh(e.textLabel)}}function Gt(i,c){if(1&i){const e=t.RV6();t.j41(0,"div",7,2),t.bIt("click",function(){const n=r.eBV(e),o=n.$implicit,s=n.$index,m=t.XpG(),g=t.sdS(1);return r.Njj(m._handleClick(o,g,s))})("cdkFocusChange",function(n){const o=r.eBV(e).$index,s=t.XpG();return r.Njj(s._tabFocusChanged(n,o))}),t.nrm(2,"span",8)(3,"div",9),t.j41(4,"span",10)(5,"span",11),t.nVh(6,At,1,1,null,12)(7,Lt,1,1),t.k0s()()()}if(2&i){const e=c.$implicit,a=c.$index,n=t.sdS(1),o=t.XpG();t.HbH(e.labelClass),t.AVh("mdc-tab--active",o.selectedIndex===a),t.Y8G("id",o._getTabLabelId(e,a))("disabled",e.disabled)("fitInkBarToContent",o.fitInkBarToContent),t.BMQ("tabIndex",o._getTabIndex(a))("aria-posinset",a+1)("aria-setsize",o._tabs.length)("aria-controls",o._getTabContentId(a))("aria-selected",o.selectedIndex===a)("aria-label",e.ariaLabel||null)("aria-labelledby",!e.ariaLabel&&e.ariaLabelledby?e.ariaLabelledby:null),t.R7$(3),t.Y8G("matRippleTrigger",n)("matRippleDisabled",e.disabled||o.disableRipple),t.R7$(3),t.vxM(e.templateLabel?6:7)}}function Nt(i,c){1&i&&t.SdG(0)}function Vt(i,c){if(1&i){const e=t.RV6();t.j41(0,"mat-tab-body",13),t.bIt("_onCentered",function(){r.eBV(e);const n=t.XpG();return r.Njj(n._removeTabBodyWrapperHeight())})("_onCentering",function(n){r.eBV(e);const o=t.XpG();return r.Njj(o._setTabBodyWrapperHeight(n))})("_beforeCentering",function(n){r.eBV(e);const o=t.XpG();return r.Njj(o._bodyCentered(n))}),t.k0s()}if(2&i){const e=c.$implicit,a=c.$index,n=t.XpG();t.HbH(e.bodyClass),t.Y8G("id",n._getTabContentId(a))("content",e.content)("position",e.position)("animationDuration",n.animationDuration)("preserveContent",n.preserveContent),t.BMQ("tabindex",null!=n.contentTabIndex&&n.selectedIndex===a?n.contentTabIndex:null)("aria-labelledby",n._getTabLabelId(e,a))("aria-hidden",n.selectedIndex!==a)}}const $t=new r.nKC("MatTabContent");let Xt=(()=>{class i{template=(0,r.WQX)(t.C4Q);constructor(){}static \u0275fac=function(a){return new(a||i)};static \u0275dir=t.FsC({type:i,selectors:[["","matTabContent",""]],features:[t.Jv_([{provide:$t,useExisting:i}])]})}return i})();const Ut=new r.nKC("MatTabLabel"),H=new r.nKC("MAT_TAB");let zt=(()=>{class i extends P.bV{_closestTab=(0,r.WQX)(H,{optional:!0});static \u0275fac=(()=>{let e;return function(n){return(e||(e=t.xGo(i)))(n||i)}})();static \u0275dir=t.FsC({type:i,selectors:[["","mat-tab-label",""],["","matTabLabel",""]],features:[t.Jv_([{provide:Ut,useExisting:i}]),t.Vt3]})}return i})();const Q=new r.nKC("MAT_TAB_GROUP");let W=(()=>{class i{_viewContainerRef=(0,r.WQX)(t.c1b);_closestTabGroup=(0,r.WQX)(Q,{optional:!0});disabled=!1;get templateLabel(){return this._templateLabel}set templateLabel(e){this._setTemplateLabelInput(e)}_templateLabel;_explicitContent=void 0;_implicitContent;textLabel="";ariaLabel;ariaLabelledby;labelClass;bodyClass;id=null;_contentPortal=null;get content(){return this._contentPortal}_stateChanges=new C.B;position=null;origin=null;isActive=!1;constructor(){(0,r.WQX)(Ot._).load(yt._)}ngOnChanges(e){(e.hasOwnProperty("textLabel")||e.hasOwnProperty("disabled"))&&this._stateChanges.next()}ngOnDestroy(){this._stateChanges.complete()}ngOnInit(){this._contentPortal=new P.VA(this._explicitContent||this._implicitContent,this._viewContainerRef)}_setTemplateLabelInput(e){e&&e._closestTab===this&&(this._templateLabel=e)}static \u0275fac=function(a){return new(a||i)};static \u0275cmp=t.VBU({type:i,selectors:[["mat-tab"]],contentQueries:function(a,n,o){if(1&a&&(t.wni(o,zt,5),t.wni(o,Xt,7,t.C4Q)),2&a){let s;t.mGM(s=t.lsd())&&(n.templateLabel=s.first),t.mGM(s=t.lsd())&&(n._explicitContent=s.first)}},viewQuery:function(a,n){if(1&a&&t.GBs(t.C4Q,7),2&a){let o;t.mGM(o=t.lsd())&&(n._implicitContent=o.first)}},hostAttrs:["hidden",""],hostVars:1,hostBindings:function(a,n){2&a&&t.BMQ("id",null)},inputs:{disabled:[2,"disabled","disabled",_.L39],textLabel:[0,"label","textLabel"],ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],labelClass:"labelClass",bodyClass:"bodyClass",id:"id"},exportAs:["matTab"],features:[t.Jv_([{provide:H,useExisting:i}]),t.OA$],ngContentSelectors:A,decls:1,vars:0,template:function(a,n){1&a&&(t.NAR(),t.DNE(0,wt,1,0,"ng-template"))},encapsulation:2})}return i})();const L="mdc-tab-indicator--active",K="mdc-tab-indicator--no-transition";class Yt{_items;_currentItem;constructor(c){this._items=c}hide(){this._items.forEach(c=>c.deactivateInkBar()),this._currentItem=void 0}alignToElement(c){const e=this._items.find(n=>n.elementRef.nativeElement===c),a=this._currentItem;if(e!==a&&(a?.deactivateInkBar(),e)){const n=a?.elementRef.nativeElement.getBoundingClientRect?.();e.activateInkBar(n),this._currentItem=e}}}let Ht=(()=>{class i{_elementRef=(0,r.WQX)(t.aKT);_inkBarElement;_inkBarContentElement;_fitToContent=!1;get fitInkBarToContent(){return this._fitToContent}set fitInkBarToContent(e){this._fitToContent!==e&&(this._fitToContent=e,this._inkBarElement&&this._appendInkBarElement())}activateInkBar(e){const a=this._elementRef.nativeElement;if(!e||!a.getBoundingClientRect||!this._inkBarContentElement)return void a.classList.add(L);const n=a.getBoundingClientRect(),o=e.width/n.width,s=e.left-n.left;a.classList.add(K),this._inkBarContentElement.style.setProperty("transform",`translateX(${s}px) scaleX(${o})`),a.getBoundingClientRect(),a.classList.remove(K),a.classList.add(L),this._inkBarContentElement.style.setProperty("transform","")}deactivateInkBar(){this._elementRef.nativeElement.classList.remove(L)}ngOnInit(){this._createInkBarElement()}ngOnDestroy(){this._inkBarElement?.remove(),this._inkBarElement=this._inkBarContentElement=null}_createInkBarElement(){const e=this._elementRef.nativeElement.ownerDocument||document,a=this._inkBarElement=e.createElement("span"),n=this._inkBarContentElement=e.createElement("span");a.className="mdc-tab-indicator",n.className="mdc-tab-indicator__content mdc-tab-indicator__content--underline",a.appendChild(this._inkBarContentElement),this._appendInkBarElement()}_appendInkBarElement(){(this._fitToContent?this._elementRef.nativeElement.querySelector(".mdc-tab__content"):this._elementRef.nativeElement).appendChild(this._inkBarElement)}static \u0275fac=function(a){return new(a||i)};static \u0275dir=t.FsC({type:i,inputs:{fitInkBarToContent:[2,"fitInkBarToContent","fitInkBarToContent",_.L39]}})}return i})(),Z=(()=>{class i extends Ht{elementRef=(0,r.WQX)(t.aKT);disabled=!1;focus(){this.elementRef.nativeElement.focus()}getOffsetLeft(){return this.elementRef.nativeElement.offsetLeft}getOffsetWidth(){return this.elementRef.nativeElement.offsetWidth}static \u0275fac=(()=>{let e;return function(n){return(e||(e=t.xGo(i)))(n||i)}})();static \u0275dir=t.FsC({type:i,selectors:[["","matTabLabelWrapper",""]],hostVars:3,hostBindings:function(a,n){2&a&&(t.BMQ("aria-disabled",!!n.disabled),t.AVh("mat-mdc-tab-disabled",n.disabled))},inputs:{disabled:[2,"disabled","disabled",_.L39]},features:[t.Vt3]})}return i})();const J={passive:!0};let Zt=(()=>{class i{_elementRef=(0,r.WQX)(t.aKT);_changeDetectorRef=(0,r.WQX)(_.gRc);_viewportRuler=(0,r.WQX)(F.Xj);_dir=(0,r.WQX)(D.D,{optional:!0});_ngZone=(0,r.WQX)(t.SKi);_platform=(0,r.WQX)(j.P);_sharedResizeObserver=(0,r.WQX)(ut.a);_injector=(0,r.WQX)(r.zZn);_renderer=(0,r.WQX)(t.sFG);_animationsDisabled=(0,v._)();_eventCleanups;_scrollDistance=0;_selectedIndexChanged=!1;_destroyed=new C.B;_showPaginationControls=!1;_disableScrollAfter=!0;_disableScrollBefore=!0;_tabLabelCount;_scrollDistanceChanged;_keyManager;_currentTextContent;_stopScrolling=new C.B;disablePagination=!1;get selectedIndex(){return this._selectedIndex}set selectedIndex(e){const a=isNaN(e)?0:e;this._selectedIndex!=a&&(this._selectedIndexChanged=!0,this._selectedIndex=a,this._keyManager&&this._keyManager.updateActiveItem(a))}_selectedIndex=0;selectFocusedIndex=new t.bkB;indexFocused=new t.bkB;constructor(){this._eventCleanups=this._ngZone.runOutsideAngular(()=>[this._renderer.listen(this._elementRef.nativeElement,"mouseleave",()=>this._stopInterval())])}ngAfterViewInit(){this._eventCleanups.push(this._renderer.listen(this._previousPaginator.nativeElement,"touchstart",()=>this._handlePaginatorPress("before"),J),this._renderer.listen(this._nextPaginator.nativeElement,"touchstart",()=>this._handlePaginatorPress("after"),J))}ngAfterContentInit(){const e=this._dir?this._dir.change:(0,gt.of)("ltr"),a=this._sharedResizeObserver.observe(this._elementRef.nativeElement).pipe((0,Ct.B)(32),(0,b.Q)(this._destroyed)),n=this._viewportRuler.change(150).pipe((0,b.Q)(this._destroyed)),o=()=>{this.updatePagination(),this._alignInkBarToSelectedTab()};this._keyManager=new _t.F(this._items).withHorizontalOrientation(this._getLayoutDirection()).withHomeAndEnd().withWrap().skipPredicate(()=>!1),this._keyManager.updateActiveItem(Math.max(this._selectedIndex,0)),(0,t.mal)(o,{injector:this._injector}),(0,S.h)(e,n,a,this._items.changes,this._itemsResized()).pipe((0,b.Q)(this._destroyed)).subscribe(()=>{this._ngZone.run(()=>{Promise.resolve().then(()=>{this._scrollDistance=Math.max(0,Math.min(this._getMaxScrollDistance(),this._scrollDistance)),o()})}),this._keyManager?.withHorizontalOrientation(this._getLayoutDirection())}),this._keyManager.change.subscribe(s=>{this.indexFocused.emit(s),this._setTabFocus(s)})}_itemsResized(){return"function"!=typeof ResizeObserver?bt.w:this._items.changes.pipe((0,B.Z)(this._items),(0,vt.n)(e=>new ht.c(a=>this._ngZone.runOutsideAngular(()=>{const n=new ResizeObserver(o=>a.next(o));return e.forEach(o=>n.observe(o.elementRef.nativeElement)),()=>{n.disconnect()}}))),(0,Pt.i)(1),(0,Mt.p)(e=>e.some(a=>a.contentRect.width>0&&a.contentRect.height>0)))}ngAfterContentChecked(){this._tabLabelCount!=this._items.length&&(this.updatePagination(),this._tabLabelCount=this._items.length,this._changeDetectorRef.markForCheck()),this._selectedIndexChanged&&(this._scrollToLabel(this._selectedIndex),this._checkScrollingControls(),this._alignInkBarToSelectedTab(),this._selectedIndexChanged=!1,this._changeDetectorRef.markForCheck()),this._scrollDistanceChanged&&(this._updateTabScrollPosition(),this._scrollDistanceChanged=!1,this._changeDetectorRef.markForCheck())}ngOnDestroy(){this._eventCleanups.forEach(e=>e()),this._keyManager?.destroy(),this._destroyed.next(),this._destroyed.complete(),this._stopScrolling.complete()}_handleKeydown(e){if(!(0,z.rp)(e))switch(e.keyCode){case R.c:case R.S:if(this.focusIndex!==this.selectedIndex){const a=this._items.get(this.focusIndex);a&&!a.disabled&&(this.selectFocusedIndex.emit(this.focusIndex),this._itemSelected(e))}break;default:this._keyManager?.onKeydown(e)}}_onContentChanges(){const e=this._elementRef.nativeElement.textContent;e!==this._currentTextContent&&(this._currentTextContent=e||"",this._ngZone.run(()=>{this.updatePagination(),this._alignInkBarToSelectedTab(),this._changeDetectorRef.markForCheck()}))}updatePagination(){this._checkPaginationEnabled(),this._checkScrollingControls(),this._updateTabScrollPosition()}get focusIndex(){return this._keyManager?this._keyManager.activeItemIndex:0}set focusIndex(e){!this._isValidIndex(e)||this.focusIndex===e||!this._keyManager||this._keyManager.setActiveItem(e)}_isValidIndex(e){return!this._items||!!this._items.toArray()[e]}_setTabFocus(e){if(this._showPaginationControls&&this._scrollToLabel(e),this._items&&this._items.length){this._items.toArray()[e].focus();const a=this._tabListContainer.nativeElement;a.scrollLeft="ltr"==this._getLayoutDirection()?0:a.scrollWidth-a.offsetWidth}}_getLayoutDirection(){return this._dir&&"rtl"===this._dir.value?"rtl":"ltr"}_updateTabScrollPosition(){if(this.disablePagination)return;const e=this.scrollDistance,a="ltr"===this._getLayoutDirection()?-e:e;this._tabList.nativeElement.style.transform=`translateX(${Math.round(a)}px)`,(this._platform.TRIDENT||this._platform.EDGE)&&(this._tabListContainer.nativeElement.scrollLeft=0)}get scrollDistance(){return this._scrollDistance}set scrollDistance(e){this._scrollTo(e)}_scrollHeader(e){return this._scrollTo(this._scrollDistance+("before"==e?-1:1)*this._tabListContainer.nativeElement.offsetWidth/3)}_handlePaginatorClick(e){this._stopInterval(),this._scrollHeader(e)}_scrollToLabel(e){if(this.disablePagination)return;const a=this._items?this._items.toArray()[e]:null;if(!a)return;const n=this._tabListContainer.nativeElement.offsetWidth,{offsetLeft:o,offsetWidth:s}=a.elementRef.nativeElement;let m,g;"ltr"==this._getLayoutDirection()?(m=o,g=m+s):(g=this._tabListInner.nativeElement.offsetWidth-o,m=g-s);const O=this.scrollDistance,lt=this.scrollDistance+n;m<O?this.scrollDistance-=O-m:g>lt&&(this.scrollDistance+=Math.min(g-lt,m-O))}_checkPaginationEnabled(){if(this.disablePagination)this._showPaginationControls=!1;else{const n=this._tabListInner.nativeElement.scrollWidth-this._elementRef.nativeElement.offsetWidth>=5;n||(this.scrollDistance=0),n!==this._showPaginationControls&&(this._showPaginationControls=n,this._changeDetectorRef.markForCheck())}}_checkScrollingControls(){this.disablePagination?this._disableScrollAfter=this._disableScrollBefore=!0:(this._disableScrollBefore=0==this.scrollDistance,this._disableScrollAfter=this.scrollDistance==this._getMaxScrollDistance(),this._changeDetectorRef.markForCheck())}_getMaxScrollDistance(){return this._tabListInner.nativeElement.scrollWidth-this._tabListContainer.nativeElement.offsetWidth||0}_alignInkBarToSelectedTab(){const e=this._items&&this._items.length?this._items.toArray()[this.selectedIndex]:null,a=e?e.elementRef.nativeElement:null;a?this._inkBar.alignToElement(a):this._inkBar.hide()}_stopInterval(){this._stopScrolling.next()}_handlePaginatorPress(e,a){a&&null!=a.button&&0!==a.button||(this._stopInterval(),(0,ft.O)(650,100).pipe((0,b.Q)((0,S.h)(this._stopScrolling,this._destroyed))).subscribe(()=>{const{maxScrollDistance:n,distance:o}=this._scrollHeader(e);(0===o||o>=n)&&this._stopInterval()}))}_scrollTo(e){if(this.disablePagination)return{maxScrollDistance:0,distance:0};const a=this._getMaxScrollDistance();return this._scrollDistance=Math.max(0,Math.min(a,e)),this._scrollDistanceChanged=!0,this._checkScrollingControls(),{maxScrollDistance:a,distance:this._scrollDistance}}static \u0275fac=function(a){return new(a||i)};static \u0275dir=t.FsC({type:i,inputs:{disablePagination:[2,"disablePagination","disablePagination",_.L39],selectedIndex:[2,"selectedIndex","selectedIndex",_.Udg]},outputs:{selectFocusedIndex:"selectFocusedIndex",indexFocused:"indexFocused"}})}return i})(),Jt=(()=>{class i extends Zt{_items;_tabListContainer;_tabList;_tabListInner;_nextPaginator;_previousPaginator;_inkBar;ariaLabel;ariaLabelledby;disableRipple=!1;ngAfterContentInit(){this._inkBar=new Yt(this._items),super.ngAfterContentInit()}_itemSelected(e){e.preventDefault()}static \u0275fac=(()=>{let e;return function(n){return(e||(e=t.xGo(i)))(n||i)}})();static \u0275cmp=t.VBU({type:i,selectors:[["mat-tab-header"]],contentQueries:function(a,n,o){if(1&a&&t.wni(o,Z,4),2&a){let s;t.mGM(s=t.lsd())&&(n._items=s)}},viewQuery:function(a,n){if(1&a&&(t.GBs(xt,7),t.GBs(Tt,7),t.GBs(Ft,7),t.GBs(It,5),t.GBs(Et,5)),2&a){let o;t.mGM(o=t.lsd())&&(n._tabListContainer=o.first),t.mGM(o=t.lsd())&&(n._tabList=o.first),t.mGM(o=t.lsd())&&(n._tabListInner=o.first),t.mGM(o=t.lsd())&&(n._nextPaginator=o.first),t.mGM(o=t.lsd())&&(n._previousPaginator=o.first)}},hostAttrs:[1,"mat-mdc-tab-header"],hostVars:4,hostBindings:function(a,n){2&a&&t.AVh("mat-mdc-tab-header-pagination-controls-enabled",n._showPaginationControls)("mat-mdc-tab-header-rtl","rtl"==n._getLayoutDirection())},inputs:{ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],disableRipple:[2,"disableRipple","disableRipple",_.L39]},features:[t.Vt3],ngContentSelectors:A,decls:13,vars:10,consts:[["previousPaginator",""],["tabListContainer",""],["tabList",""],["tabListInner",""],["nextPaginator",""],["mat-ripple","",1,"mat-mdc-tab-header-pagination","mat-mdc-tab-header-pagination-before",3,"click","mousedown","touchend","matRippleDisabled"],[1,"mat-mdc-tab-header-pagination-chevron"],[1,"mat-mdc-tab-label-container",3,"keydown"],["role","tablist",1,"mat-mdc-tab-list",3,"cdkObserveContent"],[1,"mat-mdc-tab-labels"],["mat-ripple","",1,"mat-mdc-tab-header-pagination","mat-mdc-tab-header-pagination-after",3,"mousedown","click","touchend","matRippleDisabled"]],template:function(a,n){if(1&a){const o=t.RV6();t.NAR(),t.j41(0,"div",5,0),t.bIt("click",function(){return r.eBV(o),r.Njj(n._handlePaginatorClick("before"))})("mousedown",function(m){return r.eBV(o),r.Njj(n._handlePaginatorPress("before",m))})("touchend",function(){return r.eBV(o),r.Njj(n._stopInterval())}),t.nrm(2,"div",6),t.k0s(),t.j41(3,"div",7,1),t.bIt("keydown",function(m){return r.eBV(o),r.Njj(n._handleKeydown(m))}),t.j41(5,"div",8,2),t.bIt("cdkObserveContent",function(){return r.eBV(o),r.Njj(n._onContentChanges())}),t.j41(7,"div",9,3),t.SdG(9),t.k0s()()(),t.j41(10,"div",10,4),t.bIt("mousedown",function(m){return r.eBV(o),r.Njj(n._handlePaginatorPress("after",m))})("click",function(){return r.eBV(o),r.Njj(n._handlePaginatorClick("after"))})("touchend",function(){return r.eBV(o),r.Njj(n._stopInterval())}),t.nrm(12,"div",6),t.k0s()}2&a&&(t.AVh("mat-mdc-tab-header-pagination-disabled",n._disableScrollBefore),t.Y8G("matRippleDisabled",n._disableScrollBefore||n.disableRipple),t.R7$(3),t.AVh("_mat-animation-noopable",n._animationsDisabled),t.R7$(2),t.BMQ("aria-label",n.ariaLabel||null)("aria-labelledby",n.ariaLabelledby||null),t.R7$(5),t.AVh("mat-mdc-tab-header-pagination-disabled",n._disableScrollAfter),t.Y8G("matRippleDisabled",n._disableScrollAfter||n.disableRipple))},dependencies:[Y.M,kt.Wv],styles:[".mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-divider-height, 1px);border-bottom-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-divider-height, 1px);border-top-color:var(--mat-tab-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-labels,.mat-mdc-tab-labels.cdk-drop-list{min-height:var(--mat-tab-container-height, 48px)}.mat-mdc-tab::before{margin:5px}@media(forced-colors: active){.mat-mdc-tab[aria-disabled=true]{color:GrayText}}\n"],encapsulation:2})}return i})();const qt=new r.nKC("MAT_TABS_CONFIG");let q=(()=>{class i extends P.I3{_host=(0,r.WQX)(G);_centeringSub=f.yU.EMPTY;_leavingSub=f.yU.EMPTY;constructor(){super()}ngOnInit(){super.ngOnInit(),this._centeringSub=this._host._beforeCentering.pipe((0,B.Z)(this._host._isCenterPosition())).subscribe(e=>{this._host._content&&e&&!this.hasAttached()&&this.attach(this._host._content)}),this._leavingSub=this._host._afterLeavingCenter.subscribe(()=>{this._host.preserveContent||this.detach()})}ngOnDestroy(){super.ngOnDestroy(),this._centeringSub.unsubscribe(),this._leavingSub.unsubscribe()}static \u0275fac=function(a){return new(a||i)};static \u0275dir=t.FsC({type:i,selectors:[["","matTabBodyHost",""]],features:[t.Vt3]})}return i})(),G=(()=>{class i{_elementRef=(0,r.WQX)(t.aKT);_dir=(0,r.WQX)(D.D,{optional:!0});_ngZone=(0,r.WQX)(t.SKi);_injector=(0,r.WQX)(r.zZn);_renderer=(0,r.WQX)(t.sFG);_diAnimationsDisabled=(0,v._)();_eventCleanups;_initialized;_fallbackTimer;_positionIndex;_dirChangeSubscription=f.yU.EMPTY;_position;_previousPosition;_onCentering=new t.bkB;_beforeCentering=new t.bkB;_afterLeavingCenter=new t.bkB;_onCentered=new t.bkB(!0);_portalHost;_contentElement;_content;animationDuration="500ms";preserveContent=!1;set position(e){this._positionIndex=e,this._computePositionAnimationState()}constructor(){if(this._dir){const e=(0,r.WQX)(_.gRc);this._dirChangeSubscription=this._dir.change.subscribe(a=>{this._computePositionAnimationState(a),e.markForCheck()})}}ngOnInit(){this._bindTransitionEvents(),"center"===this._position&&(this._setActiveClass(!0),(0,t.mal)(()=>this._onCentering.emit(this._elementRef.nativeElement.clientHeight),{injector:this._injector})),this._initialized=!0}ngOnDestroy(){clearTimeout(this._fallbackTimer),this._eventCleanups?.forEach(e=>e()),this._dirChangeSubscription.unsubscribe()}_bindTransitionEvents(){this._ngZone.runOutsideAngular(()=>{const e=this._elementRef.nativeElement,a=n=>{n.target===this._contentElement?.nativeElement&&(this._elementRef.nativeElement.classList.remove("mat-tab-body-animating"),"transitionend"===n.type&&this._transitionDone())};this._eventCleanups=[this._renderer.listen(e,"transitionstart",n=>{n.target===this._contentElement?.nativeElement&&(this._elementRef.nativeElement.classList.add("mat-tab-body-animating"),this._transitionStarted())}),this._renderer.listen(e,"transitionend",a),this._renderer.listen(e,"transitioncancel",a)]})}_transitionStarted(){clearTimeout(this._fallbackTimer);const e="center"===this._position;this._beforeCentering.emit(e),e&&this._onCentering.emit(this._elementRef.nativeElement.clientHeight)}_transitionDone(){"center"===this._position?this._onCentered.emit():"center"===this._previousPosition&&this._afterLeavingCenter.emit()}_setActiveClass(e){this._elementRef.nativeElement.classList.toggle("mat-mdc-tab-body-active",e)}_getLayoutDirection(){return this._dir&&"rtl"===this._dir.value?"rtl":"ltr"}_isCenterPosition(){return 0===this._positionIndex}_computePositionAnimationState(e=this._getLayoutDirection()){this._previousPosition=this._position,this._position=this._positionIndex<0?"ltr"==e?"left":"right":this._positionIndex>0?"ltr"==e?"right":"left":"center",this._animationsDisabled()?this._simulateTransitionEvents():this._initialized&&("center"===this._position||"center"===this._previousPosition)&&(clearTimeout(this._fallbackTimer),this._fallbackTimer=this._ngZone.runOutsideAngular(()=>setTimeout(()=>this._simulateTransitionEvents(),100)))}_simulateTransitionEvents(){this._transitionStarted(),(0,t.mal)(()=>this._transitionDone(),{injector:this._injector})}_animationsDisabled(){return this._diAnimationsDisabled||"0ms"===this.animationDuration||"0s"===this.animationDuration}static \u0275fac=function(a){return new(a||i)};static \u0275cmp=t.VBU({type:i,selectors:[["mat-tab-body"]],viewQuery:function(a,n){if(1&a&&(t.GBs(q,5),t.GBs(Dt,5)),2&a){let o;t.mGM(o=t.lsd())&&(n._portalHost=o.first),t.mGM(o=t.lsd())&&(n._contentElement=o.first)}},hostAttrs:[1,"mat-mdc-tab-body"],hostVars:1,hostBindings:function(a,n){2&a&&t.BMQ("inert","center"===n._position?null:"")},inputs:{_content:[0,"content","_content"],animationDuration:"animationDuration",preserveContent:"preserveContent",position:"position"},outputs:{_onCentering:"_onCentering",_beforeCentering:"_beforeCentering",_onCentered:"_onCentered"},decls:3,vars:6,consts:[["content",""],["cdkScrollable","",1,"mat-mdc-tab-body-content"],["matTabBodyHost",""]],template:function(a,n){1&a&&(t.j41(0,"div",1,0),t.DNE(2,Rt,0,0,"ng-template",2),t.k0s()),2&a&&t.AVh("mat-tab-body-content-left","left"===n._position)("mat-tab-body-content-right","right"===n._position)("mat-tab-body-content-can-animate","center"===n._position||"center"===n._previousPosition)},dependencies:[q,F.uv],styles:[".mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto;transform:none;visibility:hidden}.mat-tab-body-animating>.mat-mdc-tab-body-content,.mat-mdc-tab-body-active>.mat-mdc-tab-body-content{visibility:visible}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-tab-body-content-can-animate{transition:transform var(--mat-tab-animation-duration) 1ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable .mat-tab-body-content-can-animate{transition:none}.mat-tab-body-content-left{transform:translate3d(-100%, 0, 0)}.mat-tab-body-content-right{transform:translate3d(100%, 0, 0)}\n"],encapsulation:2})}return i})(),te=(()=>{class i{_elementRef=(0,r.WQX)(t.aKT);_changeDetectorRef=(0,r.WQX)(_.gRc);_ngZone=(0,r.WQX)(t.SKi);_tabsSubscription=f.yU.EMPTY;_tabLabelSubscription=f.yU.EMPTY;_tabBodySubscription=f.yU.EMPTY;_diAnimationsDisabled=(0,v._)();_allTabs;_tabBodies;_tabBodyWrapper;_tabHeader;_tabs=new t.rOR;_indexToSelect=0;_lastFocusedTabIndex=null;_tabBodyWrapperHeight=0;color;get fitInkBarToContent(){return this._fitInkBarToContent}set fitInkBarToContent(e){this._fitInkBarToContent=e,this._changeDetectorRef.markForCheck()}_fitInkBarToContent=!1;stretchTabs=!0;alignTabs=null;dynamicHeight=!1;get selectedIndex(){return this._selectedIndex}set selectedIndex(e){this._indexToSelect=isNaN(e)?null:e}_selectedIndex=null;headerPosition="above";get animationDuration(){return this._animationDuration}set animationDuration(e){const a=e+"";this._animationDuration=/^\d+$/.test(a)?e+"ms":a}_animationDuration;get contentTabIndex(){return this._contentTabIndex}set contentTabIndex(e){this._contentTabIndex=isNaN(e)?null:e}_contentTabIndex;disablePagination=!1;disableRipple=!1;preserveContent=!1;get backgroundColor(){return this._backgroundColor}set backgroundColor(e){const a=this._elementRef.nativeElement.classList;a.remove("mat-tabs-with-background",`mat-background-${this.backgroundColor}`),e&&a.add("mat-tabs-with-background",`mat-background-${e}`),this._backgroundColor=e}_backgroundColor;ariaLabel;ariaLabelledby;selectedIndexChange=new t.bkB;focusChange=new t.bkB;animationDone=new t.bkB;selectedTabChange=new t.bkB(!0);_groupId;_isServer=!(0,r.WQX)(j.P).isBrowser;constructor(){const e=(0,r.WQX)(qt,{optional:!0});this._groupId=(0,r.WQX)(pt._).getId("mat-tab-group-"),this.animationDuration=e&&e.animationDuration?e.animationDuration:"500ms",this.disablePagination=!(!e||null==e.disablePagination)&&e.disablePagination,this.dynamicHeight=!(!e||null==e.dynamicHeight)&&e.dynamicHeight,null!=e?.contentTabIndex&&(this.contentTabIndex=e.contentTabIndex),this.preserveContent=!!e?.preserveContent,this.fitInkBarToContent=!(!e||null==e.fitInkBarToContent)&&e.fitInkBarToContent,this.stretchTabs=!e||null==e.stretchTabs||e.stretchTabs,this.alignTabs=e&&null!=e.alignTabs?e.alignTabs:null}ngAfterContentChecked(){const e=this._indexToSelect=this._clampTabIndex(this._indexToSelect);if(this._selectedIndex!=e){const a=null==this._selectedIndex;if(!a){this.selectedTabChange.emit(this._createChangeEvent(e));const n=this._tabBodyWrapper.nativeElement;n.style.minHeight=n.clientHeight+"px"}Promise.resolve().then(()=>{this._tabs.forEach((n,o)=>n.isActive=o===e),a||(this.selectedIndexChange.emit(e),this._tabBodyWrapper.nativeElement.style.minHeight="")})}this._tabs.forEach((a,n)=>{a.position=n-e,null!=this._selectedIndex&&0==a.position&&!a.origin&&(a.origin=e-this._selectedIndex)}),this._selectedIndex!==e&&(this._selectedIndex=e,this._lastFocusedTabIndex=null,this._changeDetectorRef.markForCheck())}ngAfterContentInit(){this._subscribeToAllTabChanges(),this._subscribeToTabLabels(),this._tabsSubscription=this._tabs.changes.subscribe(()=>{const e=this._clampTabIndex(this._indexToSelect);if(e===this._selectedIndex){const a=this._tabs.toArray();let n;for(let o=0;o<a.length;o++)if(a[o].isActive){this._indexToSelect=this._selectedIndex=o,this._lastFocusedTabIndex=null,n=a[o];break}!n&&a[e]&&Promise.resolve().then(()=>{a[e].isActive=!0,this.selectedTabChange.emit(this._createChangeEvent(e))})}this._changeDetectorRef.markForCheck()})}ngAfterViewInit(){this._tabBodySubscription=this._tabBodies.changes.subscribe(()=>this._bodyCentered(!0))}_subscribeToAllTabChanges(){this._allTabs.changes.pipe((0,B.Z)(this._allTabs)).subscribe(e=>{this._tabs.reset(e.filter(a=>a._closestTabGroup===this||!a._closestTabGroup)),this._tabs.notifyOnChanges()})}ngOnDestroy(){this._tabs.destroy(),this._tabsSubscription.unsubscribe(),this._tabLabelSubscription.unsubscribe(),this._tabBodySubscription.unsubscribe()}realignInkBar(){this._tabHeader&&this._tabHeader._alignInkBarToSelectedTab()}updatePagination(){this._tabHeader&&this._tabHeader.updatePagination()}focusTab(e){const a=this._tabHeader;a&&(a.focusIndex=e)}_focusChanged(e){this._lastFocusedTabIndex=e,this.focusChange.emit(this._createChangeEvent(e))}_createChangeEvent(e){const a=new ee;return a.index=e,this._tabs&&this._tabs.length&&(a.tab=this._tabs.toArray()[e]),a}_subscribeToTabLabels(){this._tabLabelSubscription&&this._tabLabelSubscription.unsubscribe(),this._tabLabelSubscription=(0,S.h)(...this._tabs.map(e=>e._stateChanges)).subscribe(()=>this._changeDetectorRef.markForCheck())}_clampTabIndex(e){return Math.min(this._tabs.length-1,Math.max(e||0,0))}_getTabLabelId(e,a){return e.id||`${this._groupId}-label-${a}`}_getTabContentId(e){return`${this._groupId}-content-${e}`}_setTabBodyWrapperHeight(e){if(!this.dynamicHeight||!this._tabBodyWrapperHeight)return void(this._tabBodyWrapperHeight=e);const a=this._tabBodyWrapper.nativeElement;a.style.height=this._tabBodyWrapperHeight+"px",this._tabBodyWrapper.nativeElement.offsetHeight&&(a.style.height=e+"px")}_removeTabBodyWrapperHeight(){const e=this._tabBodyWrapper.nativeElement;this._tabBodyWrapperHeight=e.clientHeight,e.style.height="",this._ngZone.run(()=>this.animationDone.emit())}_handleClick(e,a,n){a.focusIndex=n,e.disabled||(this.selectedIndex=n)}_getTabIndex(e){return e===(this._lastFocusedTabIndex??this.selectedIndex)?0:-1}_tabFocusChanged(e,a){e&&"mouse"!==e&&"touch"!==e&&(this._tabHeader.focusIndex=a)}_bodyCentered(e){e&&this._tabBodies?.forEach((a,n)=>a._setActiveClass(n===this._selectedIndex))}_animationsDisabled(){return this._diAnimationsDisabled||"0"===this.animationDuration||"0ms"===this.animationDuration}static \u0275fac=function(a){return new(a||i)};static \u0275cmp=t.VBU({type:i,selectors:[["mat-tab-group"]],contentQueries:function(a,n,o){if(1&a&&t.wni(o,W,5),2&a){let s;t.mGM(s=t.lsd())&&(n._allTabs=s)}},viewQuery:function(a,n){if(1&a&&(t.GBs(jt,5),t.GBs(St,5),t.GBs(G,5)),2&a){let o;t.mGM(o=t.lsd())&&(n._tabBodyWrapper=o.first),t.mGM(o=t.lsd())&&(n._tabHeader=o.first),t.mGM(o=t.lsd())&&(n._tabBodies=o)}},hostAttrs:[1,"mat-mdc-tab-group"],hostVars:11,hostBindings:function(a,n){2&a&&(t.BMQ("mat-align-tabs",n.alignTabs),t.HbH("mat-"+(n.color||"primary")),t.xc7("--mat-tab-animation-duration",n.animationDuration),t.AVh("mat-mdc-tab-group-dynamic-height",n.dynamicHeight)("mat-mdc-tab-group-inverted-header","below"===n.headerPosition)("mat-mdc-tab-group-stretch-tabs",n.stretchTabs))},inputs:{color:"color",fitInkBarToContent:[2,"fitInkBarToContent","fitInkBarToContent",_.L39],stretchTabs:[2,"mat-stretch-tabs","stretchTabs",_.L39],alignTabs:[0,"mat-align-tabs","alignTabs"],dynamicHeight:[2,"dynamicHeight","dynamicHeight",_.L39],selectedIndex:[2,"selectedIndex","selectedIndex",_.Udg],headerPosition:"headerPosition",animationDuration:"animationDuration",contentTabIndex:[2,"contentTabIndex","contentTabIndex",_.Udg],disablePagination:[2,"disablePagination","disablePagination",_.L39],disableRipple:[2,"disableRipple","disableRipple",_.L39],preserveContent:[2,"preserveContent","preserveContent",_.L39],backgroundColor:"backgroundColor",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"]},outputs:{selectedIndexChange:"selectedIndexChange",focusChange:"focusChange",animationDone:"animationDone",selectedTabChange:"selectedTabChange"},exportAs:["matTabGroup"],features:[t.Jv_([{provide:Q,useExisting:i}])],ngContentSelectors:A,decls:9,vars:8,consts:[["tabHeader",""],["tabBodyWrapper",""],["tabNode",""],[3,"indexFocused","selectFocusedIndex","selectedIndex","disableRipple","disablePagination","aria-label","aria-labelledby"],["role","tab","matTabLabelWrapper","","cdkMonitorElementFocus","",1,"mdc-tab","mat-mdc-tab","mat-focus-indicator",3,"id","mdc-tab--active","class","disabled","fitInkBarToContent"],[1,"mat-mdc-tab-body-wrapper"],["role","tabpanel",3,"id","class","content","position","animationDuration","preserveContent"],["role","tab","matTabLabelWrapper","","cdkMonitorElementFocus","",1,"mdc-tab","mat-mdc-tab","mat-focus-indicator",3,"click","cdkFocusChange","id","disabled","fitInkBarToContent"],[1,"mdc-tab__ripple"],["mat-ripple","",1,"mat-mdc-tab-ripple",3,"matRippleTrigger","matRippleDisabled"],[1,"mdc-tab__content"],[1,"mdc-tab__text-label"],[3,"cdkPortalOutlet"],["role","tabpanel",3,"_onCentered","_onCentering","_beforeCentering","id","content","position","animationDuration","preserveContent"]],template:function(a,n){if(1&a){const o=t.RV6();t.NAR(),t.j41(0,"mat-tab-header",3,0),t.bIt("indexFocused",function(m){return r.eBV(o),r.Njj(n._focusChanged(m))})("selectFocusedIndex",function(m){return r.eBV(o),r.Njj(n.selectedIndex=m)}),t.Z7z(2,Gt,8,17,"div",4,t.fX1),t.k0s(),t.nVh(4,Nt,1,0),t.j41(5,"div",5,1),t.Z7z(7,Vt,1,10,"mat-tab-body",6,t.fX1),t.k0s()}2&a&&(t.Y8G("selectedIndex",n.selectedIndex||0)("disableRipple",n.disableRipple)("disablePagination",n.disablePagination)("aria-label",n.ariaLabel)("aria-labelledby",n.ariaLabelledby),t.R7$(2),t.Dyx(n._tabs),t.R7$(2),t.vxM(n._isServer?4:-1),t.R7$(),t.AVh("_mat-animation-noopable",n._animationsDisabled()),t.R7$(2),t.Dyx(n._tabs))},dependencies:[Jt,Z,U.C,Y.M,P.I3,G],styles:['.mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mat-tab-container-height, 48px);font-family:var(--mat-tab-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-tab-active-indicator-height, 2px);border-radius:var(--mat-tab-active-indicator-shape, 0)}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-disabled-ripple-color, var(--mat-sys-on-surface-variant))}.mat-mdc-tab .mdc-tab__ripple::before{content:"";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}\n'],encapsulation:2})}return i})();class ee{index;tab}let ne=(()=>{class i{static \u0275fac=function(a){return new(a||i)};static \u0275mod=t.$C({type:i});static \u0275inj=r.G2t({imports:[I.M,I.M]})}return i})();var N=l(1997),tt=l(2506),V=l(357),ae=l(5178),ie=l(1497),M=l(282),et=l(4085),nt=l(9355),oe=l(5955);const re=["tooltip"],it=new r.nKC("mat-tooltip-scroll-strategy",{providedIn:"root",factory:()=>{const i=(0,r.WQX)(r.zZn);return()=>(0,M.o)(i,{scrollThrottle:20})}}),ce={provide:it,deps:[],useFactory:function se(i){const c=(0,r.WQX)(r.zZn);return()=>(0,M.o)(c,{scrollThrottle:20})}},de=new r.nKC("mat-tooltip-default-options",{providedIn:"root",factory:function le(){return{showDelay:0,hideDelay:0,touchendHideDelay:1500}}}),ot="tooltip-panel",rt=(0,oe.n)({passive:!0});let ge=(()=>{class i{_elementRef=(0,r.WQX)(t.aKT);_ngZone=(0,r.WQX)(t.SKi);_platform=(0,r.WQX)(j.P);_ariaDescriber=(0,r.WQX)(ae.vr);_focusMonitor=(0,r.WQX)(U.F);_dir=(0,r.WQX)(D.D);_injector=(0,r.WQX)(r.zZn);_viewContainerRef=(0,r.WQX)(t.c1b);_animationsDisabled=(0,v._)();_defaultOptions=(0,r.WQX)(de,{optional:!0});_overlayRef;_tooltipInstance;_overlayPanelClass;_portal;_position="below";_positionAtOrigin=!1;_disabled=!1;_tooltipClass;_viewInitialized=!1;_pointerExitEventsInitialized=!1;_tooltipComponent=be;_viewportMargin=8;_currentPosition;_cssClassPrefix="mat-mdc";_ariaDescriptionPending;_dirSubscribed=!1;get position(){return this._position}set position(e){e!==this._position&&(this._position=e,this._overlayRef&&(this._updatePosition(this._overlayRef),this._tooltipInstance?.show(0),this._overlayRef.updatePosition()))}get positionAtOrigin(){return this._positionAtOrigin}set positionAtOrigin(e){this._positionAtOrigin=(0,et.he)(e),this._detach(),this._overlayRef=null}get disabled(){return this._disabled}set disabled(e){const a=(0,et.he)(e);this._disabled!==a&&(this._disabled=a,a?this.hide(0):this._setupPointerEnterEventsIfNeeded(),this._syncAriaDescription(this.message))}get showDelay(){return this._showDelay}set showDelay(e){this._showDelay=(0,nt.c)(e)}_showDelay;get hideDelay(){return this._hideDelay}set hideDelay(e){this._hideDelay=(0,nt.c)(e),this._tooltipInstance&&(this._tooltipInstance._mouseLeaveHideDelay=this._hideDelay)}_hideDelay;touchGestures="auto";get message(){return this._message}set message(e){const a=this._message;this._message=null!=e?String(e).trim():"",!this._message&&this._isTooltipVisible()?this.hide(0):(this._setupPointerEnterEventsIfNeeded(),this._updateTooltipMessage()),this._syncAriaDescription(a)}_message="";get tooltipClass(){return this._tooltipClass}set tooltipClass(e){this._tooltipClass=e,this._tooltipInstance&&this._setTooltipClass(this._tooltipClass)}_passiveListeners=[];_touchstartTimeout=null;_destroyed=new C.B;_isDestroyed=!1;constructor(){const e=this._defaultOptions;e&&(this._showDelay=e.showDelay,this._hideDelay=e.hideDelay,e.position&&(this.position=e.position),e.positionAtOrigin&&(this.positionAtOrigin=e.positionAtOrigin),e.touchGestures&&(this.touchGestures=e.touchGestures),e.tooltipClass&&(this.tooltipClass=e.tooltipClass)),this._viewportMargin=8}ngAfterViewInit(){this._viewInitialized=!0,this._setupPointerEnterEventsIfNeeded(),this._focusMonitor.monitor(this._elementRef).pipe((0,b.Q)(this._destroyed)).subscribe(e=>{e?"keyboard"===e&&this._ngZone.run(()=>this.show()):this._ngZone.run(()=>this.hide(0))})}ngOnDestroy(){const e=this._elementRef.nativeElement;this._touchstartTimeout&&clearTimeout(this._touchstartTimeout),this._overlayRef&&(this._overlayRef.dispose(),this._tooltipInstance=null),this._passiveListeners.forEach(([a,n])=>{e.removeEventListener(a,n,rt)}),this._passiveListeners.length=0,this._destroyed.next(),this._destroyed.complete(),this._isDestroyed=!0,this._ariaDescriber.removeDescription(e,this.message,"tooltip"),this._focusMonitor.stopMonitoring(e)}show(e=this.showDelay,a){if(this.disabled||!this.message||this._isTooltipVisible())return void this._tooltipInstance?._cancelPendingAnimations();const n=this._createOverlay(a);this._detach(),this._portal=this._portal||new P.A8(this._tooltipComponent,this._viewContainerRef);const o=this._tooltipInstance=n.attach(this._portal).instance;o._triggerElement=this._elementRef.nativeElement,o._mouseLeaveHideDelay=this._hideDelay,o.afterHidden().pipe((0,b.Q)(this._destroyed)).subscribe(()=>this._detach()),this._setTooltipClass(this._tooltipClass),this._updateTooltipMessage(),o.show(e)}hide(e=this.hideDelay){const a=this._tooltipInstance;a&&(a.isVisible()?a.hide(e):(a._cancelPendingAnimations(),this._detach()))}toggle(e){this._isTooltipVisible()?this.hide():this.show(void 0,e)}_isTooltipVisible(){return!!this._tooltipInstance&&this._tooltipInstance.isVisible()}_createOverlay(e){if(this._overlayRef){const s=this._overlayRef.getConfig().positionStrategy;if((!this.positionAtOrigin||!e)&&s._origin instanceof t.aKT)return this._overlayRef;this._detach()}const a=this._injector.get(F.R).getAncestorScrollContainers(this._elementRef),n=`${this._cssClassPrefix}-${ot}`,o=(0,M.h)(this._injector,this.positionAtOrigin&&e||this._elementRef).withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`).withFlexibleDimensions(!1).withViewportMargin(this._viewportMargin).withScrollableContainers(a);return o.positionChanges.pipe((0,b.Q)(this._destroyed)).subscribe(s=>{this._updateCurrentPositionClass(s.connectionPair),this._tooltipInstance&&s.scrollableViewProperties.isOverlayClipped&&this._tooltipInstance.isVisible()&&this._ngZone.run(()=>this.hide(0))}),this._overlayRef=(0,M.c)(this._injector,{direction:this._dir,positionStrategy:o,panelClass:this._overlayPanelClass?[...this._overlayPanelClass,n]:n,scrollStrategy:this._injector.get(it)(),disableAnimations:this._animationsDisabled}),this._updatePosition(this._overlayRef),this._overlayRef.detachments().pipe((0,b.Q)(this._destroyed)).subscribe(()=>this._detach()),this._overlayRef.outsidePointerEvents().pipe((0,b.Q)(this._destroyed)).subscribe(()=>this._tooltipInstance?._handleBodyInteraction()),this._overlayRef.keydownEvents().pipe((0,b.Q)(this._destroyed)).subscribe(s=>{this._isTooltipVisible()&&s.keyCode===R.g&&!(0,z.rp)(s)&&(s.preventDefault(),s.stopPropagation(),this._ngZone.run(()=>this.hide(0)))}),this._defaultOptions?.disableTooltipInteractivity&&this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`),this._dirSubscribed||(this._dirSubscribed=!0,this._dir.change.pipe((0,b.Q)(this._destroyed)).subscribe(()=>{this._overlayRef&&this._updatePosition(this._overlayRef)})),this._overlayRef}_detach(){this._overlayRef&&this._overlayRef.hasAttached()&&this._overlayRef.detach(),this._tooltipInstance=null}_updatePosition(e){const a=e.getConfig().positionStrategy,n=this._getOrigin(),o=this._getOverlayPosition();a.withPositions([this._addOffset({...n.main,...o.main}),this._addOffset({...n.fallback,...o.fallback})])}_addOffset(e){const n=!this._dir||"ltr"==this._dir.value;return"top"===e.originY?e.offsetY=-8:"bottom"===e.originY?e.offsetY=8:"start"===e.originX?e.offsetX=n?-8:8:"end"===e.originX&&(e.offsetX=n?8:-8),e}_getOrigin(){const e=!this._dir||"ltr"==this._dir.value,a=this.position;let n;"above"==a||"below"==a?n={originX:"center",originY:"above"==a?"top":"bottom"}:"before"==a||"left"==a&&e||"right"==a&&!e?n={originX:"start",originY:"center"}:("after"==a||"right"==a&&e||"left"==a&&!e)&&(n={originX:"end",originY:"center"});const{x:o,y:s}=this._invertPosition(n.originX,n.originY);return{main:n,fallback:{originX:o,originY:s}}}_getOverlayPosition(){const e=!this._dir||"ltr"==this._dir.value,a=this.position;let n;"above"==a?n={overlayX:"center",overlayY:"bottom"}:"below"==a?n={overlayX:"center",overlayY:"top"}:"before"==a||"left"==a&&e||"right"==a&&!e?n={overlayX:"end",overlayY:"center"}:("after"==a||"right"==a&&e||"left"==a&&!e)&&(n={overlayX:"start",overlayY:"center"});const{x:o,y:s}=this._invertPosition(n.overlayX,n.overlayY);return{main:n,fallback:{overlayX:o,overlayY:s}}}_updateTooltipMessage(){this._tooltipInstance&&(this._tooltipInstance.message=this.message,this._tooltipInstance._markForCheck(),(0,t.mal)(()=>{this._tooltipInstance&&this._overlayRef.updatePosition()},{injector:this._injector}))}_setTooltipClass(e){this._tooltipInstance&&(this._tooltipInstance.tooltipClass=e,this._tooltipInstance._markForCheck())}_invertPosition(e,a){return"above"===this.position||"below"===this.position?"top"===a?a="bottom":"bottom"===a&&(a="top"):"end"===e?e="start":"start"===e&&(e="end"),{x:e,y:a}}_updateCurrentPositionClass(e){const{overlayY:a,originX:n,originY:o}=e;let s;if(s="center"===a?this._dir&&"rtl"===this._dir.value?"end"===n?"left":"right":"start"===n?"left":"right":"bottom"===a&&"top"===o?"above":"below",s!==this._currentPosition){const m=this._overlayRef;if(m){const g=`${this._cssClassPrefix}-${ot}-`;m.removePanelClass(g+this._currentPosition),m.addPanelClass(g+s)}this._currentPosition=s}}_setupPointerEnterEventsIfNeeded(){this._disabled||!this.message||!this._viewInitialized||this._passiveListeners.length||(this._platformSupportsMouseEvents()?this._passiveListeners.push(["mouseenter",e=>{let a;this._setupPointerExitEventsIfNeeded(),void 0!==e.x&&void 0!==e.y&&(a=e),this.show(void 0,a)}]):"off"!==this.touchGestures&&(this._disableNativeGesturesIfNecessary(),this._passiveListeners.push(["touchstart",e=>{const a=e.targetTouches?.[0],n=a?{x:a.clientX,y:a.clientY}:void 0;this._setupPointerExitEventsIfNeeded(),this._touchstartTimeout&&clearTimeout(this._touchstartTimeout),this._touchstartTimeout=setTimeout(()=>{this._touchstartTimeout=null,this.show(void 0,n)},this._defaultOptions?.touchLongPressShowDelay??500)}])),this._addListeners(this._passiveListeners))}_setupPointerExitEventsIfNeeded(){if(this._pointerExitEventsInitialized)return;this._pointerExitEventsInitialized=!0;const e=[];if(this._platformSupportsMouseEvents())e.push(["mouseleave",a=>{const n=a.relatedTarget;(!n||!this._overlayRef?.overlayElement.contains(n))&&this.hide()}],["wheel",a=>this._wheelListener(a)]);else if("off"!==this.touchGestures){this._disableNativeGesturesIfNecessary();const a=()=>{this._touchstartTimeout&&clearTimeout(this._touchstartTimeout),this.hide(this._defaultOptions?.touchendHideDelay)};e.push(["touchend",a],["touchcancel",a])}this._addListeners(e),this._passiveListeners.push(...e)}_addListeners(e){e.forEach(([a,n])=>{this._elementRef.nativeElement.addEventListener(a,n,rt)})}_platformSupportsMouseEvents(){return!this._platform.IOS&&!this._platform.ANDROID}_wheelListener(e){if(this._isTooltipVisible()){const a=this._injector.get(r.qQL).elementFromPoint(e.clientX,e.clientY),n=this._elementRef.nativeElement;a!==n&&!n.contains(a)&&this.hide()}}_disableNativeGesturesIfNecessary(){const e=this.touchGestures;if("off"!==e){const a=this._elementRef.nativeElement,n=a.style;("on"===e||"INPUT"!==a.nodeName&&"TEXTAREA"!==a.nodeName)&&(n.userSelect=n.msUserSelect=n.webkitUserSelect=n.MozUserSelect="none"),("on"===e||!a.draggable)&&(n.webkitUserDrag="none"),n.touchAction="none",n.webkitTapHighlightColor="transparent"}}_syncAriaDescription(e){this._ariaDescriptionPending||(this._ariaDescriptionPending=!0,this._ariaDescriber.removeDescription(this._elementRef.nativeElement,e,"tooltip"),this._isDestroyed||(0,t.mal)({write:()=>{this._ariaDescriptionPending=!1,this.message&&!this.disabled&&this._ariaDescriber.describe(this._elementRef.nativeElement,this.message,"tooltip")}},{injector:this._injector}))}static \u0275fac=function(a){return new(a||i)};static \u0275dir=t.FsC({type:i,selectors:[["","matTooltip",""]],hostAttrs:[1,"mat-mdc-tooltip-trigger"],hostVars:2,hostBindings:function(a,n){2&a&&t.AVh("mat-mdc-tooltip-disabled",n.disabled)},inputs:{position:[0,"matTooltipPosition","position"],positionAtOrigin:[0,"matTooltipPositionAtOrigin","positionAtOrigin"],disabled:[0,"matTooltipDisabled","disabled"],showDelay:[0,"matTooltipShowDelay","showDelay"],hideDelay:[0,"matTooltipHideDelay","hideDelay"],touchGestures:[0,"matTooltipTouchGestures","touchGestures"],message:[0,"matTooltip","message"],tooltipClass:[0,"matTooltipClass","tooltipClass"]},exportAs:["matTooltip"]})}return i})(),be=(()=>{class i{_changeDetectorRef=(0,r.WQX)(_.gRc);_elementRef=(0,r.WQX)(t.aKT);_isMultiline=!1;message;tooltipClass;_showTimeoutId;_hideTimeoutId;_triggerElement;_mouseLeaveHideDelay;_animationsDisabled=(0,v._)();_tooltip;_closeOnInteraction=!1;_isVisible=!1;_onHide=new C.B;_showAnimation="mat-mdc-tooltip-show";_hideAnimation="mat-mdc-tooltip-hide";constructor(){}show(e){null!=this._hideTimeoutId&&clearTimeout(this._hideTimeoutId),this._showTimeoutId=setTimeout(()=>{this._toggleVisibility(!0),this._showTimeoutId=void 0},e)}hide(e){null!=this._showTimeoutId&&clearTimeout(this._showTimeoutId),this._hideTimeoutId=setTimeout(()=>{this._toggleVisibility(!1),this._hideTimeoutId=void 0},e)}afterHidden(){return this._onHide}isVisible(){return this._isVisible}ngOnDestroy(){this._cancelPendingAnimations(),this._onHide.complete(),this._triggerElement=null}_handleBodyInteraction(){this._closeOnInteraction&&this.hide(0)}_markForCheck(){this._changeDetectorRef.markForCheck()}_handleMouseLeave({relatedTarget:e}){(!e||!this._triggerElement.contains(e))&&(this.isVisible()?this.hide(this._mouseLeaveHideDelay):this._finalizeAnimation(!1))}_onShow(){this._isMultiline=this._isTooltipMultiline(),this._markForCheck()}_isTooltipMultiline(){const e=this._elementRef.nativeElement.getBoundingClientRect();return e.height>24&&e.width>=200}_handleAnimationEnd({animationName:e}){(e===this._showAnimation||e===this._hideAnimation)&&this._finalizeAnimation(e===this._showAnimation)}_cancelPendingAnimations(){null!=this._showTimeoutId&&clearTimeout(this._showTimeoutId),null!=this._hideTimeoutId&&clearTimeout(this._hideTimeoutId),this._showTimeoutId=this._hideTimeoutId=void 0}_finalizeAnimation(e){e?this._closeOnInteraction=!0:this.isVisible()||this._onHide.next()}_toggleVisibility(e){const a=this._tooltip.nativeElement,n=this._showAnimation,o=this._hideAnimation;if(a.classList.remove(e?o:n),a.classList.add(e?n:o),this._isVisible!==e&&(this._isVisible=e,this._changeDetectorRef.markForCheck()),e&&!this._animationsDisabled&&"function"==typeof getComputedStyle){const s=getComputedStyle(a);("0s"===s.getPropertyValue("animation-duration")||"none"===s.getPropertyValue("animation-name"))&&(this._animationsDisabled=!0)}e&&this._onShow(),this._animationsDisabled&&(a.classList.add("_mat-animation-noopable"),this._finalizeAnimation(e))}static \u0275fac=function(a){return new(a||i)};static \u0275cmp=t.VBU({type:i,selectors:[["mat-tooltip-component"]],viewQuery:function(a,n){if(1&a&&t.GBs(re,7),2&a){let o;t.mGM(o=t.lsd())&&(n._tooltip=o.first)}},hostAttrs:["aria-hidden","true"],hostBindings:function(a,n){1&a&&t.bIt("mouseleave",function(s){return n._handleMouseLeave(s)})},decls:4,vars:4,consts:[["tooltip",""],[1,"mdc-tooltip","mat-mdc-tooltip",3,"animationend","ngClass"],[1,"mat-mdc-tooltip-surface","mdc-tooltip__surface"]],template:function(a,n){if(1&a){const o=t.RV6();t.j41(0,"div",1,0),t.bIt("animationend",function(m){return r.eBV(o),r.Njj(n._handleAnimationEnd(m))}),t.j41(2,"div",2),t.EFF(3),t.k0s()()}2&a&&(t.AVh("mdc-tooltip--multiline",n._isMultiline),t.Y8G("ngClass",n.tooltipClass),t.R7$(3),t.JRh(n.message))},dependencies:[h.YU],styles:['.mat-mdc-tooltip{position:relative;transform:scale(0);display:inline-flex}.mat-mdc-tooltip::before{content:"";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-surface{word-break:normal;overflow-wrap:anywhere;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center;will-change:transform,opacity;background-color:var(--mat-tooltip-container-color, var(--mat-sys-inverse-surface));color:var(--mat-tooltip-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mat-tooltip-container-shape, var(--mat-sys-corner-extra-small));font-family:var(--mat-tooltip-supporting-text-font, var(--mat-sys-body-small-font));font-size:var(--mat-tooltip-supporting-text-size, var(--mat-sys-body-small-size));font-weight:var(--mat-tooltip-supporting-text-weight, var(--mat-sys-body-small-weight));line-height:var(--mat-tooltip-supporting-text-line-height, var(--mat-sys-body-small-line-height));letter-spacing:var(--mat-tooltip-supporting-text-tracking, var(--mat-sys-body-small-tracking))}.mat-mdc-tooltip-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:"";pointer-events:none}.mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:right}.mat-mdc-tooltip-panel{line-height:normal}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\n'],encapsulation:2,changeDetection:0})}return i})(),he=(()=>{class i{static \u0275fac=function(a){return new(a||i)};static \u0275mod=t.$C({type:i});static \u0275inj=r.G2t({providers:[ce],imports:[ie.A,M.t,I.M,I.M,F.Gj]})}return i})();var fe=l(5781),E=l(467),st=l(4796),$=l(3156),Ce=l(7097),ve=l(3890),Pe=l(5333),ct=l(4179),u=l(3364),Me=l(2544);function Oe(i,c){1&i&&(t.j41(0,"div",4),t.nrm(1,"mat-spinner",5),t.j41(2,"p"),t.EFF(3,"Initializing 2FA setup..."),t.k0s()())}function ye(i,c){if(1&i){const e=t.RV6();t.j41(0,"div",6)(1,"div",7)(2,"mat-icon"),t.EFF(3,"qr_code_scanner"),t.k0s(),t.j41(4,"h3"),t.EFF(5,"Step 1: Scan QR Code"),t.k0s()(),t.j41(6,"div",8)(7,"p"),t.EFF(8,"Scan this QR code with your authenticator app:"),t.k0s(),t.j41(9,"div",9),t.nrm(10,"img",10),t.k0s(),t.j41(11,"div",11)(12,"p")(13,"strong"),t.EFF(14,"Recommended apps:"),t.k0s()(),t.j41(15,"ul")(16,"li"),t.EFF(17,"Google Authenticator"),t.k0s(),t.j41(18,"li"),t.EFF(19,"Microsoft Authenticator"),t.k0s(),t.j41(20,"li"),t.EFF(21,"Authy"),t.k0s(),t.j41(22,"li"),t.EFF(23,"1Password"),t.k0s()()(),t.j41(24,"div",12)(25,"p"),t.EFF(26,"Can't scan the QR code? Enter this key manually:"),t.k0s(),t.j41(27,"mat-form-field",13)(28,"mat-label"),t.EFF(29,"Secret Key"),t.k0s(),t.nrm(30,"input",14),t.j41(31,"button",15),t.bIt("click",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.copySecret())}),t.j41(32,"mat-icon"),t.EFF(33,"content_copy"),t.k0s()()()()(),t.j41(34,"div",16)(35,"button",17),t.bIt("click",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.cancel())}),t.EFF(36,"Cancel"),t.k0s(),t.j41(37,"button",18),t.bIt("click",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.nextStep())}),t.EFF(38,"Next"),t.k0s()()()}if(2&i){const e=t.XpG();t.R7$(10),t.Y8G("src",e.setupData.qrCode,t.B4B),t.R7$(20),t.Y8G("value",e.setupData.secret),t.R7$(5),t.Y8G("disabled",e.loading)}}function ke(i,c){1&i&&(t.j41(0,"mat-error"),t.EFF(1," Code is required "),t.k0s())}function we(i,c){1&i&&(t.j41(0,"mat-error"),t.EFF(1," Code must be 6 digits "),t.k0s())}function xe(i,c){1&i&&t.nrm(0,"mat-spinner",26)}function Te(i,c){1&i&&(t.j41(0,"span"),t.EFF(1,"Verify & Enable"),t.k0s())}function Fe(i,c){if(1&i){const e=t.RV6();t.j41(0,"div",6)(1,"div",7)(2,"mat-icon"),t.EFF(3,"verified_user"),t.k0s(),t.j41(4,"h3"),t.EFF(5,"Step 2: Verify Setup"),t.k0s()(),t.j41(6,"form",19),t.bIt("ngSubmit",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.onVerifyCode())}),t.j41(7,"p"),t.EFF(8,"Enter the 6-digit code from your authenticator app:"),t.k0s(),t.j41(9,"mat-form-field",20)(10,"mat-label"),t.EFF(11,"6-digit code"),t.k0s(),t.nrm(12,"input",21),t.DNE(13,ke,2,0,"mat-error",22)(14,we,2,0,"mat-error",22),t.k0s(),t.j41(15,"div",16)(16,"button",23),t.bIt("click",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.previousStep())}),t.EFF(17,"Back"),t.k0s(),t.j41(18,"button",24),t.DNE(19,xe,1,0,"mat-spinner",25)(20,Te,2,0,"span",22),t.k0s()()()()}if(2&i){let e,a;const n=t.XpG();t.R7$(6),t.Y8G("formGroup",n.verificationForm),t.R7$(7),t.Y8G("ngIf",null==(e=n.verificationForm.get("code"))?null:e.hasError("required")),t.R7$(),t.Y8G("ngIf",null==(a=n.verificationForm.get("code"))?null:a.hasError("pattern")),t.R7$(2),t.Y8G("disabled",n.loading),t.R7$(2),t.Y8G("disabled",n.loading||n.verificationForm.invalid),t.R7$(),t.Y8G("ngIf",n.loading),t.R7$(),t.Y8G("ngIf",!n.loading)}}function Ie(i,c){if(1&i&&(t.j41(0,"div",37),t.EFF(1),t.k0s()),2&i){const e=c.$implicit;t.R7$(),t.SpI(" ",e," ")}}function Ee(i,c){if(1&i){const e=t.RV6();t.j41(0,"div",6)(1,"div",7)(2,"mat-icon"),t.EFF(3,"backup"),t.k0s(),t.j41(4,"h3"),t.EFF(5,"Step 3: Save Backup Codes"),t.k0s()(),t.j41(6,"div",27)(7,"div",28)(8,"mat-icon"),t.EFF(9,"warning"),t.k0s(),t.j41(10,"p")(11,"strong"),t.EFF(12,"Important:"),t.k0s(),t.EFF(13," Save these backup codes in a secure location. Each code can only be used once if you lose access to your authenticator app."),t.k0s()(),t.j41(14,"div",29)(15,"div",30),t.DNE(16,Ie,2,1,"div",31),t.k0s()(),t.j41(17,"div",32)(18,"button",33),t.bIt("click",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.downloadBackupCodes())}),t.j41(19,"mat-icon"),t.EFF(20,"download"),t.k0s(),t.EFF(21," Download Codes "),t.k0s(),t.j41(22,"button",33),t.bIt("click",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.copyBackupCodes())}),t.j41(23,"mat-icon"),t.EFF(24,"content_copy"),t.k0s(),t.EFF(25," Copy Codes "),t.k0s()(),t.j41(26,"div",34)(27,"mat-checkbox",35),t.mxI("ngModelChange",function(n){r.eBV(e);const o=t.XpG();return t.DH7(o.backupCodesDownloaded,n)||(o.backupCodesDownloaded=n),r.Njj(n)}),t.EFF(28," I have saved my backup codes in a secure location "),t.k0s()()(),t.j41(29,"div",16)(30,"button",36),t.bIt("click",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.completeSetup())}),t.EFF(31," Complete Setup "),t.k0s()()()}if(2&i){const e=t.XpG();t.R7$(16),t.Y8G("ngForOf",e.setupData.backupCodes),t.R7$(11),t.R50("ngModel",e.backupCodesDownloaded),t.R7$(3),t.Y8G("disabled",!e.backupCodesDownloaded)}}let De=(()=>{class i{constructor(e,a,n){this.twoFactorService=e,this.formBuilder=a,this.snackBar=n,this.setupComplete=new t.bkB,this.cancelled=new t.bkB,this.currentStep=1,this.setupData=null,this.backupCodesDownloaded=!1,this.loading=!1,this.verificationForm=this.formBuilder.group({code:["",[d.k0.required,d.k0.pattern(/^\d{6}$/)]],method:["authenticator"]})}ngOnInit(){this.initializeSetup()}initializeSetup(){this.loading=!0,this.twoFactorService.setup2FA().subscribe({next:e=>{this.setupData=e,this.loading=!1},error:e=>{this.snackBar.open(e.error?.message||"Failed to initialize 2FA setup","Close",{duration:5e3}),this.loading=!1,this.cancelled.emit()}})}nextStep(){this.currentStep<3&&this.currentStep++}previousStep(){this.currentStep>1&&this.currentStep--}onVerifyCode(){if(this.verificationForm.invalid)return void this.markFormGroupTouched(this.verificationForm);this.loading=!0;const{code:e,method:a}=this.verificationForm.value,n=e.replace(/\s/g,"").padStart(6,"0");this.twoFactorService.verify2FA(n,a).subscribe({next:o=>{this.snackBar.open("2FA enabled successfully!","Close",{duration:3e3}),this.loading=!1,this.nextStep()},error:o=>{let s="Invalid verification code";o.error?.message?s=o.error.message:400===o.status?s="Invalid code. Please check your authenticator app and try again.":401===o.status&&(s="Authentication failed. Please log in again."),this.snackBar.open(s,"Close",{duration:5e3}),this.loading=!1,this.verificationForm.patchValue({code:""})}})}downloadBackupCodes(){if(!this.setupData?.backupCodes)return;const e=this.setupData.backupCodes.join("\n"),a=new Blob(["2FA Backup Codes\n","==================\n","Keep these codes safe! Each can only be used once.\n\n",e,"\n\nGenerated: "+(new Date).toISOString()],{type:"text/plain"}),n=window.URL.createObjectURL(a),o=document.createElement("a");o.href=n,o.download="2fa-backup-codes.txt",o.click(),window.URL.revokeObjectURL(n),this.backupCodesDownloaded=!0}copyBackupCodes(){if(!this.setupData?.backupCodes)return;const e=this.setupData.backupCodes.join("\n");navigator.clipboard.writeText(e).then(()=>{this.snackBar.open("Backup codes copied to clipboard!","Close",{duration:2e3})}).catch(()=>{this.snackBar.open("Failed to copy backup codes","Close",{duration:3e3})})}copySecret(){this.setupData?.secret&&navigator.clipboard.writeText(this.setupData.secret).then(()=>{this.snackBar.open("Secret key copied to clipboard!","Close",{duration:2e3})}).catch(()=>{this.snackBar.open("Failed to copy secret key","Close",{duration:3e3})})}completeSetup(){this.setupComplete.emit()}cancel(){this.cancelled.emit()}markFormGroupTouched(e){Object.keys(e.controls).forEach(a=>{e.get(a)?.markAsTouched()})}static#t=this.\u0275fac=function(a){return new(a||i)(t.rXU($.f),t.rXU(d.ok),t.rXU(T.UG))};static#e=this.\u0275cmp=t.VBU({type:i,selectors:[["app-two-factor-setup"]],outputs:{setupComplete:"setupComplete",cancelled:"cancelled"},standalone:!1,decls:12,vars:4,consts:[[1,"two-factor-setup-container"],[1,"setup-card"],["class","loading-container",4,"ngIf"],["class","step-container",4,"ngIf"],[1,"loading-container"],["diameter","40"],[1,"step-container"],[1,"step-header"],[1,"qr-section"],[1,"qr-code-container"],["alt","2FA QR Code",1,"qr-code",3,"src"],[1,"authenticator-apps"],[1,"manual-entry"],["appearance","outline",1,"secret-field"],["matInput","","readonly","",3,"value"],["mat-icon-button","","matSuffix","","matTooltip","Copy secret",3,"click"],[1,"step-actions"],["mat-button","",3,"click","disabled"],["mat-raised-button","","color","primary",3,"click"],[1,"verification-form",3,"ngSubmit","formGroup"],["appearance","outline",1,"code-field"],["matInput","","formControlName","code","placeholder","123456","maxlength","6","autocomplete","off"],[4,"ngIf"],["mat-button","","type","button",3,"click","disabled"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],["diameter","20",4,"ngIf"],["diameter","20"],[1,"backup-codes-section"],[1,"warning-message"],[1,"backup-codes"],[1,"codes-grid"],["class","backup-code",4,"ngFor","ngForOf"],[1,"backup-actions"],["mat-stroked-button","",3,"click"],[1,"confirmation"],[3,"ngModelChange","ngModel"],["mat-raised-button","","color","primary",3,"click","disabled"],[1,"backup-code"]],template:function(a,n){1&a&&(t.j41(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"mat-card-title"),t.EFF(4,"Setup Two-Factor Authentication"),t.k0s(),t.j41(5,"mat-card-subtitle"),t.EFF(6,"Secure your account with an additional layer of protection"),t.k0s()(),t.j41(7,"mat-card-content"),t.DNE(8,Oe,4,0,"div",2)(9,ye,39,3,"div",3)(10,Fe,21,7,"div",3)(11,Ee,32,3,"div",3),t.k0s()()()),2&a&&(t.R7$(8),t.Y8G("ngIf",n.loading&&!n.setupData),t.R7$(),t.Y8G("ngIf",1===n.currentStep&&n.setupData&&!n.loading),t.R7$(),t.Y8G("ngIf",2===n.currentStep&&n.setupData&&!n.loading),t.R7$(),t.Y8G("ngIf",3===n.currentStep&&n.setupData))},dependencies:[h.Sq,h.bT,d.qT,d.me,d.BC,d.cb,d.tU,d.j4,d.JD,d.vS,p.RN,p.m2,p.MM,p.Lc,p.dh,y.$z,ct.M,k.An,u.j,u.M,u.b,u.g,w.fg,x.LG,tt.So,ge],styles:['@charset "UTF-8";.two-factor-setup-container[_ngcontent-%COMP%]{max-width:600px;margin:0 auto;padding:20px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]{min-height:400px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;margin-bottom:24px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:28px;width:28px;height:28px;color:#1976d2}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;color:#1976d2}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]{text-align:center}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .qr-code-container[_ngcontent-%COMP%]{margin:24px 0}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .qr-code-container[_ngcontent-%COMP%]   .qr-code[_ngcontent-%COMP%]{max-width:200px;height:auto;border:2px solid #e0e0e0;border-radius:8px;padding:16px;background:#fff}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .authenticator-apps[_ngcontent-%COMP%]{margin:24px 0;text-align:left}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .authenticator-apps[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style-type:none;padding-left:0}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .authenticator-apps[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{padding:4px 0}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .authenticator-apps[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before{content:"\\1f4f1  ";margin-right:8px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .manual-entry[_ngcontent-%COMP%]{margin-top:24px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .manual-entry[_ngcontent-%COMP%]   .secret-field[_ngcontent-%COMP%]{width:100%}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .manual-entry[_ngcontent-%COMP%]   .secret-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{font-family:Courier New,monospace;font-size:14px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .verification-form[_ngcontent-%COMP%]{text-align:center}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .verification-form[_ngcontent-%COMP%]   .code-field[_ngcontent-%COMP%]{width:200px;margin:24px 0}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .verification-form[_ngcontent-%COMP%]   .code-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{text-align:center;font-size:24px;font-weight:700;letter-spacing:4px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:12px;padding:16px;background-color:#fff3cd;border:1px solid #ffeaa7;border-radius:8px;margin-bottom:24px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#856404;margin-top:2px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#856404}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-codes[_ngcontent-%COMP%]{margin:24px 0}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-codes[_ngcontent-%COMP%]   .codes-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(120px,1fr));gap:12px;padding:16px;background-color:#f8f9fa;border-radius:8px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-codes[_ngcontent-%COMP%]   .codes-grid[_ngcontent-%COMP%]   .backup-code[_ngcontent-%COMP%]{font-family:Courier New,monospace;font-weight:700;font-size:16px;text-align:center;padding:8px;background:#fff;border:1px solid #dee2e6;border-radius:4px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-actions[_ngcontent-%COMP%]{display:flex;gap:12px;justify-content:center;margin:24px 0}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .confirmation[_ngcontent-%COMP%]{margin:24px 0;text-align:center}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-top:32px;padding-top:16px;border-top:1px solid #e0e0e0}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin-right:8px}.two-factor-setup-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{text-align:center;padding:40px}.two-factor-setup-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin:0 auto 16px}@media (max-width: 600px){.two-factor-setup-container[_ngcontent-%COMP%]{padding:12px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .codes-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .codes-grid[_ngcontent-%COMP%]   .backup-code[_ngcontent-%COMP%]{font-size:14px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-actions[_ngcontent-%COMP%]{flex-direction:column}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]{flex-direction:column;gap:12px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}}']})}return i})();function Re(i,c){1&i&&(t.j41(0,"div",4),t.nrm(1,"mat-spinner",5),t.k0s())}function je(i,c){if(1&i){const e=t.RV6();t.j41(0,"div")(1,"app-two-factor-setup",6),t.bIt("setupComplete",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.onSetupComplete())})("cancelled",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.onSetupCancelled())}),t.k0s()()}}function Se(i,c){if(1&i){const e=t.RV6();t.j41(0,"div",11)(1,"div",12)(2,"mat-icon",13),t.EFF(3,"info"),t.k0s(),t.j41(4,"div",14)(5,"p")(6,"strong"),t.EFF(7,"Enhance your account security"),t.k0s()(),t.j41(8,"p"),t.EFF(9,"Two-factor authentication adds an extra layer of security to your account by requiring a second form of verification when signing in."),t.k0s(),t.j41(10,"div",15)(11,"h4"),t.EFF(12,"Benefits:"),t.k0s(),t.j41(13,"ul")(14,"li"),t.EFF(15,"Protects against password theft"),t.k0s(),t.j41(16,"li"),t.EFF(17,"Reduces risk of unauthorized access"),t.k0s(),t.j41(18,"li"),t.EFF(19,"Works with popular authenticator apps"),t.k0s(),t.j41(20,"li"),t.EFF(21,"Includes backup codes for recovery"),t.k0s()()()()(),t.j41(22,"div",16)(23,"button",17),t.bIt("click",function(){r.eBV(e);const n=t.XpG(2);return r.Njj(n.enableTwoFactor())}),t.j41(24,"mat-icon"),t.EFF(25,"add_circle"),t.k0s(),t.EFF(26," Enable Two-Factor Authentication "),t.k0s()()()}if(2&i){const e=t.XpG(2);t.R7$(23),t.Y8G("disabled",e.loading)}}function Be(i,c){1&i&&(t.j41(0,"mat-error"),t.EFF(1," Password is required "),t.k0s())}function Ae(i,c){1&i&&(t.j41(0,"mat-error"),t.EFF(1," 2FA code is required "),t.k0s())}function Le(i,c){1&i&&(t.j41(0,"mat-error"),t.EFF(1," Code must be 6 digits "),t.k0s())}function Ge(i,c){1&i&&t.nrm(0,"mat-spinner",44)}function Ne(i,c){1&i&&(t.j41(0,"span"),t.EFF(1,"Disable 2FA"),t.k0s())}function Ve(i,c){if(1&i){const e=t.RV6();t.j41(0,"div",34)(1,"mat-card",35)(2,"mat-card-header")(3,"mat-card-title"),t.EFF(4,"Disable Two-Factor Authentication"),t.k0s(),t.j41(5,"mat-card-subtitle"),t.EFF(6,"Enter your password and a 2FA code to confirm"),t.k0s()(),t.j41(7,"mat-card-content")(8,"form",36),t.bIt("ngSubmit",function(){r.eBV(e);const n=t.XpG(3);return r.Njj(n.onDisableTwoFactor())}),t.j41(9,"mat-form-field",37)(10,"mat-label"),t.EFF(11,"Current Password"),t.k0s(),t.nrm(12,"input",38),t.DNE(13,Be,2,0,"mat-error",2),t.k0s(),t.j41(14,"mat-form-field",37)(15,"mat-label"),t.EFF(16,"6-digit 2FA Code"),t.k0s(),t.nrm(17,"input",39),t.DNE(18,Ae,2,0,"mat-error",2)(19,Le,2,0,"mat-error",2),t.k0s(),t.j41(20,"div",40)(21,"button",41),t.bIt("click",function(){r.eBV(e);const n=t.XpG(3);return r.Njj(n.hideDisableForm())}),t.EFF(22," Cancel "),t.k0s(),t.j41(23,"button",42),t.DNE(24,Ge,1,0,"mat-spinner",43)(25,Ne,2,0,"span",2),t.k0s()()()()()()}if(2&i){let e,a,n;const o=t.XpG(3);t.R7$(8),t.Y8G("formGroup",o.disableForm),t.R7$(5),t.Y8G("ngIf",null==(e=o.disableForm.get("password"))?null:e.hasError("required")),t.R7$(5),t.Y8G("ngIf",null==(a=o.disableForm.get("code"))?null:a.hasError("required")),t.R7$(),t.Y8G("ngIf",null==(n=o.disableForm.get("code"))?null:n.hasError("pattern")),t.R7$(2),t.Y8G("disabled",o.loading),t.R7$(2),t.Y8G("disabled",o.loading||o.disableForm.invalid),t.R7$(),t.Y8G("ngIf",o.loading),t.R7$(),t.Y8G("ngIf",!o.loading)}}function $e(i,c){if(1&i){const e=t.RV6();t.j41(0,"div",18)(1,"div",19)(2,"mat-icon",20),t.EFF(3,"check_circle"),t.k0s(),t.j41(4,"div",21)(5,"p")(6,"strong"),t.EFF(7,"Your account is protected"),t.k0s()(),t.j41(8,"p"),t.EFF(9,"Two-factor authentication is active on your account. You'll be prompted for a verification code when signing in from new devices."),t.k0s()()(),t.j41(10,"div",22)(11,"div",23)(12,"h4"),t.EFF(13,"Test & Manage"),t.k0s(),t.j41(14,"div",24)(15,"button",25),t.bIt("click",function(){r.eBV(e);const n=t.XpG(2);return r.Njj(n.sendTestCode())}),t.j41(16,"mat-icon"),t.EFF(17,"send"),t.k0s(),t.EFF(18," Send Test Code "),t.k0s()()(),t.nrm(19,"mat-divider"),t.j41(20,"div",26)(21,"h4"),t.EFF(22,"Danger Zone"),t.k0s(),t.j41(23,"p",27)(24,"mat-icon"),t.EFF(25,"warning"),t.k0s(),t.EFF(26," Disabling 2FA will make your account less secure "),t.k0s(),t.j41(27,"div",28)(28,"button",29),t.bIt("click",function(){r.eBV(e);const n=t.XpG(2);return r.Njj(n.showDisableForm2FA())}),t.j41(29,"mat-icon"),t.EFF(30,"remove_circle"),t.k0s(),t.EFF(31," Disable with Password & Code "),t.k0s(),t.j41(32,"div",30)(33,"p",31)(34,"mat-icon"),t.EFF(35,"help_outline"),t.k0s(),t.EFF(36," Can't access your authenticator device? "),t.k0s(),t.j41(37,"button",32),t.bIt("click",function(){r.eBV(e);const n=t.XpG(2);return r.Njj(n.openEmailDisableDialog())}),t.j41(38,"mat-icon"),t.EFF(39,"email"),t.k0s(),t.EFF(40," Disable via Email Verification "),t.k0s()()()()(),t.DNE(41,Ve,26,8,"div",33),t.k0s()}if(2&i){const e=t.XpG(2);t.R7$(15),t.Y8G("disabled",e.loading),t.R7$(13),t.Y8G("disabled",e.loading||e.showDisableForm),t.R7$(9),t.Y8G("disabled",e.loading),t.R7$(4),t.Y8G("ngIf",e.showDisableForm)}}function Xe(i,c){if(1&i&&(t.j41(0,"div",7)(1,"mat-card",8)(2,"mat-card-header")(3,"mat-card-title")(4,"mat-icon"),t.EFF(5),t.k0s(),t.EFF(6," Two-Factor Authentication "),t.k0s(),t.j41(7,"mat-card-subtitle"),t.EFF(8," Status: "),t.j41(9,"span"),t.EFF(10),t.k0s()()(),t.j41(11,"mat-card-content"),t.DNE(12,Se,27,1,"div",9)(13,$e,42,4,"div",10),t.k0s()()()),2&i){const e=t.XpG();t.R7$(4),t.HbH(e.twoFactorStatus.enabled?"enabled-icon":"disabled-icon"),t.R7$(),t.SpI(" ","security"," "),t.R7$(4),t.HbH(e.twoFactorStatus.enabled?"status-enabled":"status-disabled"),t.R7$(),t.SpI(" ",e.twoFactorStatus.enabled?"Enabled":"Disabled"," "),t.R7$(2),t.Y8G("ngIf",!e.twoFactorStatus.enabled),t.R7$(),t.Y8G("ngIf",e.twoFactorStatus.enabled)}}let Ue=(()=>{class i{constructor(e,a,n,o,s){this.twoFactorService=e,this.formBuilder=a,this.snackBar=n,this.dialog=o,this.authService=s,this.twoFactorStatus={enabled:!1},this.loading=!1,this.showSetup=!1,this.showDisableForm=!1,this.disableForm=this.formBuilder.group({password:["",[d.k0.required]],code:["",[d.k0.required,d.k0.pattern(/^\d{6}$/)]]})}ngOnInit(){this.loadStatus()}loadStatus(){this.loading=!0,this.twoFactorService.get2FAStatus().subscribe({next:e=>{this.twoFactorStatus=e,this.loading=!1},error:e=>{console.error("Failed to load 2FA status:",e),this.loading=!1}})}enableTwoFactor(){this.showSetup=!0}onSetupComplete(){this.showSetup=!1,this.loadStatus(),this.snackBar.open("Two-factor authentication has been enabled!","Close",{duration:5e3})}onSetupCancelled(){this.showSetup=!1}showDisableForm2FA(){this.showDisableForm=!0}hideDisableForm(){this.showDisableForm=!1,this.disableForm.reset()}onDisableTwoFactor(){if(this.disableForm.invalid)return void this.markFormGroupTouched(this.disableForm);this.loading=!0;const{code:a}=this.disableForm.value;this.twoFactorService.disable2FA(a).subscribe({next:n=>{this.snackBar.open("Two-factor authentication has been disabled!","Close",{duration:5e3}),this.hideDisableForm(),this.loadStatus(),this.loading=!1},error:n=>{this.snackBar.open(n.error?.message||"Failed to disable 2FA","Close",{duration:5e3}),this.loading=!1}})}sendTestCode(){this.loading=!0,this.twoFactorService.send2FASMS().subscribe({next:e=>{this.snackBar.open("Test code sent to your phone!","Close",{duration:3e3}),this.loading=!1},error:e=>{this.snackBar.open(e.error?.message||"Failed to send test code","Close",{duration:5e3}),this.loading=!1}})}openEmailDisableDialog(){const e=this.authService.currentUserValue;e?this.dialog.open(Me.p,{width:"500px",maxWidth:"90vw",disableClose:!1,data:{email:e.email,allCodesUsed:!1,source:"profile"}}).afterClosed().subscribe(n=>{n?.success&&this.snackBar.open("Disable confirmation email sent! Check your inbox for the confirmation link.","Close",{duration:8e3,panelClass:["success-snackbar"]})}):this.snackBar.open("User information not available. Please refresh and try again.","Close",{duration:5e3})}markFormGroupTouched(e){Object.keys(e.controls).forEach(a=>{e.get(a)?.markAsTouched()})}static#t=this.\u0275fac=function(a){return new(a||i)(t.rXU($.f),t.rXU(d.ok),t.rXU(T.UG),t.rXU(V.h),t.rXU(st.u))};static#e=this.\u0275cmp=t.VBU({type:i,selectors:[["app-two-factor-management"]],standalone:!1,decls:4,vars:3,consts:[[1,"two-factor-management"],["class","loading-container",4,"ngIf"],[4,"ngIf"],["class","management-container",4,"ngIf"],[1,"loading-container"],["diameter","30"],[3,"setupComplete","cancelled"],[1,"management-container"],[1,"status-card"],["class","disabled-state",4,"ngIf"],["class","enabled-state",4,"ngIf"],[1,"disabled-state"],[1,"info-section"],[1,"info-icon"],[1,"info-content"],[1,"benefits"],[1,"action-section"],["mat-raised-button","","color","primary",3,"click","disabled"],[1,"enabled-state"],[1,"success-section"],[1,"success-icon"],[1,"success-content"],[1,"management-actions"],[1,"action-group"],[1,"button-group"],["mat-stroked-button","",3,"click","disabled"],[1,"danger-zone"],[1,"warning-text"],[1,"disable-options"],["mat-stroked-button","","color","warn",3,"click","disabled"],[1,"alternative-disable"],[1,"help-text"],["mat-button","","color","accent",3,"click","disabled"],["class","disable-form-container",4,"ngIf"],[1,"disable-form-container"],[1,"disable-card"],[3,"ngSubmit","formGroup"],["appearance","outline",1,"full-width"],["matInput","","type","password","formControlName","password","autocomplete","current-password"],["matInput","","formControlName","code","placeholder","123456","maxlength","6","autocomplete","off"],[1,"form-actions"],["mat-button","","type","button",3,"click","disabled"],["mat-raised-button","","color","warn","type","submit",3,"disabled"],["diameter","20",4,"ngIf"],["diameter","20"]],template:function(a,n){1&a&&(t.j41(0,"div",0),t.DNE(1,Re,2,0,"div",1)(2,je,2,0,"div",2)(3,Xe,14,8,"div",3),t.k0s()),2&a&&(t.R7$(),t.Y8G("ngIf",n.loading&&!n.showSetup),t.R7$(),t.Y8G("ngIf",n.showSetup),t.R7$(),t.Y8G("ngIf",!n.loading&&!n.showSetup))},dependencies:[h.bT,d.qT,d.me,d.BC,d.cb,d.tU,d.j4,d.JD,p.RN,p.m2,p.MM,p.Lc,p.dh,y.$z,k.An,u.j,u.M,u.b,w.fg,x.LG,N.q,De],styles:[".two-factor-management[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{text-align:center;padding:40px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .enabled-icon[_ngcontent-%COMP%]{color:#4caf50}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .disabled-icon[_ngcontent-%COMP%]{color:#757575}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%]   .status-enabled[_ngcontent-%COMP%]{color:#4caf50;font-weight:500}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%]   .status-disabled[_ngcontent-%COMP%]{color:#757575;font-weight:500}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:24px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-icon[_ngcontent-%COMP%]{color:#2196f3;font-size:28px;width:28px;height:28px;margin-top:4px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]{flex:1}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 12px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-of-type{margin-bottom:16px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]   .benefits[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 8px;color:#333}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]   .benefits[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:0;padding-left:20px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]   .benefits[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:4px;color:#666}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]{text-align:center;padding-top:16px;border-top:1px solid #e0e0e0}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:24px;padding:16px;background-color:#e8f5e8;border-radius:8px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%]{color:#4caf50;font-size:28px;width:28px;height:28px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]{flex:1}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 8px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child{margin-bottom:0}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]{margin-bottom:24px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;color:#333}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   mat-divider[_ngcontent-%COMP%]{margin:24px 0}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;color:#d32f2f}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin:0 0 16px;color:#f57c00;font-size:14px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%] > button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]   .alternative-disable[_ngcontent-%COMP%]{background:#f8f9fa;border:1px solid #e9ecef;border-radius:8px;padding:16px;text-align:center}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]   .alternative-disable[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:8px;margin:0 0 12px;color:#6c757d;font-size:14px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]   .alternative-disable[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#6c757d}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]   .alternative-disable[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]{margin-top:24px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]{border:1px solid #ffcdd2}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{color:#d32f2f}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-top:16px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin-right:8px}@media (max-width: 600px){.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]{flex-direction:column;text-align:center}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-icon[_ngcontent-%COMP%]{align-self:center}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]{flex-direction:column;text-align:center}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%]{align-self:center}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%] > button[_ngcontent-%COMP%]{width:100%}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]   .alternative-disable[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{flex-direction:column;gap:12px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}}"]})}return i})();function ze(i,c){if(1&i&&(t.j41(0,"div",33),t.nrm(1,"img",34),t.k0s()),2&i){const e=t.XpG();t.R7$(),t.Y8G("src",e.currentUser.avatarUrl,t.B4B)("alt",(e.currentUser.firstName||"")+" "+(e.currentUser.lastName||""))}}function Ye(i,c){if(1&i&&(t.j41(0,"div",35)(1,"p")(2,"strong"),t.EFF(3,"Connected via:"),t.k0s(),t.j41(4,"span",36),t.nrm(5,"i"),t.EFF(6),t.k0s()()()),2&i){const e=t.XpG();t.R7$(5),t.HbH(e.getOAuthProviderIcon()),t.xc7("color",e.getOAuthProviderColor()),t.R7$(),t.SpI(" ",e.getOAuthProviderName()," ")}}function He(i,c){if(1&i){const e=t.RV6();t.j41(0,"button",18),t.bIt("click",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.enableEditMode())}),t.j41(1,"mat-icon"),t.EFF(2,"edit"),t.k0s(),t.EFF(3," Edit Profile "),t.k0s()}}function Qe(i,c){1&i&&(t.j41(0,"mat-error"),t.EFF(1," First name is required "),t.k0s())}function We(i,c){1&i&&(t.j41(0,"mat-error"),t.EFF(1," Last name is required "),t.k0s())}function Ke(i,c){1&i&&(t.j41(0,"span"),t.EFF(1,"Email is required"),t.k0s())}function Ze(i,c){1&i&&(t.j41(0,"span"),t.EFF(1,"Please enter a valid email"),t.k0s())}function Je(i,c){if(1&i&&(t.j41(0,"mat-error"),t.DNE(1,Ke,2,0,"span",42)(2,Ze,2,0,"span",42),t.k0s()),2&i){let e,a;const n=t.XpG(2);t.R7$(),t.Y8G("ngIf",null==(e=n.editProfileForm.get("email"))||null==e.errors?null:e.errors.required),t.R7$(),t.Y8G("ngIf",null==(a=n.editProfileForm.get("email"))||null==a.errors?null:a.errors.email)}}function qe(i,c){1&i&&t.nrm(0,"mat-spinner",51)}function tn(i,c){1&i&&(t.j41(0,"span"),t.EFF(1,"Save Changes"),t.k0s())}function en(i,c){if(1&i){const e=t.RV6();t.j41(0,"mat-card",37)(1,"mat-card-header")(2,"mat-card-title"),t.EFF(3,"Edit Profile"),t.k0s()(),t.j41(4,"mat-card-content")(5,"form",38),t.bIt("ngSubmit",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.saveProfile())}),t.j41(6,"div",39)(7,"mat-form-field",40)(8,"mat-label"),t.EFF(9,"First Name"),t.k0s(),t.nrm(10,"input",41),t.DNE(11,Qe,2,0,"mat-error",42),t.k0s(),t.j41(12,"mat-form-field",40)(13,"mat-label"),t.EFF(14,"Last Name"),t.k0s(),t.nrm(15,"input",43),t.DNE(16,We,2,0,"mat-error",42),t.k0s()(),t.j41(17,"mat-form-field",44)(18,"mat-label"),t.EFF(19,"Email"),t.k0s(),t.nrm(20,"input",45),t.DNE(21,Je,3,2,"mat-error",42),t.k0s(),t.j41(22,"mat-form-field",44)(23,"mat-label"),t.EFF(24,"Phone (Optional)"),t.k0s(),t.nrm(25,"input",46),t.k0s(),t.j41(26,"div",47)(27,"button",48),t.DNE(28,qe,1,0,"mat-spinner",49)(29,tn,2,0,"span",42),t.k0s(),t.j41(30,"button",50),t.bIt("click",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.cancelEdit())}),t.EFF(31," Cancel "),t.k0s()()()()()}if(2&i){let e,a,n;const o=t.XpG();t.R7$(5),t.Y8G("formGroup",o.editProfileForm),t.R7$(6),t.Y8G("ngIf",(null==(e=o.editProfileForm.get("firstName"))?null:e.invalid)&&(null==(e=o.editProfileForm.get("firstName"))?null:e.touched)),t.R7$(5),t.Y8G("ngIf",(null==(a=o.editProfileForm.get("lastName"))?null:a.invalid)&&(null==(a=o.editProfileForm.get("lastName"))?null:a.touched)),t.R7$(5),t.Y8G("ngIf",(null==(n=o.editProfileForm.get("email"))?null:n.invalid)&&(null==(n=o.editProfileForm.get("email"))?null:n.touched)),t.R7$(6),t.Y8G("disabled",o.loading||o.editProfileForm.invalid),t.R7$(),t.Y8G("ngIf",o.loading),t.R7$(),t.Y8G("ngIf",!o.loading)}}function nn(i,c){if(1&i&&(t.j41(0,"div",52)(1,"mat-icon",53),t.EFF(2,"info"),t.k0s(),t.j41(3,"span"),t.EFF(4),t.k0s()()),2&i){const e=t.XpG();t.R7$(4),t.SpI("Connected via ",e.getOAuthProviderName()," \u2022 You can also set a password for direct login")}}function an(i,c){if(1&i){const e=t.RV6();t.j41(0,"mat-form-field",40)(1,"mat-label"),t.EFF(2,"Current Password"),t.k0s(),t.nrm(3,"input",60),t.j41(4,"button",58),t.bIt("click",function(){r.eBV(e);const n=t.XpG(2);return r.Njj(n.hideCurrentPassword=!n.hideCurrentPassword)}),t.j41(5,"mat-icon"),t.EFF(6),t.k0s()(),t.j41(7,"mat-error"),t.EFF(8),t.k0s()()}if(2&i){const e=t.XpG(2);t.R7$(3),t.Y8G("type",e.hideCurrentPassword?"password":"text"),t.R7$(3),t.JRh(e.hideCurrentPassword?"visibility_off":"visibility"),t.R7$(2),t.JRh(e.getFieldError("currentPassword"))}}function on(i,c){if(1&i&&(t.j41(0,"div",61)(1,"mat-icon",53),t.EFF(2,"lock"),t.k0s(),t.j41(3,"p"),t.EFF(4,"Set up a password to enable direct login with your email address."),t.k0s(),t.j41(5,"p")(6,"small"),t.EFF(7),t.k0s()()()),2&i){const e=t.XpG(2);t.R7$(7),t.SpI("You can continue using ",e.getOAuthProviderName()," login or use your new password.")}}function rn(i,c){if(1&i&&(t.j41(0,"mat-form-field",40)(1,"mat-label"),t.EFF(2,"2FA Code (Required)"),t.k0s(),t.nrm(3,"input",62),t.j41(4,"mat-icon",63),t.EFF(5,"verified_user"),t.k0s(),t.j41(6,"mat-hint"),t.EFF(7,"Enter the 6-digit code from your authenticator app"),t.k0s(),t.j41(8,"mat-error"),t.EFF(9),t.k0s()()),2&i){const e=t.XpG(2);t.R7$(9),t.JRh(e.getFieldError("twoFactorToken"))}}function sn(i,c){1&i&&t.nrm(0,"mat-spinner",51)}function cn(i,c){1&i&&(t.j41(0,"span"),t.EFF(1,"Change Password"),t.k0s())}function ln(i,c){if(1&i){const e=t.RV6();t.j41(0,"div",54)(1,"form",38),t.bIt("ngSubmit",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.onChangePassword())}),t.DNE(2,an,9,3,"mat-form-field",55)(3,on,8,1,"div",56),t.j41(4,"mat-form-field",40)(5,"mat-label"),t.EFF(6,"New Password"),t.k0s(),t.nrm(7,"input",57),t.j41(8,"button",58),t.bIt("click",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.hideNewPassword=!n.hideNewPassword)}),t.j41(9,"mat-icon"),t.EFF(10),t.k0s()(),t.j41(11,"mat-error"),t.EFF(12),t.k0s()(),t.j41(13,"mat-form-field",40)(14,"mat-label"),t.EFF(15,"Confirm New Password"),t.k0s(),t.nrm(16,"input",59),t.j41(17,"button",58),t.bIt("click",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.hideConfirmPassword=!n.hideConfirmPassword)}),t.j41(18,"mat-icon"),t.EFF(19),t.k0s()(),t.j41(20,"mat-error"),t.EFF(21),t.k0s()(),t.DNE(22,rn,10,1,"mat-form-field",55),t.j41(23,"div",47)(24,"button",48),t.DNE(25,sn,1,0,"mat-spinner",49)(26,cn,2,0,"span",42),t.k0s(),t.j41(27,"button",50),t.bIt("click",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.toggleChangePassword())}),t.EFF(28," Cancel "),t.k0s()()()()}if(2&i){const e=t.XpG();t.R7$(),t.Y8G("formGroup",e.changePasswordForm),t.R7$(),t.Y8G("ngIf",!e.isOAuthUser()),t.R7$(),t.Y8G("ngIf",e.isOAuthUser()),t.R7$(4),t.Y8G("type",e.hideNewPassword?"password":"text"),t.R7$(3),t.JRh(e.hideNewPassword?"visibility_off":"visibility"),t.R7$(2),t.JRh(e.getFieldError("newPassword")),t.R7$(4),t.Y8G("type",e.hideConfirmPassword?"password":"text"),t.R7$(3),t.JRh(e.hideConfirmPassword?"visibility_off":"visibility"),t.R7$(2),t.JRh(e.getFieldError("confirmPassword")),t.R7$(),t.Y8G("ngIf",e.twoFactorStatus.enabled),t.R7$(2),t.Y8G("disabled",e.loading),t.R7$(),t.Y8G("ngIf",e.loading),t.R7$(),t.Y8G("ngIf",!e.loading)}}function dn(i,c){if(1&i&&(t.j41(0,"p")(1,"strong"),t.EFF(2,"Requested:"),t.k0s(),t.EFF(3),t.nI1(4,"date"),t.k0s()),2&i){const e=t.XpG(2);t.R7$(3),t.SpI(" ",t.i5U(4,1,null==e.deletionStatus||null==e.deletionStatus.deletionRecord?null:e.deletionStatus.deletionRecord.deletionRequestedAt,"medium")," ")}}function mn(i,c){1&i&&t.nrm(0,"mat-spinner",68)}function _n(i,c){1&i&&(t.j41(0,"mat-icon"),t.EFF(1,"cancel"),t.k0s())}function pn(i,c){if(1&i){const e=t.RV6();t.j41(0,"div",64)(1,"mat-icon",25),t.EFF(2,"warning"),t.k0s(),t.j41(3,"div",65)(4,"h4"),t.EFF(5,"Account Deletion Pending"),t.k0s(),t.j41(6,"p"),t.EFF(7,"Your account is scheduled for deletion. Please check your email for confirmation instructions."),t.k0s(),t.DNE(8,dn,5,4,"p",42),t.j41(9,"button",66),t.bIt("click",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.cancelPendingDeletion())}),t.DNE(10,mn,1,0,"mat-spinner",67)(11,_n,2,0,"mat-icon",42),t.EFF(12," Cancel Deletion Request "),t.k0s()()()}if(2&i){const e=t.XpG();t.R7$(8),t.Y8G("ngIf",null==e.deletionStatus||null==e.deletionStatus.deletionRecord?null:e.deletionStatus.deletionRecord.deletionRequestedAt),t.R7$(),t.Y8G("disabled",e.isDeletionLoading),t.R7$(),t.Y8G("ngIf",e.isDeletionLoading),t.R7$(),t.Y8G("ngIf",!e.isDeletionLoading)}}function un(i,c){1&i&&t.nrm(0,"mat-spinner",68)}function gn(i,c){1&i&&(t.j41(0,"mat-icon"),t.EFF(1,"download"),t.k0s())}function bn(i,c){1&i&&t.nrm(0,"mat-spinner",68)}function hn(i,c){1&i&&(t.j41(0,"mat-icon"),t.EFF(1,"delete_forever"),t.k0s())}function fn(i,c){if(1&i){const e=t.RV6();t.j41(0,"div",78)(1,"mat-card",79)(2,"mat-card-content")(3,"div",80)(4,"mat-icon",25),t.EFF(5,"warning"),t.k0s(),t.j41(6,"div")(7,"h4"),t.EFF(8,"Quick Account Deletion"),t.k0s(),t.j41(9,"p"),t.EFF(10,"This will delete your account with default preservation settings:"),t.k0s(),t.j41(11,"ul")(12,"li"),t.EFF(13,"\u2705 Payment data will be preserved for 30 days"),t.k0s(),t.j41(14,"li"),t.EFF(15,"\u2705 Transaction history will be preserved for 30 days"),t.k0s(),t.j41(16,"li"),t.EFF(17,"\u274c Profile data will be permanently deleted"),t.k0s(),t.j41(18,"li"),t.EFF(19,"\u274c Security logs will be permanently deleted"),t.k0s()(),t.j41(20,"p")(21,"strong"),t.EFF(22,"You will receive an email confirmation before deletion is finalized."),t.k0s()()()(),t.j41(23,"div",81)(24,"button",82),t.bIt("click",function(){r.eBV(e);const n=t.XpG(2);return r.Njj(n.toggleDeleteAccountForm())}),t.EFF(25," Cancel "),t.k0s(),t.j41(26,"button",76),t.bIt("click",function(){r.eBV(e);const n=t.XpG(2);return r.Njj(n.quickDeleteAccount())}),t.DNE(27,bn,1,0,"mat-spinner",67)(28,hn,2,0,"mat-icon",42),t.EFF(29," Request Deletion "),t.k0s()()()()()}if(2&i){const e=t.XpG(2);t.R7$(26),t.Y8G("disabled",e.isDeletionLoading),t.R7$(),t.Y8G("ngIf",e.isDeletionLoading),t.R7$(),t.Y8G("ngIf",!e.isDeletionLoading)}}function Cn(i,c){if(1&i){const e=t.RV6();t.j41(0,"div",69)(1,"div",70)(2,"div",71)(3,"mat-icon",53),t.EFF(4,"download"),t.k0s(),t.j41(5,"div")(6,"h4"),t.EFF(7,"Export Your Data"),t.k0s(),t.j41(8,"p"),t.EFF(9,"Download a copy of your account data and information."),t.k0s()()(),t.j41(10,"button",72),t.bIt("click",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.exportUserData())}),t.DNE(11,un,1,0,"mat-spinner",67)(12,gn,2,0,"mat-icon",42),t.EFF(13," Export Data "),t.k0s()(),t.nrm(14,"mat-divider"),t.j41(15,"div",73)(16,"div",71)(17,"mat-icon",25),t.EFF(18,"delete_forever"),t.k0s(),t.j41(19,"div")(20,"h4"),t.EFF(21,"Delete Account"),t.k0s(),t.j41(22,"p"),t.EFF(23,"Permanently delete your account and data. This action cannot be undone immediately."),t.k0s()()(),t.j41(24,"div",74)(25,"button",75),t.bIt("click",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.toggleDeleteAccountForm())}),t.j41(26,"mat-icon"),t.EFF(27),t.k0s(),t.EFF(28),t.k0s(),t.j41(29,"button",76),t.bIt("click",function(){r.eBV(e);const n=t.XpG();return r.Njj(n.navigateToAccountDeletion())}),t.j41(30,"mat-icon"),t.EFF(31,"settings"),t.k0s(),t.EFF(32," Advanced Options "),t.k0s()()(),t.DNE(33,fn,30,3,"div",77),t.k0s()}if(2&i){const e=t.XpG();t.R7$(10),t.Y8G("disabled",e.isDeletionLoading),t.R7$(),t.Y8G("ngIf",e.isDeletionLoading),t.R7$(),t.Y8G("ngIf",!e.isDeletionLoading),t.R7$(13),t.Y8G("disabled",e.isDeletionLoading),t.R7$(2),t.JRh(e.showDeleteAccountForm?"expand_less":"expand_more"),t.R7$(),t.SpI(" ",e.showDeleteAccountForm?"Hide Options":"Quick Delete"," "),t.R7$(),t.Y8G("disabled",e.isDeletionLoading),t.R7$(4),t.Y8G("ngIf",e.showDeleteAccountForm)}}function vn(i,c){if(1&i&&(t.j41(0,"div",30)(1,"strong"),t.EFF(2,"OAuth Provider:"),t.k0s(),t.j41(3,"span"),t.nrm(4,"i"),t.EFF(5),t.k0s()()),2&i){const e=t.XpG();t.R7$(4),t.HbH(e.getOAuthProviderIcon()),t.xc7("color",e.getOAuthProviderColor()),t.R7$(),t.SpI(" ",e.getOAuthProviderName()," ")}}const Pn=[{path:"",component:(()=>{class i{constructor(e,a,n,o,s,m,g,O){this.authService=e,this.twoFactorService=a,this.oauthService=n,this.accountDeletionService=o,this.formBuilder=s,this.snackBar=m,this.dialog=g,this.router=O,this.currentUser=null,this.twoFactorStatus={enabled:!1},this.deletionStatus=null,this.loading=!1,this.editMode=!1,this.hideCurrentPassword=!0,this.hideNewPassword=!0,this.hideConfirmPassword=!0,this.showChangePassword=!1,this.isDeletionLoading=!1,this.showDeleteAccountForm=!1,this.changePasswordForm=this.formBuilder.group({currentPassword:["",[d.k0.required]],newPassword:["",[d.k0.required,d.k0.minLength(8)]],confirmPassword:["",[d.k0.required]],twoFactorToken:[""]},{validators:this.passwordMatchValidator}),this.editProfileForm=this.formBuilder.group({firstName:["",[d.k0.required]],lastName:["",[d.k0.required]],email:["",[d.k0.required,d.k0.email]],phone:[""]})}ngOnInit(){this.currentUser=this.authService.currentUserValue,this.initializeEditForm(),this.load2FAStatus(),this.checkDeletionStatus()}initializeEditForm(){this.currentUser&&this.editProfileForm.patchValue({firstName:this.currentUser.firstName||"",lastName:this.currentUser.lastName||"",email:this.currentUser.email||"",phone:this.currentUser.phone||""})}enableEditMode(){this.editMode=!0,this.initializeEditForm()}cancelEdit(){this.editMode=!1,this.initializeEditForm()}saveProfile(){if(this.editProfileForm.invalid)return void this.markFormGroupTouched(this.editProfileForm);this.loading=!0;const e=this.editProfileForm.value;this.authService.updateProfile(e).subscribe({next:a=>{this.snackBar.open("Profile updated successfully!","Close",{duration:3e3}),this.editMode=!1,this.loading=!1,this.currentUser&&(this.currentUser={...this.currentUser,...e})},error:a=>{this.snackBar.open(a.error?.message||"Failed to update profile","Close",{duration:5e3}),this.loading=!1}})}toggleChangePassword(){this.showChangePassword=!this.showChangePassword,this.showChangePassword?this.isOAuthUser()?(this.changePasswordForm.get("currentPassword")?.clearValidators(),this.changePasswordForm.get("currentPassword")?.updateValueAndValidity()):(this.changePasswordForm.get("currentPassword")?.setValidators([d.k0.required]),this.changePasswordForm.get("currentPassword")?.updateValueAndValidity()):this.changePasswordForm.reset()}onChangePassword(){if(this.changePasswordForm.invalid)return void this.markFormGroupTouched(this.changePasswordForm);this.loading=!0;const e=this.changePasswordForm.value,a=this.isOAuthUser()?"":e.currentPassword;this.authService.changePassword(a,e.newPassword,e.twoFactorToken||void 0).subscribe({next:n=>{const o=this.isOAuthUser()?"Password set successfully! You can now login with email and password.":"Password changed successfully!";this.snackBar.open(o,"Close",{duration:5e3}),this.changePasswordForm.reset(),this.showChangePassword=!1,this.loading=!1},error:n=>{this.snackBar.open(n.message||"Failed to change password","Close",{duration:5e3}),this.loading=!1}})}onForgotPassword(){this.currentUser?.email?(this.loading=!0,this.authService.forgotPassword(this.currentUser.email).subscribe({next:e=>{this.snackBar.open("Password reset instructions have been sent to your email","Close",{duration:5e3}),this.loading=!1},error:e=>{this.snackBar.open(e.error?.message||"Failed to send password reset email","Close",{duration:5e3}),this.loading=!1}})):this.snackBar.open("No email address found","Close",{duration:3e3})}isOAuthUser(){return this.oauthService.isOAuthUser(this.currentUser)}getOAuthProviderName(){return this.oauthService.getOAuthProviderName(this.currentUser)}getOAuthProviderIcon(){return this.oauthService.getOAuthProviderIcon(this.currentUser)}getOAuthProviderColor(){return this.oauthService.getOAuthProviderColor(this.currentUser)}getFieldError(e){const a=this.changePasswordForm.get(e);if(a?.errors&&a.touched){if(a.errors.required)return`${e} is required`;if(a.errors.minlength)return`${e} must be at least ${a.errors.minlength.requiredLength} characters`;if(a.errors.passwordMismatch)return"Passwords do not match"}return""}passwordMatchValidator(e){const a=e.get("newPassword"),n=e.get("confirmPassword");return n.setErrors(a&&n&&a.value!==n.value?{passwordMismatch:!0}:null),null}markFormGroupTouched(e){Object.keys(e.controls).forEach(a=>{e.get(a)?.markAsTouched()})}load2FAStatus(){this.twoFactorService.get2FAStatus().subscribe({next:e=>{this.twoFactorStatus=e},error:e=>{console.error("Failed to load 2FA status:",e)}})}navigateToAccountDeletion(){this.router.navigate(["/profile/delete-account"])}checkDeletionStatus(){var e=this;return(0,E.A)(function*(){try{e.isDeletionLoading=!0;const a=yield e.accountDeletionService.getDeletionStatus().toPromise();e.deletionStatus=a||null}catch(a){console.error("Error checking deletion status:",a)}finally{e.isDeletionLoading=!1}})()}cancelPendingDeletion(){var e=this;return(0,E.A)(function*(){if(e.deletionStatus?.hasPendingDeletion)try{e.isDeletionLoading=!0,yield e.accountDeletionService.cancelDeletion().toPromise(),e.snackBar.open("Account deletion request has been cancelled.","Close",{duration:5e3,panelClass:["snack-bar-success"]}),e.deletionStatus=null}catch(a){console.error("Error cancelling deletion:",a),e.snackBar.open(a.message||"Failed to cancel deletion request.","Close",{duration:5e3,panelClass:["snack-bar-error"]})}finally{e.isDeletionLoading=!1}})()}toggleDeleteAccountForm(){this.showDeleteAccountForm=!this.showDeleteAccountForm}quickDeleteAccount(){var e=this;return(0,E.A)(function*(){if(confirm("Are you sure you want to delete your account? This action cannot be undone immediately. Some data may be preserved for restoration during a limited period."))try{e.isDeletionLoading=!0;const n={preservePaymentData:!0,preserveTransactionHistory:!0,preserveProfileData:!1,preserveSecurityLogs:!1,customRetentionPeriod:30,reason:"Deleted from profile settings"};yield e.accountDeletionService.requestAccountDeletion(n).toPromise(),e.snackBar.open("Account deletion requested! Please check your email for confirmation instructions.","Close",{duration:8e3,panelClass:["snack-bar-warning"]}),e.showDeleteAccountForm=!1,yield e.checkDeletionStatus()}catch(n){console.error("Error requesting deletion:",n),e.snackBar.open(n.message||"Failed to request account deletion. Please try again.","Close",{duration:5e3,panelClass:["snack-bar-error"]})}finally{e.isDeletionLoading=!1}})()}exportUserData(){var e=this;return(0,E.A)(function*(){e.isDeletionLoading=!0;try{const a=yield e.accountDeletionService.exportUserData().toPromise();if(a){const n=window.URL.createObjectURL(a),o=document.createElement("a");o.href=n,o.download=`user-data-export-${(new Date).toISOString().split("T")[0]}.json`,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(n),e.snackBar.open("Your data has been exported and downloaded successfully!","Close",{duration:5e3,panelClass:["snack-bar-success"]})}}catch(a){console.error("Direct export failed, trying email export:",a);try{yield e.accountDeletionService.requestDataExport().toPromise(),e.snackBar.open("Your data export has been requested. You will receive an email with a download link shortly.","Close",{duration:8e3,panelClass:["snack-bar-info"]})}catch(n){console.error("Email export also failed:",n),e.snackBar.open(n.error?.message||"Failed to export data. Please try again later.","Close",{duration:5e3,panelClass:["snack-bar-error"]})}}finally{e.isDeletionLoading=!1}})()}static#t=this.\u0275fac=function(a){return new(a||i)(t.rXU(st.u),t.rXU($.f),t.rXU(Ce.T),t.rXU(ve.z),t.rXU(d.ok),t.rXU(T.UG),t.rXU(V.h),t.rXU(Pe.Ix))};static#e=this.\u0275cmp=t.VBU({type:i,selectors:[["app-profile"]],standalone:!1,decls:106,vars:40,consts:[[1,"profile-container"],[1,"container"],["animationDuration","0ms",1,"profile-tabs"],["label","Profile Information"],[1,"tab-content"],[1,"user-info"],["class","user-avatar",4,"ngIf"],[1,"user-details"],["class","oauth-info",4,"ngIf"],[1,"account-status"],[1,"status-chips"],[1,"status-chip"],["mat-button","","color","primary",3,"click",4,"ngIf"],["class","edit-profile-card",4,"ngIf"],["label","Security"],[1,"security-section"],[1,"section-header"],[1,"password-actions"],["mat-button","","color","primary",3,"click"],["mat-button","","color","accent",3,"click","disabled"],["class","oauth-password-notice",4,"ngIf"],["class","change-password-form",4,"ngIf"],["label","Two-Factor Authentication"],["label","Account Settings"],[1,"account-settings-card"],["color","warn"],["class","deletion-warning",4,"ngIf"],["class","account-actions",4,"ngIf"],[1,"account-info-section"],[1,"info-grid"],[1,"info-item"],[1,"monospace"],["class","info-item",4,"ngIf"],[1,"user-avatar"],[3,"src","alt"],[1,"oauth-info"],[1,"oauth-provider"],[1,"edit-profile-card"],[3,"ngSubmit","formGroup"],[1,"form-row"],["appearance","outline",1,"form-field"],["matInput","","formControlName","firstName","autocomplete","given-name"],[4,"ngIf"],["matInput","","formControlName","lastName","autocomplete","family-name"],["appearance","outline",1,"form-field","full-width"],["matInput","","formControlName","email","type","email","autocomplete","email"],["matInput","","formControlName","phone","type","tel","autocomplete","tel"],[1,"form-actions"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],["diameter","20",4,"ngIf"],["mat-button","","type","button",3,"click"],["diameter","20"],[1,"oauth-password-notice"],["color","primary"],[1,"change-password-form"],["class","form-field","appearance","outline",4,"ngIf"],["class","oauth-password-setup-info",4,"ngIf"],["matInput","","formControlName","newPassword","autocomplete","new-password",3,"type"],["mat-icon-button","","matSuffix","","type","button",3,"click"],["matInput","","formControlName","confirmPassword","autocomplete","new-password",3,"type"],["matInput","","formControlName","currentPassword","autocomplete","current-password",3,"type"],[1,"oauth-password-setup-info"],["matInput","","formControlName","twoFactorToken","placeholder","000000","maxlength","6","autocomplete","one-time-code"],["matSuffix",""],[1,"deletion-warning"],[1,"warning-content"],["mat-raised-button","","color","accent",3,"click","disabled"],["diameter","18",4,"ngIf"],["diameter","18"],[1,"account-actions"],[1,"action-section"],[1,"action-header"],["mat-stroked-button","","color","primary",3,"click","disabled"],[1,"action-section","danger-zone"],[1,"delete-actions"],["mat-stroked-button","","color","warn",3,"click","disabled"],["mat-raised-button","","color","warn",3,"click","disabled"],["class","inline-delete-form",4,"ngIf"],[1,"inline-delete-form"],[1,"delete-form-card"],[1,"delete-warning"],[1,"delete-form-actions"],["mat-button","",3,"click"]],template:function(a,n){1&a&&(t.j41(0,"div",0)(1,"div",1)(2,"h1"),t.EFF(3,"Profile & Security Settings"),t.k0s(),t.j41(4,"mat-tab-group",2)(5,"mat-tab",3)(6,"div",4)(7,"mat-card")(8,"mat-card-header")(9,"mat-card-title"),t.EFF(10,"User Information"),t.k0s()(),t.j41(11,"mat-card-content")(12,"div",5),t.DNE(13,ze,2,2,"div",6),t.j41(14,"div",7)(15,"p")(16,"strong"),t.EFF(17,"Name:"),t.k0s(),t.EFF(18),t.k0s(),t.j41(19,"p")(20,"strong"),t.EFF(21,"Email:"),t.k0s(),t.EFF(22),t.k0s(),t.j41(23,"p")(24,"strong"),t.EFF(25,"Phone:"),t.k0s(),t.EFF(26),t.k0s(),t.j41(27,"p")(28,"strong"),t.EFF(29,"Member since:"),t.k0s(),t.EFF(30),t.nI1(31,"date"),t.k0s(),t.DNE(32,Ye,7,5,"div",8),t.j41(33,"div",9)(34,"div",10)(35,"div",11)(36,"mat-icon"),t.EFF(37),t.k0s(),t.j41(38,"span"),t.EFF(39),t.k0s()(),t.j41(40,"div",11)(41,"mat-icon"),t.EFF(42,"security"),t.k0s(),t.j41(43,"span"),t.EFF(44),t.k0s()()()()()()(),t.j41(45,"mat-card-actions"),t.DNE(46,He,4,0,"button",12),t.k0s()(),t.DNE(47,en,32,7,"mat-card",13),t.k0s()(),t.j41(48,"mat-tab",14)(49,"div",4)(50,"mat-card")(51,"mat-card-header")(52,"mat-card-title"),t.EFF(53,"Password Settings"),t.k0s()(),t.j41(54,"mat-card-content")(55,"div",15)(56,"div",16)(57,"h3"),t.EFF(58,"Password"),t.k0s(),t.j41(59,"div",17)(60,"button",18),t.bIt("click",function(){return n.toggleChangePassword()}),t.j41(61,"mat-icon"),t.EFF(62),t.k0s(),t.EFF(63),t.k0s(),t.j41(64,"button",19),t.bIt("click",function(){return n.onForgotPassword()}),t.j41(65,"mat-icon"),t.EFF(66,"help"),t.k0s(),t.EFF(67," Forgot Password "),t.k0s()(),t.DNE(68,nn,5,1,"div",20),t.k0s(),t.DNE(69,ln,29,13,"div",21),t.k0s()()()()(),t.j41(70,"mat-tab",22)(71,"div",4),t.nrm(72,"app-two-factor-management"),t.k0s()(),t.j41(73,"mat-tab",23)(74,"div",4)(75,"mat-card",24)(76,"mat-card-header")(77,"mat-card-title")(78,"mat-icon",25),t.EFF(79,"settings"),t.k0s(),t.EFF(80," Account Management "),t.k0s()(),t.j41(81,"mat-card-content"),t.DNE(82,pn,13,4,"div",26)(83,Cn,34,8,"div",27),t.j41(84,"div",28)(85,"h4"),t.EFF(86,"Account Information"),t.k0s(),t.j41(87,"div",29)(88,"div",30)(89,"strong"),t.EFF(90,"Account ID:"),t.k0s(),t.j41(91,"span",31),t.EFF(92),t.k0s()(),t.j41(93,"div",30)(94,"strong"),t.EFF(95,"Created:"),t.k0s(),t.j41(96,"span"),t.EFF(97),t.nI1(98,"date"),t.k0s()(),t.j41(99,"div",30)(100,"strong"),t.EFF(101,"Last Updated:"),t.k0s(),t.j41(102,"span"),t.EFF(103),t.nI1(104,"date"),t.k0s()(),t.DNE(105,vn,6,5,"div",32),t.k0s()()()()()()()()()),2&a&&(t.R7$(13),t.Y8G("ngIf",null==n.currentUser?null:n.currentUser.avatarUrl),t.R7$(5),t.Lme(" ",null==n.currentUser?null:n.currentUser.firstName," ",null==n.currentUser?null:n.currentUser.lastName),t.R7$(4),t.SpI(" ",null==n.currentUser?null:n.currentUser.email),t.R7$(4),t.SpI(" ",(null==n.currentUser?null:n.currentUser.phone)||"Not provided"),t.R7$(4),t.SpI(" ",t.i5U(31,31,null==n.currentUser?null:n.currentUser.createdAt,"mediumDate")),t.R7$(2),t.Y8G("ngIf",n.isOAuthUser()),t.R7$(3),t.AVh("verified",null==n.currentUser?null:n.currentUser.emailVerified)("unverified",!(null!=n.currentUser&&n.currentUser.emailVerified)),t.R7$(2),t.JRh(null!=n.currentUser&&n.currentUser.emailVerified?"verified":"warning"),t.R7$(2),t.SpI("Email ",null!=n.currentUser&&n.currentUser.emailVerified?"Verified":"Unverified"),t.R7$(),t.AVh("enabled",n.twoFactorStatus.enabled)("disabled",!n.twoFactorStatus.enabled),t.R7$(4),t.SpI("2FA ",n.twoFactorStatus.enabled?"Enabled":"Disabled"),t.R7$(2),t.Y8G("ngIf",!n.editMode),t.R7$(),t.Y8G("ngIf",n.editMode),t.R7$(15),t.JRh(n.showChangePassword?"expand_less":"expand_more"),t.R7$(),t.SpI(" ",n.showChangePassword?"Cancel":"Change Password"," "),t.R7$(),t.Y8G("disabled",n.loading),t.R7$(4),t.Y8G("ngIf",n.isOAuthUser()),t.R7$(),t.Y8G("ngIf",n.showChangePassword),t.R7$(13),t.Y8G("ngIf",null==n.deletionStatus?null:n.deletionStatus.hasPendingDeletion),t.R7$(),t.Y8G("ngIf",!(null!=n.deletionStatus&&n.deletionStatus.hasPendingDeletion)),t.R7$(9),t.JRh(null==n.currentUser?null:n.currentUser.id),t.R7$(5),t.JRh(t.i5U(98,34,null==n.currentUser?null:n.currentUser.createdAt,"medium")),t.R7$(6),t.JRh(t.i5U(104,37,null==n.currentUser?null:n.currentUser.updatedAt,"medium")),t.R7$(2),t.Y8G("ngIf",n.isOAuthUser()))},dependencies:[h.bT,d.qT,d.me,d.BC,d.cb,d.tU,d.j4,d.JD,p.RN,p.YY,p.m2,p.MM,p.dh,y.$z,ct.M,k.An,u.j,u.M,u.c,u.b,u.g,w.fg,x.LG,W,te,N.q,Ue,h.vh],styles:[".profile-container[_ngcontent-%COMP%]{min-height:100vh;background:#f5f5f5;padding:2rem}.container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto}h1[_ngcontent-%COMP%]{color:#333;margin-bottom:2rem}mat-card[_ngcontent-%COMP%]{margin-bottom:1.5rem}.user-info[_ngcontent-%COMP%]{display:flex;gap:1.5rem;align-items:flex-start}.user-info[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;object-fit:cover;border:3px solid #e0e0e0}.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]{flex:1}.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:.5rem 0}.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#333;margin-right:.5rem}.oauth-info[_ngcontent-%COMP%]{margin-top:1rem}.oauth-info[_ngcontent-%COMP%]   .oauth-provider[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.5rem;padding:.25rem .75rem;background:#f5f5f5;border-radius:20px;font-size:.875rem}.oauth-info[_ngcontent-%COMP%]   .oauth-provider[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.account-status[_ngcontent-%COMP%]{margin-top:1rem}.account-status[_ngcontent-%COMP%]   .status-chips[_ngcontent-%COMP%]{display:flex;gap:.5rem;flex-wrap:wrap;margin-top:.5rem}.account-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;padding:.25rem .75rem;border-radius:16px;font-size:.875rem;font-weight:500}.account-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.account-status[_ngcontent-%COMP%]   .status-chip.verified[_ngcontent-%COMP%]{background:#4caf501a;color:#4caf50}.account-status[_ngcontent-%COMP%]   .status-chip.unverified[_ngcontent-%COMP%]{background:#ff98001a;color:#ff9800}.account-status[_ngcontent-%COMP%]   .status-chip.enabled[_ngcontent-%COMP%]{background:#2196f31a;color:#2196f3}.account-status[_ngcontent-%COMP%]   .status-chip.disabled[_ngcontent-%COMP%]{background:#9e9e9e1a;color:#9e9e9e}.security-section[_ngcontent-%COMP%]{margin-top:1.5rem}.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem}.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;color:#333}.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .password-actions[_ngcontent-%COMP%]{display:flex;gap:.5rem}.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .oauth-password-notice[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;color:#ff9800;font-size:.875rem}.change-password-form[_ngcontent-%COMP%]{background:#f9f9f9;padding:1.5rem;border-radius:8px;border:1px solid #e0e0e0}.change-password-form[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]{width:100%;margin-bottom:1rem}.change-password-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-top:1.5rem}.change-password-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px}.profile-tabs[_ngcontent-%COMP%]   .mat-tab-body-content[_ngcontent-%COMP%]{padding:0;overflow:visible}.profile-tabs[_ngcontent-%COMP%]   .tab-content[_ngcontent-%COMP%]{padding:1.5rem 0}.edit-profile-card[_ngcontent-%COMP%]{margin-top:1rem}.edit-profile-card[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]{display:flex;gap:1rem}.edit-profile-card[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]{flex:1}.edit-profile-card[_ngcontent-%COMP%]   .form-field.full-width[_ngcontent-%COMP%]{width:100%}.edit-profile-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-top:1rem}.edit-profile-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px}.account-settings-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.deletion-warning[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem;padding:1rem;background:#fff3cd;border:1px solid #ffeaa7;border-radius:8px;margin-bottom:1.5rem;color:#856404}.deletion-warning[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px}.deletion-warning[_ngcontent-%COMP%]   .warning-content[_ngcontent-%COMP%]{flex:1}.deletion-warning[_ngcontent-%COMP%]   .warning-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#856404}.deletion-warning[_ngcontent-%COMP%]   .warning-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:.25rem 0;font-size:.875rem}.deletion-warning[_ngcontent-%COMP%]   .warning-content[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-top:.75rem}.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1.5rem 0}.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   .action-header[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem;flex:1}.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   .action-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px;margin-top:2px}.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   .action-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 .25rem;color:#333}.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   .action-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:.875rem}.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;flex-shrink:0}.account-actions[_ngcontent-%COMP%]   .action-section.danger-zone[_ngcontent-%COMP%]   .action-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#d32f2f}.account-actions[_ngcontent-%COMP%]   .action-section.danger-zone[_ngcontent-%COMP%]   .delete-actions[_ngcontent-%COMP%]{display:flex;gap:.75rem;flex-shrink:0}.account-info-section[_ngcontent-%COMP%]{margin-top:2rem;padding-top:1.5rem;border-top:1px solid #e0e0e0}.account-info-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 1rem;color:#333}.account-info-section[_ngcontent-%COMP%]   .info-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:1rem}.account-info-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.25rem}.account-info-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-size:.875rem;color:#666;text-transform:uppercase;letter-spacing:.5px}.account-info-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.875rem;color:#333}.account-info-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   span.monospace[_ngcontent-%COMP%]{font-family:Courier New,monospace;background:#f5f5f5;padding:.25rem .5rem;border-radius:4px;font-size:.75rem}.account-info-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.25rem}.inline-delete-form[_ngcontent-%COMP%]{margin-top:1rem}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]{background:#fafafa;border:1px solid #e0e0e0}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:1rem;margin-bottom:1.5rem}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px;margin-top:2px}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#d32f2f}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:.5rem 0;font-size:.875rem;color:#666}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:.5rem 0;padding-left:1.5rem;font-size:.875rem;color:#666}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-warning[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin:.25rem 0}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:.75rem;margin-top:1rem}.inline-delete-form[_ngcontent-%COMP%]   .delete-form-card[_ngcontent-%COMP%]   .delete-form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}@media (max-width: 768px){.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .password-actions[_ngcontent-%COMP%]{flex-direction:column;gap:.25rem}.edit-profile-card[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]{flex-direction:column;gap:0}.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:1rem}.account-actions[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;justify-content:center}.info-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}"]})}return i})()},{path:"delete-account",loadComponent:()=>l.e(594).then(l.bind(l,7594)).then(i=>i.AccountDeletionComponent)}];let Mn=(()=>{class i{static#t=this.\u0275fac=function(a){return new(a||i)};static#e=this.\u0275mod=t.$C({type:i});static#n=this.\u0275inj=r.G2t({imports:[h.MD,dt.iI.forChild(Pn),d.X1,d.YN,p.Hu,y.Hl,k.m_,mt.M,w.fS,x.D6,T._T,ne,N.w,tt.g7,V.l,he,fe.M]})}return i})()}}]);