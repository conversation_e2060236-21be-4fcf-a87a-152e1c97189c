"""
Simple Account Deletion Email Test
Direct test of the account deletion email functionality
"""

import requests
import json

def test_simple_deletion_request():
    """Test account deletion request with email sending"""
    
    print("🧪 Testing Account Deletion Email")
    print("=" * 40)
    
    # Use a known test user's credentials
    BASE_URL = "http://localhost:3002"
    
    # First, let's try to login with the test email to get a token
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    print("1. Attempting login...")
    try:
        login_response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        print(f"   Login status: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print(f"   Login response: {login_response.text}")
            print("   ⚠️  Login failed, let's try with a different approach")
            
            # Let's try to create a user first
            print("\n2. Creating test user...")
            register_data = {
                "name": "Test User",
                "email": "<EMAIL>",
                "password": "password123",
                "password_confirmation": "password123"
            }
            
            register_response = requests.post(f"{BASE_URL}/api/auth/register", json=register_data)
            print(f"   Registration status: {register_response.status_code}")
            
            if register_response.status_code == 200:
                print("   ✅ User created successfully")
                # Try login again
                login_response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
                print(f"   Second login attempt: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get('access_token')
            print(f"   ✅ Authentication successful")
            
            # Now test account deletion
            print("\n3. Testing account deletion request...")
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            deletion_data = {
                "preservePaymentData": True,
                "preserveTransactionHistory": True, 
                "preserveProfileData": False,
                "preserveSecurityLogs": False,
                "customRetentionPeriod": 30,
                "reason": "Testing email functionality from script"
            }
            
            deletion_response = requests.post(
                f"{BASE_URL}/api/account/request-deletion",
                json=deletion_data,
                headers=headers
            )
            
            print(f"   Deletion request status: {deletion_response.status_code}")
            
            if deletion_response.status_code == 200:
                deletion_result = deletion_response.json()
                print(f"   ✅ Deletion request successful!")
                print(f"   📧 Response: {json.dumps(deletion_result, indent=2)}")
                
                print("\n   🔍 Check the server console logs for email sending details:")
                print("   - Look for '📧 Sending deletion confirmation email'")
                print("   - Look for Brevo API or SMTP success/failure messages")
                print("   - Check your email inbox for the confirmation email")
                
            else:
                print(f"   ❌ Deletion request failed")
                print(f"   Response: {deletion_response.text}")
        
        else:
            print(f"   ❌ Authentication failed completely")
            print(f"   Response: {login_response.text}")
            
    except Exception as e:
        print(f"   ❌ Request error: {str(e)}")
    
    print("\n" + "=" * 40)
    print("✅ Test completed!")
    print("📧 If successful, check your email inbox for the deletion confirmation.")

if __name__ == "__main__":
    test_simple_deletion_request()
