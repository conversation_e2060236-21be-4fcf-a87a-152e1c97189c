#!/usr/bin/env python3
"""
Script to delete specific users using raw SQL
"""

import os
import sys

# Add the masonite project to path
sys.path.append(os.path.join(os.path.dirname(__file__)))

# Initialize Masonite application
from wsgi import application

# Now we can import models after app initialization
from app.models.User import User
from masonite.orm import DatabaseManager

def delete_users_raw():
    """Delete specific users using raw database queries"""
    user_ids_to_delete = [255, 265]
    
    print("🧹 Starting cleanup of test users using raw database queries...")
    
    # Get database connection
    db = DatabaseManager().connection()
    
    for user_id in user_ids_to_delete:
        try:
            # First, get user info for logging
            user_info = db.table('users').where('id', user_id).first()
            
            if user_info:
                email = user_info.get('email')
                name = user_info.get('name') or 'Unknown'
                
                # Delete the user using raw query
                deleted_count = db.table('users').where('id', user_id).delete()
                
                if deleted_count > 0:
                    print(f"✅ Deleted user: {email} (ID: {user_id}, Name: {name})")
                else:
                    print(f"⚠️  No rows deleted for user ID: {user_id}")
            else:
                print(f"ℹ️  User not found with ID: {user_id}")
                
        except Exception as e:
            print(f"❌ Error deleting user ID {user_id}: {str(e)}")
    
    print("🎉 Cleanup completed!")

if __name__ == "__main__":
    delete_users_raw()
