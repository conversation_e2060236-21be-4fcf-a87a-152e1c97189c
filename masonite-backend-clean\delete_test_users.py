#!/usr/bin/env python3
"""
Script to delete specific test users from the database
For cleanup purposes only - removes test users created during development
"""

import os
import sys

# Add the masonite project to path
sys.path.append(os.path.join(os.path.dirname(__file__)))

# Initialize Masonite application
from wsgi import application

# Now we can import models after app initialization
from app.models.User import User

def delete_test_users():
    """Delete specific test users"""
    emails_to_delete = [
        '<EMAIL>',
        '<EMAIL>'
    ]
    
    print("🧹 Starting cleanup of test users...")
    
    for email in emails_to_delete:
        try:
            # Find user by email
            user = User.where('email', email).first()
            
            if user:
                user_id = user.id
                username = user.name or 'Unknown'
                
                # Delete the user
                user.delete()
                
                print(f"✅ Deleted user: {email} (ID: {user_id}, Name: {username})")
            else:
                print(f"ℹ️  User not found: {email}")
                
        except Exception as e:
            print(f"❌ Error deleting {email}: {str(e)}")
    
    print("🎉 Cleanup completed!")

if __name__ == "__main__":
    delete_test_users()
