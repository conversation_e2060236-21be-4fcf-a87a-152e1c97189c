#!/usr/bin/env python3
"""
Payment Fix Test - Test Razorpay integration after fixing test mode detection
"""

import requests
import json
import time

API_BASE = "http://localhost:3002"  # Masonite backend port

def test_payment_fix():
    print("🧪 TESTING PAYMENT FIX - RAZORPAY INTEGRATION")
    print("="*60)
    
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }    # Step 1: Login to get JWT token (using existing user)
    print("🔐 Step 1: Getting JWT token...")
    login_response = requests.post(f"{API_BASE}/api/auth/login", json={
        'email': '<EMAIL>',
        'password': 'Aaa12345@'
    }, headers=headers)
    
    if login_response.status_code == 200:
        login_data = login_response.json()
        token = login_data.get('token')
        print(f"✅ Login successful")
        print(f"   Token: {token[:20]}...")
        
        # Add authorization header
        auth_headers = {
            **headers,
            'Authorization': f'Bearer {token}'
        }        # Step 2: Test payment order creation
        print(f"\n💳 Step 2: Testing payment order creation...")
        payment_data = {
            'amount': 100.0,
            'currency': 'INR',
            'description': 'Test payment after Razorpay fix'
        }
        
        print(f"   Request data: {payment_data}")
        
        payment_response = requests.post(f"{API_BASE}/api/payments/create-order", 
                                       json=payment_data, 
                                       headers=auth_headers)
        
        print(f"   Status: {payment_response.status_code}")
        print(f"   Response: {payment_response.text}")
        
        if payment_response.status_code == 200:
            payment_result = payment_response.json()
            print(f"✅ SUCCESS: Payment order created successfully!")
            print(f"   Order ID: {payment_result.get('orderId', 'N/A')}")
            print(f"   Amount: {payment_result.get('amount', 'N/A')}")
            print(f"   Currency: {payment_result.get('currency', 'N/A')}")
            print(f"   Razorpay Key: {payment_result.get('key', 'N/A')}")
            print(f"   Test Mode: {payment_result.get('testMode', 'N/A')}")
            
            # If it's a real order (not test mode), show instructions
            if payment_result.get('testMode') == False:
                print(f"\n🎉 REAL RAZORPAY ORDER CREATED!")
                print(f"   This should work with the frontend now.")
                print(f"   Order ID: {payment_result.get('orderId')}")
                print(f"   The frontend should be able to use this order with Razorpay Checkout.")
            else:
                print(f"\n⚠️  Still in test mode - check Razorpay credentials")
        else:
            print(f"❌ Payment order creation failed:")
            try:
                error_data = payment_response.json()
                print(f"   Error: {error_data}")
            except:
                print(f"   Error: {payment_response.text}")
    else:
        print(f"❌ Login failed:")
        print(f"   Status: {login_response.status_code}")
        try:
            error_data = login_response.json()
            print(f"   Error: {error_data}")
        except:
            print(f"   Error: {login_response.text}")

if __name__ == "__main__":
    test_payment_fix()
