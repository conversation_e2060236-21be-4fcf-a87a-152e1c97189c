#!/usr/bin/env python3
"""
Test 2FA Recovery Code Functionality
Tests both enabling 2FA (generating 3 codes) and using recovery codes during login
"""

import requests
import json
import time

# Configuration
BASE_URL = 'http://localhost:3002/api'
EMAIL = '<EMAIL>'
PASSWORD = 'SecurePass123!'

def make_request(method, endpoint, data=None, headers=None):
    """Make HTTP request with error handling"""
    url = f"{BASE_URL}{endpoint}"
    try:
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, headers=headers)
        elif method.upper() == 'PUT':
            response = requests.put(url, json=data, headers=headers)
        elif method.upper() == 'DELETE':
            response = requests.delete(url, headers=headers)
        
        print(f"📊 {method.upper()} {endpoint}")
        print(f"   Status: {response.status_code}")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            response_data = response.json()
            print(f"   Response: {json.dumps(response_data, indent=2)}")
            return response.status_code, response_data
        else:
            print(f"   Response: {response.text}")
            return response.status_code, {'message': response.text}
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return 0, {'error': str(e)}

def test_2fa_recovery_codes():
    """Test complete 2FA flow with recovery codes"""
    print("🧪 Testing 2FA Recovery Code Functionality")
    print("=" * 60)
    
    # Step 1: Login
    print("\n🔑 Step 1: Login to get access token")
    status, response = make_request('POST', '/auth/login', {
        'email': EMAIL,
        'password': PASSWORD
    })
    
    if status != 200:
        print(f"❌ Login failed with status {status}")
        return False
    
    token = response.get('token')
    if not token:
        print("❌ No API token received")
        return False
    
    headers = {'Authorization': f'Bearer {token}'}
    print(f"✅ Login successful, token: {token[:20]}...")
    
    # Step 2: Check if 2FA is already enabled
    print("\n📋 Step 2: Check current 2FA status")
    status, response = make_request('GET', '/2fa/status', headers=headers)
    
    if status == 200:
        current_status = response.get('enabled', False)
        print(f"🔍 Current 2FA status: {'Enabled' if current_status else 'Disabled'}")
        
        if current_status:
            print("\n🔄 Disabling existing 2FA first...")
            # Try to disable with password
            status, response = make_request('POST', '/2fa/disable', {
                'password': PASSWORD
            }, headers=headers)
            
            if status == 200:
                print("✅ 2FA disabled successfully")
            else:
                print(f"⚠️ Failed to disable 2FA: {response}")
      # Step 3: Setup 2FA
    print("\n🔐 Step 3: Setup 2FA (should generate 3 recovery codes)")
    status, response = make_request('POST', '/2fa/setup', headers=headers)
    
    if status != 200:
        print(f"❌ 2FA setup failed with status {status}")
        return False
    
    backup_codes = response.get('backupCodes', [])
    secret = response.get('secret')
    
    print(f"✅ 2FA setup successful!")
    print(f"🔑 Secret: {secret}")
    print(f"🎯 Backup codes count: {len(backup_codes)}")
    print(f"📝 Backup codes: {backup_codes}")
    
    if len(backup_codes) != 3:
        print(f"❌ ISSUE FOUND: Expected 3 backup codes, got {len(backup_codes)}")
        print("   This matches the reported issue!")
    else:
        print("✅ Correct number of backup codes generated")
    
    # Step 4: Try to enable 2FA manually
    print("\n🔐 Step 4: Manually enable 2FA")
    print("Please enter a 6-digit TOTP code from your authenticator app or we'll use '123456' for testing:")
    
    # Use a test token for verification
    test_token = "123456"  # In real scenario, get this from TOTP app
    
    status, response = make_request('POST', '/2fa/verify', {
        'token': test_token
    }, headers=headers)
    
    if status == 200:
        print("✅ 2FA enabled successfully!")
    else:
        print(f"⚠️ 2FA verification failed (expected for test token): {response}")
        print("   Note: This is expected as we're using a test token")
    
    # Step 5: Test login with recovery codes (if any were generated)
    print("\n🔄 Step 5: Test login with recovery codes")
    
    if backup_codes:
        recovery_code = backup_codes[0]
        print(f"\n🧪 Testing recovery code: {recovery_code}")
        
        # Attempt login with recovery code
        status, response = make_request('POST', '/auth/login', {
            'email': EMAIL,
            'password': PASSWORD,
            'recoveryCode': recovery_code
        })
        
        if status == 200:
            print(f"✅ Recovery code login successful!")
        else:
            print(f"❌ Recovery code login failed: {response}")
            
            # Check for specific error messages about exhaustion
            error_message = response.get('error', {}).get('message', '')
            if 'exhausted' in error_message.lower() or 'remaining' in error_message.lower():
                print(f"🎯 FOUND RECOVERY CODE ISSUE: {error_message}")
            elif 'requires' in error_message.lower() and 'factor' in error_message.lower():
                print("✅ This is expected - 2FA is not fully enabled yet")
    
    # Step 6: Check recovery codes status
    print("\n📊 Step 6: Check recovery codes status")
    status, response = make_request('GET', '/2fa/recovery-codes', headers=headers)
    
    if status == 200:
        print("✅ Recovery codes status retrieved:")
        print(f"   Response: {json.dumps(response, indent=2)}")
    else:
        print(f"❌ Failed to get recovery codes status: {response}")
    
    print("\n" + "=" * 60)
    print("🎯 Test Summary:")
    print(f"📊 Backup codes generated: {len(backup_codes)}")
    print(f"✅ Expected count (3): {'✅ PASS' if len(backup_codes) == 3 else '❌ FAIL'}")
    
    return len(backup_codes) == 3

if __name__ == '__main__':
    try:
        success = test_2fa_recovery_codes()
        if success:
            print("\n🎉 Recovery codes test passed!")
        else:
            print("\n❌ Recovery codes test revealed issues!")
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
