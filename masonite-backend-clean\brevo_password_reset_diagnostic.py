#!/usr/bin/env python3
"""
Brevo Password Reset Email Diagnostic
Checks if emails are actually being sent via Brevo API
"""

import os
import sys
import requests
import json
import time

# Add the masonite project to path
sys.path.append(os.path.join(os.path.dirname(__file__)))

# Initialize Masonite application
from wsgi import application

from masonite.environment import env
from app.services.BrevoEmailService import BrevoEmailService
from app.models.User import User

def test_brevo_password_reset_directly():
    """Test Brevo API password reset email directly"""
    
    print("🔐 Testing Brevo Password Reset Email Directly")
    print("=" * 60)
    
    # Check if API key is configured
    api_key = env('BREVO_API_KEY')
    if not api_key or api_key == 'your-brevo-api-key-here':
        print("❌ Brevo API key not configured in .env file")
        return False
    
    print(f"🔑 API Key: {api_key[:10]}...{api_key[-4:]} (masked)")
    
    # Find or create a test user
    test_email = "<EMAIL>"
    test_user = User.where('email', test_email).first()
    
    if not test_user:
        print(f"⚠️  User {test_email} not found. Creating one...")
        test_user = User.create({
            'name': 'Password Reset Test User',
            'email': test_email,
            'password': 'hashed_password_here',
            'email_verified_at': None
        })
    
    print(f"👤 Testing with user: {test_user.email} (ID: {test_user.id})")
    
    try:
        # Test Brevo API service for password reset
        brevo_service = BrevoEmailService()
        result = brevo_service.send_password_reset_email(test_user, 'test_reset_token_123')
        
        print(f"\n📧 Brevo API Response:")
        print(f"   Success: {result['success']}")
        
        if result['success']:
            print("✅ Brevo API password reset email SUCCESSFUL!")
            print(f"📧 Email sent to: {result['to']}")
            print(f"🆔 Message ID: {result['message_id']}")
            print(f"🔧 Method: {result['method']}")
            
            print(f"\n🌐 Check Brevo Dashboard:")
            print(f"   1. Login to https://app.brevo.com")
            print(f"   2. Go to Campaigns → Transactional")
            print(f"   3. Look for message ID: {result['message_id']}")
            print(f"   4. Check delivery status")
            
            return True
        else:
            print("❌ Brevo API password reset email FAILED")
            print(f"❌ Error: {result['error']}")
            if 'status_code' in result:
                print(f"📊 Status Code: {result['status_code']}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during password reset API test: {e}")
        return False

def check_brevo_configuration():
    """Check current Brevo configuration"""
    
    print("\n🔧 Brevo Configuration Check")
    print("=" * 40)
    
    # Check environment variables
    api_key = env('BREVO_API_KEY')
    api_url = env('BREVO_API_URL', 'https://api.brevo.com/v3/smtp/email')
    mail_from = env('MAIL_FROM')
    
    print(f"📋 Configuration:")
    print(f"   BREVO_API_KEY: {'✅ SET' if api_key and api_key != 'your-brevo-api-key-here' else '❌ NOT SET'}")
    print(f"   BREVO_API_URL: {api_url}")
    print(f"   MAIL_FROM: {mail_from}")
    
    if not api_key or api_key == 'your-brevo-api-key-here':
        print("\n❌ ISSUE FOUND: Brevo API key is not properly configured")
        print("🔧 Solution:")
        print("   1. Login to your Brevo account")
        print("   2. Go to: SMTP & API → API Keys")
        print("   3. Copy your API key")
        print("   4. Update BREVO_API_KEY in .env file")
        print("   5. Restart the Masonite server")
        return False
    
    return True

def test_brevo_api_connectivity():
    """Test if we can connect to Brevo API"""
    
    print("\n🌐 Testing Brevo API Connectivity")
    print("=" * 40)
    
    api_key = env('BREVO_API_KEY')
    if not api_key or api_key == 'your-brevo-api-key-here':
        print("❌ Cannot test - API key not configured")
        return False
    
    # Test with a simple API call to check connectivity
    headers = {
        'accept': 'application/json',
        'api-key': api_key
    }
    
    try:
        # Test account info endpoint
        response = requests.get(
            'https://api.brevo.com/v3/account',
            headers=headers,
            timeout=10
        )
        
        print(f"📊 API Response Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Brevo API connectivity: WORKING")
            account_info = response.json()
            print(f"📧 Account Email: {account_info.get('email', 'N/A')}")
            print(f"🏢 Company: {account_info.get('companyName', 'N/A')}")
            return True
        elif response.status_code == 401:
            print("❌ Brevo API: UNAUTHORIZED - Invalid API key")
            return False
        else:
            print(f"⚠️  Brevo API: Unexpected response {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Brevo API connectivity error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Brevo Password Reset Email Diagnostic")
    print("=" * 60)
    
    # Check configuration
    config_ok = check_brevo_configuration()
    
    if config_ok:
        # Test API connectivity
        connectivity_ok = test_brevo_api_connectivity()
        
        if connectivity_ok:
            # Test password reset email
            email_ok = test_brevo_password_reset_directly()
            
            if email_ok:
                print("\n🎉 All checks PASSED!")
                print("📧 Password reset emails should be working")
                print("🔍 Check your Brevo dashboard for email logs")
            else:
                print("\n⚠️  Email sending failed despite good configuration")
        else:
            print("\n❌ API connectivity issues")
    else:
        print("\n❌ Configuration issues need to be resolved first")
    
    print("\n📝 If emails still don't appear in Brevo dashboard:")
    print("   1. Check email delivery settings in Brevo")
    print("   2. Verify sender domain is authenticated")
    print("   3. Check rate limits and account status")
    print("   4. Look for emails in spam/junk folder")
