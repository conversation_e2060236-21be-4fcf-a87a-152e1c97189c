#!/usr/bin/env python3
"""
Route Method Analysis Script
Find out how to get the HTTP method from Route objects
"""

import os
import sys
import importlib.util

# Add the project root to the Python path
project_root = r"c:\Users\<USER>\Downloads\study\apps\ai\cody\Modular backend secure user system and payment_Cody\masonite-backend-clean"
sys.path.insert(0, project_root)
os.chdir(project_root)

print("=== Route Method Analysis ===")

try:
    # Import the routes directly
    spec = importlib.util.spec_from_file_location("api_routes", "routes/api.py")
    api_routes_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(api_routes_module)
    
    routes = api_routes_module.ROUTES
    print(f"Loaded {len(routes)} routes")
    
    # Analyze route 118 which should be the POST /auth/login
    login_route_index = 118  # Based on the routes/api.py file structure
    
    if login_route_index < len(routes):
        route = routes[login_route_index]
        print(f"\nAnalyzing route at index {login_route_index}:")
        print(f"  Type: {type(route)}")
        print(f"  URL: {getattr(route, 'url', 'Unknown')}")
        print(f"  Controller: {getattr(route, 'controller', 'Unknown')}")
        
        # Check all attributes for method information
        all_attrs = [attr for attr in dir(route) if not attr.startswith('_')]
        print(f"  All attributes: {all_attrs}")
        
        # Check specific attributes that might contain method info
        method_attrs = ['request_method', 'method', 'methods', 'http_method', 'verb']
        for attr in method_attrs:
            value = getattr(route, attr, 'Not found')
            print(f"    {attr}: {value}")
        
        # Check if the route has compile information
        try:
            compiled = route.compile_route_to_regex()
            print(f"  Compiled route info: {compiled}")
        except Exception as e:
            print(f"  Compilation error: {e}")
    
    # Let's look for the specific login route in different ways
    print(f"\n=== Searching for POST /auth/login route ===")
    found_post_login = False
    
    for i, route in enumerate(routes):
        url = getattr(route, 'url', '')
        controller = getattr(route, 'controller', '')
        
        if '/auth/login' in url and 'AuthController@login' in controller:
            found_post_login = True
            print(f"Found POST login route at index {i}:")
            print(f"  URL: {url}")
            print(f"  Controller: {controller}")
            
            # Check all method-related attributes
            method_attrs = ['request_method', 'method', 'methods', 'http_method', 'verb']
            for attr in method_attrs:
                value = getattr(route, attr, 'Not found')
                print(f"  {attr}: {value}")
            
            # Check the route's internal data structure
            if hasattr(route, '__dict__'):
                print(f"  Route __dict__: {route.__dict__}")
            break
    
    if not found_post_login:
        print("❌ POST /auth/login route not found!")
        
        # Show all auth/login routes
        print("\nAll auth/login routes found:")
        for i, route in enumerate(routes):
            url = getattr(route, 'url', '')
            controller = getattr(route, 'controller', '')
            if '/auth/login' in url:
                print(f"  {i}: {url} -> {controller}")
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
