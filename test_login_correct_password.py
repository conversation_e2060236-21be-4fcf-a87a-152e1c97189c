"""
Test login endpoint on port 3002 with correct password
"""
import requests
import json

def test_login_endpoint():
    """Test the login endpoint with correct user credentials"""
    
    # API endpoint
    url = "http://localhost:3002/api/auth/login"
    
    # Test credentials - using the verified test user with correct password
    payload = {
        "email": "<EMAIL>",
        "password": "Aaa12345!"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Origin': 'http://localhost:3000'
    }
    
    print("🧪 Testing login endpoint with correct password...")
    print(f"📡 URL: {url}")
    print(f"📝 Payload: {payload}")
    
    try:
        # Test with a longer timeout
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"✅ Response Status: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            response_data = response.json()
            print(f"📄 Response Body: {json.dumps(response_data, indent=2)}")
            
            # Check if we got a token
            if response.status_code == 200 and 'token' in response_data:
                print("🎉 LOGIN SUCCESSFUL!")
                print(f"🔑 Token received: {response_data['token'][:50]}...")
                print(f"👤 User data: {response_data.get('user', {})}")
            elif response.status_code == 403 and 'EmailNotVerifiedError' in str(response_data):
                print("⚠️ Email not verified - this is expected if user needs verification")
            else:
                print("❌ Login failed")
        else:
            print(f"📄 Response Text: {response.text[:500]}...")
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the server running on port 3002?")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_login_endpoint()
