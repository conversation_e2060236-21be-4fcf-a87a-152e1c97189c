#!/usr/bin/env python3
"""
Simple Recovery Code Test - Use existing user or create via direct API
"""

import requests
import json
import time

# Configuration
BASE_URL = 'http://localhost:3002/api'

def quick_test():
    """Quick test of recovery code functionality"""
    print("🧪 Quick Recovery Code Test")
    print("=" * 40)
    
    # Test 1: Try to hit the server
    try:
        response = requests.get(f"{BASE_URL}/auth/me", timeout=5)
        print(f"✅ Server is responding: {response.status_code}")
    except Exception as e:
        print(f"❌ Server not responding: {e}")
        return
    
    # Test 2: Try a simple login to see existing users
    test_emails = [
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>',
        '<EMAIL>'
    ]
    
    for email in test_emails:
        try:
            login_data = {'email': email, 'password': 'SecurePass123!'}
            response = requests.post(f"{BASE_URL}/auth/login", json=login_data, timeout=5)
            print(f"📧 {email}: {response.status_code}")
            if response.status_code == 200:
                print(f"   ✅ Valid user found!")
                data = response.json()
                token = data.get('access_token') or data.get('token')
                if token:
                    # Test 2FA setup
                    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
                    setup_response = requests.post(f"{BASE_URL}/2fa/setup", json={}, headers=headers, timeout=5)
                    print(f"   2FA Setup: {setup_response.status_code}")
                    if setup_response.status_code == 200:
                        setup_data = setup_response.json()
                        backup_codes = setup_data.get('backupCodes', [])
                        print(f"   Backup codes: {len(backup_codes)}")
                        if backup_codes:
                            print(f"   First code: {backup_codes[0]}")
                            
                            # Test login with recovery code
                            recovery_login_data = {
                                'email': email,
                                'password': 'SecurePass123!',
                                'recoveryCode': backup_codes[0]
                            }
                            recovery_response = requests.post(f"{BASE_URL}/auth/login", json=recovery_login_data, timeout=5)
                            print(f"   Recovery login: {recovery_response.status_code}")
                            if recovery_response.status_code != 200:
                                print(f"   Recovery error: {recovery_response.json()}")
                    return
            elif response.status_code == 401:
                response_data = response.json()
                if 'two-factor' in str(response_data).lower():
                    print(f"   🔐 2FA required - testing recovery code")
                    # Try with a dummy recovery code to see the error
                    recovery_data = {
                        'email': email, 
                        'password': 'SecurePass123!',
                        'recoveryCode': 'ABC12345'
                    }
                    recovery_response = requests.post(f"{BASE_URL}/auth/login", json=recovery_data, timeout=5)
                    print(f"   Recovery test: {recovery_response.status_code}")
                    if recovery_response.status_code != 200:
                        print(f"   Recovery error: {recovery_response.json()}")
        except Exception as e:
            print(f"❌ Error testing {email}: {e}")

if __name__ == '__main__':
    quick_test()
