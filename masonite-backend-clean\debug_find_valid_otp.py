#!/usr/bin/env python3
"""
Debug OTP find_valid_otp method specifically
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_find_valid_otp():
    """Test the find_valid_otp method directly"""
    try:
        print("🔍 Testing OTP.find_valid_otp() method...")
        
        from app.models.OTP import OTP
        from datetime import datetime, timezone
        
        test_identifier = "<EMAIL>"
        test_type = "login"
        
        print(f"🔑 Searching for: identifier='{test_identifier}', type='{test_type}'")
        
        # Direct call to find_valid_otp
        found_otp = OTP.find_valid_otp(test_identifier, test_type)
        
        if found_otp:
            print(f"✅ Found OTP:")
            print(f"   ID: {found_otp.id}")
            print(f"   Identifier: '{found_otp.identifier}'")
            print(f"   Type: '{found_otp.otp_type}'")
            print(f"   Used: {found_otp.used}")
            print(f"   Attempts: {found_otp.attempts}")
            print(f"   Max Attempts: {found_otp.max_attempts}")
            print(f"   Expires: {found_otp.expires_at}")
            print(f"   Now: {datetime.now(timezone.utc)}")
            print(f"   Expired: {datetime.now(timezone.utc) > found_otp.expires_at}")
        else:
            print("❌ No OTP found!")
            
            # Let's debug the query step by step
            print("\n🔍 Debugging the query step by step...")
            
            # Step 1: All OTPs for this identifier
            all_otps = OTP.where('identifier', test_identifier).get()
            print(f"📊 Total OTPs for '{test_identifier}': {len(all_otps)}")
            
            # Step 2: Filter by type
            type_otps = OTP.where('identifier', test_identifier)\
                          .where('otp_type', test_type).get()
            print(f"📊 OTPs with type '{test_type}': {len(type_otps)}")
            
            # Step 3: Filter by used
            unused_otps = OTP.where('identifier', test_identifier)\
                            .where('otp_type', test_type)\
                            .where('used', False).get()
            print(f"📊 Unused OTPs: {len(unused_otps)}")
            
            # Step 4: Filter by expiration
            current_time = datetime.now(timezone.utc)
            unexpired_otps = OTP.where('identifier', test_identifier)\
                               .where('otp_type', test_type)\
                               .where('used', False)\
                               .where('expires_at', '>', current_time).get()
            print(f"📊 Unexpired OTPs: {len(unexpired_otps)}")
            
            # Step 5: Filter by attempts
            valid_attempts_otps = OTP.where('identifier', test_identifier)\
                                    .where('otp_type', test_type)\
                                    .where('used', False)\
                                    .where('expires_at', '>', current_time)\
                                    .where('attempts', '<', 3).get()
            print(f"📊 OTPs with valid attempts: {len(valid_attempts_otps)}")
            
            # Show details of any unexpired OTPs
            if unexpired_otps:
                print("\n📋 Unexpired OTP details:")
                for otp in unexpired_otps:
                    print(f"   ID {otp.id}: attempts={otp.attempts}, max={otp.max_attempts}, expires={otp.expires_at}")
                    attempts_ok = otp.attempts < otp.max_attempts  
                    print(f"   Attempts OK: {attempts_ok} ({otp.attempts} < {otp.max_attempts})")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 OTP find_valid_otp Debug Test")
    print("=" * 50)
    
    test_find_valid_otp()
