#!/usr/bin/env python3
"""
Check OTP table structure and user verification status
"""

import psycopg2
from datetime import datetime

# Database connection
try:
    conn = psycopg2.connect(
        host="localhost",
        database="masonite_secure_backend",
        user="postgres",
        password="password"
    )
    cursor = conn.cursor()
    
    print("🔍 CHECKING OTP TABLE STRUCTURE AND USER STATUS")
    print("="*60)
    
    # First check OTP table structure
    print(f"\n🗄️  OTP Table Structure:")
    print("-" * 40)
    cursor.execute("""
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'otps'
        ORDER BY ordinal_position
    """)
    
    columns = cursor.fetchall()
    if columns:
        for col in columns:
            print(f"   {col[0]}: {col[1]} (Nullable: {col[2]})")
    else:
        print("   ❌ OTP table not found or no columns")
    
    # Check users table structure for verification fields
    print(f"\n👤 Users Table Verification Fields:")
    print("-" * 40)
    cursor.execute("""
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name LIKE '%verif%'
        ORDER BY ordinal_position
    """)
    
    user_columns = cursor.fetchall()
    for col in user_columns:
        print(f"   {col[0]}: {col[1]} (Nullable: {col[2]})")
    
    # Check both test users
    test_emails = ['<EMAIL>', '<EMAIL>']
    
    for email in test_emails:
        print(f"\n📧 Checking user: {email}")
        print("-" * 40)
        
        # Get user details
        cursor.execute("""
            SELECT id, email, password, email_verified_at, 
                   email_verification_token, email_verification_expires,
                   created_at, updated_at
            FROM users 
            WHERE email = %s
        """, (email,))
        
        user = cursor.fetchone()
        
        if user:
            user_id, email, password_hash, verified_at, token, expires, created, updated = user
            print(f"   ✅ User found!")
            print(f"   ID: {user_id}")
            print(f"   Email: {email}")
            print(f"   Password Hash: {password_hash[:50]}...")
            print(f"   Email Verified At: {verified_at}")
            print(f"   Verification Token: {token}")
            print(f"   Token Expires: {expires}")
            print(f"   Created: {created}")
            print(f"   Updated: {updated}")
            
            # Get the correct column names for OTP
            cursor.execute("""
                SELECT *
                FROM otps 
                WHERE user_id = %s 
                ORDER BY created_at DESC 
                LIMIT 5
            """, (user_id,))
            
            otps = cursor.fetchall()
            
            if otps:
                print(f"   📱 Recent OTP records ({len(otps)}):")
                for i, otp in enumerate(otps):
                    print(f"      OTP {i+1}: {otp}")
            else:
                print("   📱 No OTP records found")
                
        else:
            print(f"   ❌ User not found!")
    
    cursor.close()
    conn.close()
    
    print(f"\n✅ Database check completed!")
    
except Exception as e:
    print(f"❌ Database error: {e}")
