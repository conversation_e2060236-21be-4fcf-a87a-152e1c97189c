#!/usr/bin/env python3
"""
Script to list users in the database to find the correct emails
"""

import os
import sys

# Add the masonite project to path
sys.path.append(os.path.join(os.path.dirname(__file__)))

# Initialize Masonite application
from wsgi import application

# Now we can import models after app initialization
from app.models.User import User

def list_users():
    """List all users in the database"""
    print("📋 Listing all users in the database...")
    
    try:
        users = User.all()
        
        if users.count() == 0:
            print("ℹ️  No users found in database")
            return
            
        print(f"Found {users.count()} users:")
        print("-" * 80)
        
        for user in users:
            print(f"ID: {user.id:3d} | Email: {user.email:35s} | Name: {user.name or 'N/A':20s} | Created: {user.created_at}")
            
    except Exception as e:
        print(f"❌ Error listing users: {str(e)}")

if __name__ == "__main__":
    list_users()
