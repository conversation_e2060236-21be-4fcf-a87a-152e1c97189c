"""
Check user credentials and debug login issue
"""
import os
import sys

# Add the Masonite project to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'masonite-backend-clean'))

from bootstrap.start import app
from app.models.User import User
from masoniteorm import Model

def check_user_credentials():
    """Check if the user exists and verify password"""
    
    email = "<EMAIL>"
    password = "Password123!"
    
    print(f"🔍 Checking user: {email}")
    
    # Find user
    user = User.where('email', email).first()
    
    if not user:
        print(f"❌ User not found: {email}")
        return
    
    print(f"✅ User found: {user.email} (ID: {user.id})")
    print(f"📝 User name: {user.name}")
    print(f"📝 User password hash: {user.password[:50]}...")
    print(f"📝 Email verified: {user.email_verified_at}")
    
    # Check password
    try:
        if hasattr(user, 'verify_password'):
            is_valid = user.verify_password(password)
            print(f"📝 Password valid (verify_password): {is_valid}")
        
        # Try direct bcrypt check
        import bcrypt
        if user.password:
            try:
                is_valid_bcrypt = bcrypt.checkpw(password.encode('utf-8'), user.password.encode('utf-8'))
                print(f"📝 Password valid (bcrypt): {is_valid_bcrypt}")
            except Exception as e:
                print(f"❌ Bcrypt error: {e}")
        
        # Check if user is active
        print(f"📝 User is_active: {getattr(user, 'is_active', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Password check error: {e}")

if __name__ == "__main__":
    check_user_credentials()
