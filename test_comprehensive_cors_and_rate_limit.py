#!/usr/bin/env python3
"""
Comprehensive test script for CORS and rate limit fixes.
Tests all major API endpoints and verifies proper error handling.
"""

import requests
import json
import time
from urllib.parse import urljoin

BASE_URL = "http://127.0.0.1:3002"
FRONTEND_ORIGIN = "http://localhost:4200"

def test_cors_preflight(endpoint, method="POST"):
    """Test CORS preflight request for an endpoint."""
    print(f"\n🔍 Testing CORS preflight for {endpoint} ({method})")
    
    url = urljoin(BASE_URL, endpoint)
    headers = {
        "Origin": FRONTEND_ORIGIN,
        "Access-Control-Request-Method": method,
        "Access-Control-Request-Headers": "Content-Type,Authorization"
    }
    
    try:
        response = requests.options(url, headers=headers)
        print(f"   Status: {response.status_code}")
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
            'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
        }
        
        print(f"   CORS Headers: {json.dumps(cors_headers, indent=6)}")
        
        # Check if CORS headers are present and correct
        if (cors_headers['Access-Control-Allow-Origin'] == FRONTEND_ORIGIN and
            'POST' in cors_headers.get('Access-Control-Allow-Methods', '') and
            'Content-Type' in cors_headers.get('Access-Control-Allow-Headers', '')):
            print("   ✅ CORS preflight PASSED")
            return True
        else:
            print("   ❌ CORS preflight FAILED")
            return False
            
    except Exception as e:
        print(f"   ❌ CORS preflight ERROR: {e}")
        return False

def test_actual_request(endpoint, method="POST", data=None, headers=None):
    """Test actual API request with CORS headers."""
    print(f"\n📡 Testing actual request to {endpoint} ({method})")
    
    url = urljoin(BASE_URL, endpoint)
    default_headers = {
        "Origin": FRONTEND_ORIGIN,
        "Content-Type": "application/json"
    }
    
    if headers:
        default_headers.update(headers)
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=default_headers)
        elif method.upper() == "POST":
            response = requests.post(url, headers=default_headers, json=data or {})
        elif method.upper() == "PUT":
            response = requests.put(url, headers=default_headers, json=data or {})
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=default_headers)
        
        print(f"   Status: {response.status_code}")
        print(f"   Response CORS Header: {response.headers.get('Access-Control-Allow-Origin', 'None')}")
        
        if response.headers.get('Access-Control-Allow-Origin'):
            print("   ✅ Actual request has CORS headers")
            return True
        else:
            print("   ❌ Actual request missing CORS headers")
            return False
            
    except Exception as e:
        print(f"   ❌ Actual request ERROR: {e}")
        return False

def main():
    print("🚀 Starting Comprehensive CORS and Rate Limit Tests")
    print("=" * 60)
    
    # Test endpoints that were previously failing with CORS errors
    critical_endpoints = [
        ("/api/auth/verify-email", "POST"),
        ("/api/auth/change-password", "POST"),
        ("/api/auth/reset-password", "POST"),
        ("/api/auth/login", "POST"),
        ("/api/auth/register", "POST"),
        ("/api/auth/logout", "POST"),
        ("/api/auth/refresh", "POST"),
        ("/api/auth/enable-2fa", "POST"),
        ("/api/auth/disable-2fa", "POST"),
        ("/api/auth/verify-2fa", "POST"),
        ("/api/profile/update", "PUT"),
        ("/api/profile/upload-avatar", "POST"),
        ("/api/profile/delete-account", "DELETE"),
        ("/api/payments/create-order", "POST"),
        ("/api/payments/process-payment", "POST"),
        ("/api/payments/webhook", "POST"),
        ("/api/oauth/google", "POST"),
        ("/api/oauth/github", "POST"),
        ("/api/oauth/callback", "POST"),
        ("/api/notifications/send", "POST"),
        ("/api/notifications/mark-read", "PUT"),
        ("/api/admin/users", "GET"),
        ("/api/admin/stats", "GET"),
    ]
    
    cors_results = []
    request_results = []
    
    print("\n📋 Testing CORS Preflight Requests")
    print("-" * 40)
    
    for endpoint, method in critical_endpoints:
        success = test_cors_preflight(endpoint, method)
        cors_results.append((endpoint, method, success))
    
    print("\n📋 Testing Actual API Requests")
    print("-" * 40)
    
    # Test a subset of endpoints with actual requests
    sample_endpoints = [
        ("/api/auth/verify-email", "POST", {"token": "test"}),
        ("/api/auth/login", "POST", {"email": "<EMAIL>", "password": "test"}),
        ("/api/payments/create-order", "POST", {"amount": 100}),
        ("/api/profile/update", "PUT", {"name": "Test User"}),
        ("/api/admin/users", "GET", None),
    ]
    
    for endpoint, method, data in sample_endpoints:
        success = test_actual_request(endpoint, method, data)
        request_results.append((endpoint, method, success))
    
    # Summary
    print("\n📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    cors_passed = sum(1 for _, _, success in cors_results if success)
    cors_total = len(cors_results)
    
    request_passed = sum(1 for _, _, success in request_results if success)
    request_total = len(request_results)
    
    print(f"CORS Preflight Tests: {cors_passed}/{cors_total} passed ({cors_passed/cors_total*100:.1f}%)")
    print(f"Actual Request Tests: {request_passed}/{request_total} passed ({request_passed/request_total*100:.1f}%)")
    
    if cors_passed == cors_total and request_passed == request_total:
        print("\n🎉 ALL TESTS PASSED! CORS is working correctly for all endpoints!")
    else:
        print(f"\n⚠️  Some tests failed. Check the details above.")
        
        print("\nFailed CORS Preflight Tests:")
        for endpoint, method, success in cors_results:
            if not success:
                print(f"   ❌ {endpoint} ({method})")
        
        print("\nFailed Actual Request Tests:")
        for endpoint, method, success in request_results:
            if not success:
                print(f"   ❌ {endpoint} ({method})")
    
    print("\n🔍 Rate Limit Detection Test")
    print("-" * 40)
    
    # Test rate limit detection logic (simulated)
    rate_limit_scenarios = [
        ("ERR_FAILED", False, "Network error - should NOT trigger rate limit popup"),
        ("ERR_NETWORK", False, "Network error - should NOT trigger rate limit popup"),
        ("CORS error", False, "CORS error - should NOT trigger rate limit popup"),
        ("Rate limit exceeded", True, "Rate limit message - should trigger popup"),
        ("Too many requests", True, "Rate limit message - should trigger popup"),
        ("Throttle limit reached", True, "Rate limit message - should trigger popup"),
        ("Status 429", True, "HTTP 429 status - should trigger popup"),
    ]
    
    print("\nTesting rate limit detection logic:")
    for error_msg, should_trigger, description in rate_limit_scenarios:
        # Simulate the detection logic from our frontend
        is_rate_limit = (
            "429" in error_msg or 
            any(keyword in error_msg.lower() for keyword in ["rate", "throttle", "limit exceeded", "too many"])
        )
        
        status = "✅ PASS" if is_rate_limit == should_trigger else "❌ FAIL"
        print(f"   {status} '{error_msg}' -> {is_rate_limit} ({description})")

if __name__ == "__main__":
    main()
