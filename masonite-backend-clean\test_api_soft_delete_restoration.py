import requests
import json
from datetime import datetime, timedelta
import uuid

from app.models.User import User
from app.models.AccountDeletionRecord import AccountDeletionRecord

def test_soft_deleted_user_api_scenarios():
    """Test API scenarios for soft-deleted user handling"""
    test_email = "<EMAIL>"
    
    print(f"🧪 Testing soft-deleted user API scenarios for: {test_email}")
    
    try:
        # Cleanup any existing test data
        existing_user = User.where('email', test_email).with_trashed().first()
        if existing_user:
            existing_user.force_delete()
        AccountDeletionRecord.where('email', test_email).delete()
          # 1. Create a test user
        print("\n1. Creating test user...")
        user = User.create({
            'name': 'Test User',  # Add required name field
            'email': test_email,
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'hashed_password',
            'email_verified': True,
            'phone': '+**********'
        })
        print(f"✅ User created: {user.id}")
        
        # 2. Create deletion record with preserved data
        print("\n2. Creating deletion record with preserved data...")
        deletion_id = str(uuid.uuid4())  # Generate deletion_id
        preserved_data = {
            'first_name': 'Test',
            'last_name': 'User', 
            'phone': '+**********',
            'profile_picture': None        }
        
        deletion_record = AccountDeletionRecord.create({
            'user_id': str(user.id),
            'email': test_email,
            'deletion_id': deletion_id,  # Add the required deletion_id
            'deletion_status': 'completed',
            'preserve_payment_data': False,
            'preserve_transaction_history': False,
            'preserve_profile_data': False,
            'preserve_security_logs': False,
            'custom_retention_period': 30,            'preserved_user_data': json.dumps(preserved_data),  # Convert to JSON string
            'expires_at': datetime.now() + timedelta(days=365),
            'completed_at': datetime.now(),
            'requested_at': datetime.now()  # Add required requested_at field
        })
        print(f"✅ Deletion record created: {deletion_record.id}")
        
        # 3. Soft delete the user
        print("\n3. Soft deleting user...")
        user.delete()  # Soft delete
        print("✅ User soft deleted")
        
        # 4. Test signup with soft-deleted user
        print("\n4. Testing signup with soft-deleted user...")
        signup_response = requests.post('http://localhost:3002/api/auth/signup', json={
            'firstName': 'Test',
            'lastName': 'User', 
            'email': test_email,
            'password': 'TestPassword123!',
            'confirmPassword': 'TestPassword123!'
        })
        print(f"📥 Signup Status Code: {signup_response.status_code}")
        if signup_response.status_code == 200:
            print("✅ Soft-deleted user restored successfully via signup!")
            print(f"📥 Response: {signup_response.json()}")
        else:
            print(f"❌ Signup failed: {signup_response.text}")
        
        # 5. Test OAuth with soft-deleted user
        print("\n5. Testing OAuth with soft-deleted user...")
        oauth_response = requests.post('http://localhost:3002/api/auth/oauth/google/callback', json={
            'code': 'fake_code',
            'scope': 'email profile',
            'authuser': '0',
            'prompt': 'none'
        })
        print(f"📥 OAuth Status Code: {oauth_response.status_code}")
        if oauth_response.status_code == 200:
            print("✅ Soft-deleted user restored successfully via OAuth!")
            print(f"📥 Response: {oauth_response.json()}")
        else:
            print(f"❌ OAuth failed: {oauth_response.text}")
          # 6. Test OTP login with soft-deleted user (this should work)
        print("\n6. Testing OTP login with soft-deleted user...")
        otp_response = requests.post('http://localhost:3002/api/otp/login', json={
            'identifier': test_email,
            'otp': '123456'  # Invalid OTP but should find user
        })
        print(f"📥 OTP Status Code: {otp_response.status_code}")
        if otp_response.status_code == 401:
            print("✅ OTP login found soft-deleted user (failed with invalid OTP as expected)")
        else:
            print(f"📥 OTP Response: {otp_response.text}")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Cleanup
        try:
            # Clean up deletion records first
            deletion_records = AccountDeletionRecord.where('email', test_email).get()
            for record in deletion_records:
                record.delete()
            
            # Then clean up users
            test_users = User.where('email', test_email).with_trashed().get()
            for user in test_users:
                user.force_delete()
                
            print("🧹 Test cleanup completed successfully")
        except Exception as cleanup_error:
            print(f"⚠️ Cleanup error: {cleanup_error}")

if __name__ == "__main__":
    test_soft_deleted_user_api_scenarios()
