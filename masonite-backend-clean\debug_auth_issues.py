#!/usr/bin/env python3
"""
Debug script for OTP and verification issues
Tests the specific problems mentioned by the user
"""

import requests
import json
import time

BASE_URL = "http://localhost:3002/api"

def test_otp_verification_flow():
    """Test complete OTP flow to debug verification issues"""
    print("\n🔍 Testing OTP Verification Flow...")
    
    try:
        test_email = "<EMAIL>"
        
        # Step 1: Send OTP
        print("📧 Step 1: Sending OTP...")
        otp_response = requests.post(
            f"{BASE_URL}/otp/send-email",
            json={"email": test_email, "type": "login"},
            headers={'Content-Type': 'application/json', 'Origin': 'http://localhost:4200'},
            timeout=30
        )
        
        print(f"OTP Send Status: {otp_response.status_code}")
        print(f"OTP Response: {json.dumps(otp_response.json(), indent=2)}")
        
        if otp_response.status_code != 200:
            return False
        
        # Step 2: Ask user for OTP (simulate)
        print("\n🔑 Step 2: Testing OTP Verification...")
        print("⚠️  You should have received an OTP email. Check your email.")
        
        # Test with obviously wrong OTP first
        print("Testing with invalid OTP: 000000")
        verify_response = requests.post(
            f"{BASE_URL}/otp/verify",
            json={"identifier": test_email, "code": "000000", "type": "login"},
            headers={'Content-Type': 'application/json', 'Origin': 'http://localhost:4200'},
            timeout=30
        )
        
        print(f"Invalid OTP Status: {verify_response.status_code}")
        print(f"Invalid OTP Response: {json.dumps(verify_response.json(), indent=2)}")
        
        # Test with manual OTP input
        print("\n📝 Please enter the OTP you received in email:")
        manual_otp = input("OTP Code: ").strip()
        
        if manual_otp:
            verify_response = requests.post(
                f"{BASE_URL}/otp/verify",
                json={"identifier": test_email, "code": manual_otp, "type": "login"},
                headers={'Content-Type': 'application/json', 'Origin': 'http://localhost:4200'},
                timeout=30
            )
            
            print(f"Manual OTP Status: {verify_response.status_code}")
            print(f"Manual OTP Response: {json.dumps(verify_response.json(), indent=2)}")
            
            # If valid, try login with OTP
            if verify_response.status_code == 200 and verify_response.json().get('valid'):
                print("\n🚀 Step 3: Testing OTP Login...")
                login_response = requests.post(
                    f"{BASE_URL}/otp/login",
                    json={"identifier": test_email, "code": manual_otp},
                    headers={'Content-Type': 'application/json', 'Origin': 'http://localhost:4200'},
                    timeout=30
                )
                
                print(f"OTP Login Status: {login_response.status_code}")
                print(f"OTP Login Response: {json.dumps(login_response.json(), indent=2)}")
        
        return True
        
    except Exception as e:
        print(f"❌ OTP flow test failed: {str(e)}")
        return False

def test_user_verification_status():
    """Test user verification status after registration"""
    print("\n🔍 Testing User Verification Status...")
    
    try:
        # Create unique test user
        test_email = f"debug.{int(time.time())}@example.com"
        
        # Step 1: Register
        print(f"📝 Registering: {test_email}")
        register_response = requests.post(
            f"{BASE_URL}/auth/signup",
            json={
                "firstName": "Debug",
                "lastName": "User", 
                "email": test_email,
                "password": "DebugPass123!",
                "confirmPassword": "DebugPass123!"
            },
            headers={'Content-Type': 'application/json', 'Origin': 'http://localhost:4200'},
            timeout=30
        )
        
        print(f"Registration Status: {register_response.status_code}")
        reg_data = register_response.json()
        print(f"Registration Response: {json.dumps(reg_data, indent=2)}")
        
        if register_response.status_code != 201:
            return False
        
        # Check initial verification status
        initial_verified = reg_data.get('user', {}).get('emailVerified', 'unknown')
        print(f"📊 Initial emailVerified status: {initial_verified}")
        
        # Step 2: Try login (should fail with email not verified)
        print("\n🔐 Testing login before verification...")
        login_response = requests.post(
            f"{BASE_URL}/auth/login",
            json={"email": test_email, "password": "DebugPass123!"},
            headers={'Content-Type': 'application/json', 'Origin': 'http://localhost:4200'},
            timeout=30
        )
        
        print(f"Pre-verification Login Status: {login_response.status_code}")
        login_data = login_response.json()
        print(f"Pre-verification Login Response: {json.dumps(login_data, indent=2)}")
        
        # Step 3: Test resend verification
        print("\n📬 Testing resend verification...")
        resend_response = requests.post(
            f"{BASE_URL}/auth/resend-verification",
            json={"email": test_email},
            headers={'Content-Type': 'application/json', 'Origin': 'http://localhost:4200'},
            timeout=30
        )
        
        print(f"Resend Verification Status: {resend_response.status_code}")
        print(f"Resend Response: {json.dumps(resend_response.json(), indent=2)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Verification status test failed: {str(e)}")
        return False

def test_existing_verified_user():
    """Test with an existing verified user"""
    print("\n🔍 Testing Existing User Verification Status...")
    
    try:
        # Test with your main email
        test_email = "<EMAIL>"
        
        # Step 1: Try login
        print(f"🔐 Testing login for: {test_email}")
        login_response = requests.post(
            f"{BASE_URL}/auth/login",
            json={"email": test_email, "password": "your_password_here"},  # You'll need to provide actual password
            headers={'Content-Type': 'application/json', 'Origin': 'http://localhost:4200'},
            timeout=30
        )
        
        print(f"Login Status: {login_response.status_code}")
        login_data = login_response.json()
        print(f"Login Response: {json.dumps(login_data, indent=2)}")
        
        # Check verification status in response
        if 'user' in login_data:
            email_verified = login_data['user'].get('emailVerified', 'unknown')
            print(f"📊 Current emailVerified status: {email_verified}")
        elif 'error' in login_data:
            error_details = login_data['error'].get('details', {})
            if 'emailVerified' in error_details:
                print(f"📊 Error details emailVerified: {error_details['emailVerified']}")
        
        # Step 2: Test resend for verified user
        print(f"\n📬 Testing resend for potentially verified user...")
        resend_response = requests.post(
            f"{BASE_URL}/auth/resend-verification", 
            json={"email": test_email},
            headers={'Content-Type': 'application/json', 'Origin': 'http://localhost:4200'},
            timeout=30
        )
        
        print(f"Resend for Verified Status: {resend_response.status_code}")
        print(f"Resend Response: {json.dumps(resend_response.json(), indent=2)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Existing user test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Debug Test Suite for OTP & Verification Issues")
    print("=" * 60)
    print(f"🌐 Testing against: {BASE_URL}")
    print("=" * 60)
    
    # Test 1: OTP verification flow
    otp_test = test_otp_verification_flow()
    
    # Test 2: New user verification status
    verification_test = test_user_verification_status()
    
    # Test 3: Existing user verification
    existing_test = test_existing_verified_user()
    
    print("\n" + "=" * 60)
    print("📊 Debug Results:")
    print(f"  OTP Verification Flow: {'✅ TESTED' if otp_test else '❌ FAILED'}")
    print(f"  New User Verification: {'✅ TESTED' if verification_test else '❌ FAILED'}")
    print(f"  Existing User Check: {'✅ TESTED' if existing_test else '❌ FAILED'}")
    
    print("\n🔍 Next Steps:")
    print("1. Check the OTP verification results above")
    print("2. Look for patterns in the verification status")
    print("3. Check server logs for detailed error messages")
