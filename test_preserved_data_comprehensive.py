#!/usr/bin/env python3
"""
Test with email verification and proper preserved data
"""

import requests
import json
import time

BASE_URL = "http://localhost:3002"
TEST_EMAIL = "<EMAIL>"

def test_with_verified_email():
    print("=== Step 1: Register User ===")
    data1 = {
        'email': TEST_EMAIL,
        'password': 'TestPassword123!',
        'confirmPassword': 'TestPassword123!',
        'firstName': 'Preserved',
        'lastName': 'Test'
    }
    
    response1 = requests.post(f"{BASE_URL}/api/auth/signup", json=data1)
    print(f"Registration - Status: {response1.status_code}")
    
    if response1.status_code not in [200, 201]:
        print(f"Registration failed: {response1.text}")
        return
    
    user_data = response1.json()
    user_id = user_data.get('userId')
    
    print("\n=== Step 2: Verify Email (Direct Database Approach) ===")
    # In a real test, you'd get the verification token from email
    # For testing, let's try to get a token or bypass verification
    
    # Let's try the login endpoint with email verification bypass for testing
    print("Attempting login without verification first...")
    login_data = {
        'email': TEST_EMAIL,
        'password': 'TestPassword123!'
    }
    
    response2 = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    print(f"Login attempt - Status: {response2.status_code}")
    print(f"Response: {response2.text}")
    
    if response2.status_code == 403:
        print("Email verification required - expected")
        
        # For testing purposes, let's create some mock data and then delete
        # We'll use the initial token from registration for deletion
        token = user_data.get('token')
        
        print(f"\n=== Step 3: Delete Account with Preserved Data (using initial token) ===")
        delete_data = {
            'preserve_payment_data': True,
            'preserve_transaction_history': True,
            'preserve_profile_data': True,
            'preserve_security_logs': True,
            'reason': 'Testing preserved data logic',
            'custom_retention_period': 30
        }
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {token}'
        }
        
        response3 = requests.post(f"{BASE_URL}/api/account/request-deletion", json=delete_data, headers=headers)
        print(f"Delete request - Status: {response3.status_code}")
        print(f"Response: {response3.text}")
        
        if response3.status_code != 200:
            print("Delete request failed")
            return
        
        delete_response = response3.json()
        confirmation_token = delete_response.get('confirmationToken')
        
        print(f"\n=== Step 4: Confirm Deletion ===")
        confirm_data = {
            'token': confirmation_token
        }
        
        response4 = requests.post(f"{BASE_URL}/api/account/confirm-deletion", json=confirm_data)
        print(f"Confirm deletion - Status: {response4.status_code}")
        print(f"Response: {response4.text}")
        
        if response4.status_code != 200:
            print("Deletion confirmation failed")
            return
        
        confirm_result = response4.json()
        preserved_summary = confirm_result.get('preservedDataSummary', {})
        print(f"Preserved data summary: {preserved_summary}")
        
        time.sleep(1)
        
        print(f"\n=== Step 5: Check Preserved Data ===")
        check_response = requests.get(f"{BASE_URL}/api/account/check-preserved-data/{TEST_EMAIL}")
        print(f"Check preserved data - Status: {check_response.status_code}")
        print(f"Response: {check_response.text}")
        
        print(f"\n=== Step 6: Re-register WITHOUT restorePreservedData parameter ===")
        data6 = {
            'email': TEST_EMAIL,
            'password': 'NewPassword456!',
            'confirmPassword': 'NewPassword456!',
            'firstName': 'Preserved',
            'lastName': 'ReTest'
        }
        
        response6 = requests.post(f"{BASE_URL}/api/auth/signup", json=data6)
        print(f"Re-registration (no param) - Status: {response6.status_code}")
        print(f"Response: {response6.text}")
        
        if response6.status_code == 200:
            result6 = response6.json()
            print(f"\nAnalysis:")
            print(f"  • hasPreservedData: {result6.get('hasPreservedData')}")
            print(f"  • dataCleanedUp: {result6.get('dataCleanedUp')}")
            print(f"  • message: {result6.get('message')}")
        
        time.sleep(1)
        
        # Let's test the case where data SHOULD be cleaned up
        print(f"\n=== Step 7: Delete again and re-register WITH restorePreservedData=false ===")
        
        # Login with new credentials to get token
        login_data2 = {
            'email': TEST_EMAIL,
            'password': 'NewPassword456!'
        }
        
        response7 = requests.post(f"{BASE_URL}/api/auth/login", json=login_data2)
        print(f"Login with new password - Status: {response7.status_code}")
        
        if response7.status_code == 403:
            # Email not verified, use the token from re-registration
            new_token = result6.get('token') if 'result6' in locals() else None
            
            if new_token:
                print("Using token from re-registration for deletion...")
                headers2 = {
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {new_token}'
                }
                
                response8 = requests.post(f"{BASE_URL}/api/account/request-deletion", json=delete_data, headers=headers2)
                print(f"Second delete request - Status: {response8.status_code}")
                print(f"Response: {response8.text}")
                
                if response8.status_code == 200:
                    delete_response2 = response8.json()
                    confirmation_token2 = delete_response2.get('confirmationToken')
                    
                    if confirmation_token2:
                        confirm_data2 = {'token': confirmation_token2}
                        response9 = requests.post(f"{BASE_URL}/api/account/confirm-deletion", json=confirm_data2)
                        print(f"Second deletion confirmation - Status: {response9.status_code}")
                        
                        if response9.status_code == 200:
                            time.sleep(1)
                            
                            print(f"\n=== Step 8: Re-register WITH restorePreservedData=false ===")
                            data8 = {
                                'email': TEST_EMAIL,
                                'password': 'FinalPassword789!',
                                'confirmPassword': 'FinalPassword789!',
                                'firstName': 'Final',
                                'lastName': 'Test',
                                'restorePreservedData': False
                            }
                            
                            response10 = requests.post(f"{BASE_URL}/api/auth/signup", json=data8)
                            print(f"Re-registration (restorePreservedData=false) - Status: {response10.status_code}")
                            print(f"Response: {response10.text}")
                            
                            if response10.status_code == 200:
                                result10 = response10.json()
                                print(f"\nAnalysis for explicit false:")
                                print(f"  • hasPreservedData: {result10.get('hasPreservedData')}")
                                print(f"  • dataCleanedUp: {result10.get('dataCleanedUp')}")
                                print(f"  • message: {result10.get('message')}")

if __name__ == "__main__":
    test_with_verified_email()
