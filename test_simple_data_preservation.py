#!/usr/bin/env python3
"""
Simple test for data preservation logic - one scenario at a time to avoid rate limits
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3002/api"

def test_profile_and_security_preservation():
    """Test preserving only profile and security data"""
    email = f"test.simple.{int(time.time())}@example.com"
    password = "TestPassword123!"
    
    print("🧪 Testing Profile and Security Data Preservation")
    print("=" * 60)
    
    # Step 1: Register user
    print("📝 Step 1: Registering user...")
    register_data = {
        "email": email,
        "password": password,
        "confirmPassword": password,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+1234567890"
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=register_data)
    if response.status_code not in [200, 201]:
        print(f"❌ Registration failed: {response.text}")
        return False
    
    user_data = response.json()
    user_id = user_data.get('userId') or user_data.get('user', {}).get('id')
    token = user_data.get('token')
    print(f"✅ User registered with ID: {user_id}")
    
    # Step 2: Request deletion with profile and security preservation
    print("\n📝 Step 2: Requesting deletion with profile and security preservation...")
    deletion_data = {
        "preservePaymentData": False,
        "preserveTransactionHistory": False,
        "preserveProfileData": True,
        "preserveSecurityLogs": True,
        "customRetentionPeriod": 30,
        "reason": "Testing profile and security preservation"
    }
    
    response = requests.post(f"{BASE_URL}/account/request-deletion", 
                           json=deletion_data,
                           headers={'Authorization': f'Bearer {token}'})
    
    if response.status_code != 200:
        print(f"❌ Deletion request failed: {response.text}")
        return False
    
    deletion_response = response.json()
    deletion_id = deletion_response['deletionId']
    confirmation_token = deletion_response['confirmationToken']
    print(f"✅ Deletion requested with ID: {deletion_id}")
    
    # Step 3: Confirm deletion
    print("\n📝 Step 3: Confirming deletion...")
    response = requests.post(f"{BASE_URL}/account/confirm-deletion", 
                           json={'token': confirmation_token})
    
    if response.status_code != 200:
        print(f"❌ Deletion confirmation failed: {response.text}")
        return False
    
    confirm_response = response.json()
    print("✅ Deletion confirmed")
    print(f"📄 Preserved data summary: {confirm_response.get('preservedDataSummary', {})}")
    
    # Step 4: Check preserved data
    print("\n📝 Step 4: Checking preserved data...")
    response = requests.post(f"{BASE_URL}/account/check-preserved-data", 
                           json={'email': email})
    
    if response.status_code != 200:
        print(f"❌ Check preserved data failed: {response.text}")
        return False
    
    check_response = response.json()
    has_preserved = check_response.get('hasPreservedData', False)
    preserved_summary = check_response.get('preservedDataSummary', {})
    
    print(f"📊 Has preserved data: {has_preserved}")
    print(f"📊 Preserved data summary: {preserved_summary}")
    
    # Step 5: Validate expectations
    print("\n📝 Step 5: Validating expectations...")
    
    if not has_preserved:
        print("❌ FAIL: Expected preserved data but found none")
        return False
    
    # Check that profile and security are preserved
    if 'profile' not in preserved_summary:
        print("❌ FAIL: Expected profile data to be preserved")
        return False
    
    if 'security' not in preserved_summary:
        print("❌ FAIL: Expected security data to be preserved")
        return False
    
    # Check that payment and transaction data are NOT preserved
    if 'payments' in preserved_summary:
        print("❌ FAIL: Payment data should not be preserved")
        return False
    
    if 'transactions' in preserved_summary:
        print("❌ FAIL: Transaction data should not be preserved")
        return False
    
    print("✅ PASS: Correctly preserved only profile and security data")
    
    # Step 6: Test signup behavior
    print("\n📝 Step 6: Testing signup behavior...")
    response = requests.post(f"{BASE_URL}/auth/signup", json=register_data)
    
    if response.status_code not in [200, 201]:
        print(f"❌ Second registration failed: {response.text}")
        return False
    
    signup_response = response.json()
    signup_has_preserved = signup_response.get('hasPreservedData', False)
    signup_preserved_summary = signup_response.get('preservedDataSummary', {})
    
    print(f"📊 Signup response - Has preserved data: {signup_has_preserved}")
    print(f"📊 Signup response - Preserved summary: {signup_preserved_summary}")
    
    # Validate signup behavior matches deletion behavior
    if signup_has_preserved != has_preserved:
        print(f"❌ FAIL: Signup preserved data ({signup_has_preserved}) doesn't match deletion check ({has_preserved})")
        return False
    
    if signup_preserved_summary != preserved_summary:
        print(f"❌ FAIL: Signup preserved summary doesn't match deletion check")
        print(f"   Signup: {signup_preserved_summary}")
        print(f"   Check:  {preserved_summary}")
        return False
    
    print("✅ PASS: Signup behavior matches deletion behavior")
    print("✅ PASS: All validations successful!")
    
    return True

def main():
    """Main test function"""
    print("🚀 Starting Simple Data Preservation Test")
    print("Testing profile and security data preservation only")
    print("=" * 80)
    
    success = test_profile_and_security_preservation()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 TEST PASSED! Profile and security data preservation is working correctly!")
    else:
        print("❌ TEST FAILED! Data preservation logic needs more work!")
    print("🏁 Test completed!")

if __name__ == "__main__":
    main()
