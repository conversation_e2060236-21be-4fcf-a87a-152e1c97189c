#!/usr/bin/env python3
"""
Create a test user for testing change password functionality
"""

import os
import sys
from datetime import datetime

# Set up the Masonite environment
from wsgi import application
from masonite.environment import LoadEnvironment
LoadEnvironment()

# Import models after environment setup
from app.models.User import User

def create_test_user():
    """Create a test user for API testing"""
    
    email = "<EMAIL>"
    password = "TestPassword123!"
    
    try:
        # Delete existing user if exists
        existing_user = User.where('email', email).first()
        if existing_user:
            existing_user.delete()
            print(f"✅ Deleted existing user: {email}")
        
        # Create new user
        user = User()
        user.name = "Test User"
        user.first_name = "Test"
        user.last_name = "User"
        user.email = email
        user.set_password(password)
        user.is_active = True
        user.roles = 'user'
        user.email_verified_at = datetime.now()  # Mark as verified
        user.save()
        
        print(f"✅ Created test user:")
        print(f"   Email: {email}")
        print(f"   Password: {password}")
        print(f"   ID: {user.id}")
        print(f"   Email Verified: {user.is_email_verified()}")
        
        return user
        
    except Exception as e:
        print(f"❌ Error creating test user: {e}")
        return None

if __name__ == "__main__":
    print("🚀 Creating test user for API testing...")
    user = create_test_user()
    if user:
        print("✅ Test user created successfully!")
    else:
        print("❌ Failed to create test user!")
