#!/usr/bin/env python3
"""
🧪 End-to-End Authentication Test
==================================================
Test the actual authentication endpoints that were failing:
1. OTP verification endpoint
2. Login with email verification enforcement  
3. Resend verification endpoint
"""

import requests
import json
import time

# Base URL for the API
BASE_URL = "http://localhost:3002/api"

def test_header():
    print("🧪 End-to-End Authentication Test")
    print("=" * 50)

def test_otp_verification():
    print("\n📱 Testing OTP Verification Endpoint")
    print("-" * 40)
    
    # Test with a valid OTP - we know OTP ID 51 exists and is valid
    test_data = {
        "identifier": "<EMAIL>",
        "otp": "123456",  # We'll need to get the actual code
        "type": "login"
    }
    
    print(f"🔧 Testing OTP verification with: {test_data['identifier']}")
    
    try:
        response = requests.post(f"{BASE_URL}/auth/verify-otp", json=test_data)
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Response Body: {response.text}")
        
        if response.status_code == 200:
            print("✅ OTP verification endpoint is responding correctly")
        else:
            print("❌ OTP verification endpoint has issues")
            
    except Exception as e:
        print(f"❌ Error testing OTP verification: {str(e)}")

def test_login_enforcement():
    print("\n🔐 Testing Login with Email Verification Enforcement")
    print("-" * 55)
    
    # Test 1: Login with verified user
    test_data_verified = {
        "email": "<EMAIL>",
        "password": "test123"
    }
    
    print(f"🔧 Testing login with verified user: {test_data_verified['email']}")
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=test_data_verified)
        print(f"📊 Verified User - Status: {response.status_code}")
        print(f"📊 Verified User - Response: {response.text}")
        
    except Exception as e:
        print(f"❌ Error testing verified user login: {str(e)}")
    
    # Test 2: Login with unverified user
    test_data_unverified = {
        "email": "<EMAIL>", 
        "password": "test123"
    }
    
    print(f"\n🔧 Testing login with unverified user: {test_data_unverified['email']}")
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=test_data_unverified)
        print(f"📊 Unverified User - Status: {response.status_code}")
        print(f"📊 Unverified User - Response: {response.text}")
        
    except Exception as e:
        print(f"❌ Error testing unverified user login: {str(e)}")

def test_resend_verification():
    print("\n📧 Testing Resend Verification Endpoint")
    print("-" * 40)
    
    # Test 1: Resend for verified user (should not send)
    test_data_verified = {
        "email": "<EMAIL>"
    }
    
    print(f"🔧 Testing resend verification for verified user: {test_data_verified['email']}")
    
    try:
        response = requests.post(f"{BASE_URL}/auth/resend-verification", json=test_data_verified)
        print(f"📊 Verified User - Status: {response.status_code}")
        print(f"📊 Verified User - Response: {response.text}")
        
    except Exception as e:
        print(f"❌ Error testing resend for verified user: {str(e)}")
    
    # Test 2: Resend for unverified user (should send)
    test_data_unverified = {
        "email": "<EMAIL>"
    }
    
    print(f"\n🔧 Testing resend verification for unverified user: {test_data_unverified['email']}")
    
    try:
        response = requests.post(f"{BASE_URL}/auth/resend-verification", json=test_data_unverified)
        print(f"📊 Unverified User - Status: {response.status_code}")
        print(f"📊 Unverified User - Response: {response.text}")
        
    except Exception as e:
        print(f"❌ Error testing resend for unverified user: {str(e)}")

def test_server_connectivity():
    print("\n🌐 Testing Server Connectivity")
    print("-" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"📊 Health Check - Status: {response.status_code}")
        print(f"📊 Health Check - Response: {response.text}")
        return True
    except Exception as e:
        print(f"❌ Server not accessible: {str(e)}")
        print(f"🚨 Please make sure the server is running on localhost:8001")
        return False

def main():
    test_header()
    
    # Check if server is running
    if not test_server_connectivity():
        print(f"\n❌ Cannot proceed with tests - server not accessible")
        print(f"💡 Run: python craft serve --port 8001")
        return
    
    # Run all endpoint tests
    test_otp_verification()
    test_login_enforcement()
    test_resend_verification()
    
    print(f"\n✅ End-to-end testing complete!")
    print(f"🔧 Check the responses above to verify fixes are working")

if __name__ == '__main__':
    main()
