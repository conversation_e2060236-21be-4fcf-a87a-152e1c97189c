#!/usr/bin/env python3
"""
Test Password Reset Email Functionality
"""

import requests
import json
import time

def test_forgot_password():
    """Test the forgot password endpoint and email sending"""
    
    print("🔐 Testing Forgot Password Email Functionality")
    print("=" * 60)
    
    # Test with a known user email
    test_emails = [
        "<EMAIL>",  # Recent test user
        "<EMAIL>",  # Existing test user
        "<EMAIL>"  # General test user
    ]
    
    headers = {
        "Content-Type": "application/json",
        "Origin": "http://localhost:4200"
    }
    
    for email in test_emails:
        print(f"\n📧 Testing password reset for: {email}")
        
        test_data = {
            "email": email
        }
        
        try:
            response = requests.post(
                "http://localhost:3002/api/auth/forgot-password",
                json=test_data,
                headers=headers,
                timeout=10
            )
            
            print(f"📊 Status Code: {response.status_code}")
            print(f"📝 Response: {response.text}")
            
            if response.status_code == 200:
                print("✅ Request processed successfully")
                print("📨 Check server console for email sending logs:")
                print("   Look for: '✅ Password reset email sent via Brevo API' or '✅ Password reset email sent via SMTP'")
                
                # If Brevo API is working, email should be sent
                print("🔍 Check your Brevo dashboard for email delivery status")
                
                return True
                
            elif response.status_code == 422:
                print("⚠️  Validation error")
                try:
                    error_data = response.json()
                    print(f"Error details: {error_data}")
                except:
                    pass
                    
            else:
                print(f"❌ Unexpected status code: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print("❌ Connection Error: Is the Masonite server running?")
            print("💡 Start server with: conda activate masonite-secure-env; python craft serve --port 8001")
            return False
            
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return False

def test_invalid_email():
    """Test with invalid email format"""
    
    print("\n🧪 Testing Invalid Email Format")
    print("=" * 40)
    
    test_data = {
        "email": "invalid-email-format"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Origin": "http://localhost:4200"
    }
    
    try:
        response = requests.post(
            "http://localhost:3002/api/auth/forgot-password",
            json=test_data,
            headers=headers,
            timeout=10
        )
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 422:
            print("✅ Validation working correctly - invalid email rejected")
            try:
                error_data = response.json()
                print(f"📝 Error response: {error_data}")
            except:
                pass
            return True
        else:
            print(f"⚠️  Expected 422, got {response.status_code}")
            print(f"📝 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_missing_email():
    """Test with missing email field"""
    
    print("\n🧪 Testing Missing Email Field")
    print("=" * 40)
    
    test_data = {}  # Empty data
    
    headers = {
        "Content-Type": "application/json",
        "Origin": "http://localhost:4200"
    }
    
    try:
        response = requests.post(
            "http://localhost:3002/api/auth/forgot-password",
            json=test_data,
            headers=headers,
            timeout=10
        )
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 422:
            print("✅ Validation working correctly - missing email rejected")
            try:
                error_data = response.json()
                print(f"📝 Error response: {error_data}")
            except:
                pass
            return True
        else:
            print(f"⚠️  Expected 422, got {response.status_code}")
            print(f"📝 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Password Reset Email Test Suite")
    print("=" * 60)
    
    # Test valid email
    success1 = test_forgot_password()
    
    # Test invalid email format
    success2 = test_invalid_email()
    
    # Test missing email
    success3 = test_missing_email()
    
    print("\n" + "=" * 60)
    print("📋 Test Results Summary:")
    print(f"   Valid Email Test: {'✅ PASSED' if success1 else '❌ FAILED'}")
    print(f"   Invalid Email Test: {'✅ PASSED' if success2 else '❌ FAILED'}")
    print(f"   Missing Email Test: {'✅ PASSED' if success3 else '❌ FAILED'}")
    
    if success1 and success2 and success3:
        print("\n🎉 All password reset tests PASSED!")
        print("📧 Check server logs and Brevo dashboard for email delivery")
    else:
        print("\n⚠️  Some tests failed. Check server logs and configuration.")
    
    print("\n📝 Troubleshooting:")
    print("   1. Ensure Masonite server is running")
    print("   2. Check BREVO_API_KEY in .env file")
    print("   3. Verify user exists in database")
    print("   4. Check server console for detailed error logs")
