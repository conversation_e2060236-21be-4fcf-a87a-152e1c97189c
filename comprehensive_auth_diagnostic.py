#!/usr/bin/env python3
"""
Comprehensive Fix for Authentication Issues
1. OTP always says invalid
2. User shows unverified after login 
3. Resend verification not working
"""

import requests
import json
from datetime import datetime
import time

def print_section(title):
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def test_user_login_and_verification_status(email, password):
    """Test user login and check verification status"""
    print_section(f"Testing Login for {email}")
    
    # Test login
    login_url = "http://localhost:3002/api/auth/login"
    login_data = {
        "email": email,
        "password": password
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }
    
    try:
        print(f"🔑 Attempting login for {email}...")
        response = requests.post(login_url, json=login_data, headers=headers, timeout=15)
        
        print(f"📊 Login Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            user = data.get('user', {})
            print(f"✅ Login successful!")
            print(f"   User ID: {user.get('id')}")
            print(f"   Email: {user.get('email')}")
            print(f"   Email Verified: {user.get('emailVerified')}")
            print(f"   2FA Enabled: {user.get('twoFactorEnabled')}")
            return data.get('token'), user
            
        elif response.status_code == 403:
            data = response.json()
            print(f"⚠️  Email not verified!")
            print(f"   Error: {data.get('error', {}).get('message')}")
            print(f"   Details: {data.get('error', {}).get('details')}")
            return None, data.get('error', {}).get('details', {})
            
        else:
            try:
                error_data = response.json()
                print(f"❌ Login failed: {error_data}")
            except:
                print(f"❌ Login failed: {response.text}")
            return None, None
            
    except Exception as e:
        print(f"❌ Login request failed: {e}")
        return None, None

def test_resend_verification(email):
    """Test resend verification functionality"""
    print_section(f"Testing Resend Verification for {email}")
    
    resend_url = "http://localhost:3002/api/auth/resend-verification"
    resend_data = {
        "email": email
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }
    
    try:
        print(f"📧 Attempting to resend verification for {email}...")
        response = requests.post(resend_url, json=resend_data, headers=headers, timeout=15)
        
        print(f"📊 Resend Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Resend successful!")
            print(f"   Message: {data.get('message')}")
            return True
            
        elif response.status_code == 400:
            data = response.json()
            print(f"⚠️  Email already verified!")
            print(f"   Error: {data.get('error', {}).get('message')}")
            return False
            
        else:
            try:
                error_data = response.json()
                print(f"❌ Resend failed: {error_data}")
            except:
                print(f"❌ Resend failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Resend request failed: {e}")
        return False

def test_otp_flow(email):
    """Test OTP send and verification flow"""
    print_section(f"Testing OTP Flow for {email}")
    
    # Step 1: Send OTP
    send_url = "http://localhost:3002/api/otp/send"
    send_data = {
        "email": email,
        "type": "login"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }
    
    try:
        print(f"📱 Sending OTP to {email}...")
        response = requests.post(send_url, json=send_data, headers=headers, timeout=15)
        
        print(f"📊 OTP Send Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ OTP sent successfully!")
            print(f"   Message: {data.get('message')}")
            print(f"   Sent to: {data.get('sentTo')}")
            print(f"   Method: {data.get('method')}")
            print(f"   Expires in: {data.get('expiresIn')}")
            
            # Step 2: Ask for OTP code
            otp_code = input("\n🔢 Enter the OTP code you received: ").strip()
            
            if otp_code:
                return test_otp_verification(email, otp_code)
            else:
                print("❌ No OTP code provided")
                return False
                
        else:
            try:
                error_data = response.json()
                print(f"❌ OTP send failed: {error_data}")
            except:
                print(f"❌ OTP send failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ OTP send request failed: {e}")
        return False

def test_otp_verification(email, otp_code):
    """Test OTP verification"""
    print_section(f"Testing OTP Verification for {email}")
    
    verify_url = "http://localhost:3002/api/otp/verify"
    verify_data = {
        "identifier": email,
        "code": otp_code,
        "type": "login"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }
    
    try:
        print(f"🔍 Verifying OTP {otp_code} for {email}...")
        response = requests.post(verify_url, json=verify_data, headers=headers, timeout=15)
        
        print(f"📊 OTP Verify Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ OTP verification response!")
            print(f"   Valid: {data.get('valid')}")
            print(f"   Message: {data.get('message')}")
            if not data.get('valid'):
                print(f"   Remaining attempts: {data.get('remainingAttempts')}")
            return data.get('valid', False)
            
        else:
            try:
                error_data = response.json()
                print(f"❌ OTP verification failed: {error_data}")
            except:
                print(f"❌ OTP verification failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ OTP verification request failed: {e}")
        return False

def manually_verify_user(email):
    """Instructions for manually verifying user in database"""
    print_section(f"Manual Verification Instructions for {email}")
    
    print(f"🔧 To manually verify {email} in the database:")
    print(f"")
    print(f"1. Connect to PostgreSQL:")
    print(f"   psql -U postgres -h localhost -d masonite_secure_backend")
    print(f"")
    print(f"2. Update user verification status:")
    print(f"   UPDATE users SET ")
    print(f"     email_verified_at = NOW(),")
    print(f"     email_verification_token = NULL,")
    print(f"     email_verification_expires = NULL")
    print(f"   WHERE email = '{email}';")
    print(f"")
    print(f"3. Verify the update:")
    print(f"   SELECT id, email, email_verified_at FROM users WHERE email = '{email}';")

def check_server_logs():
    """Instructions for checking server logs"""
    print_section("Server Log Monitoring")
    
    print("🔍 To monitor server logs for debugging:")
    print("")
    print("1. The Masonite server should be running in another terminal")
    print("2. Watch for these log patterns:")
    print("   - '📥 Registration request from...' - Registration attempts")
    print("   - '✅ Email sent via...' - Email sending success")
    print("   - '🔍 OTP Send Request...' - OTP generation")
    print("   - '🔍 OTP Verification...' - OTP verification attempts")
    print("   - '❌ OTP Verification Error...' - OTP validation failures")
    print("")
    print("3. Common issues to look for:")
    print("   - No OTP logs = OTP service not working")
    print("   - OTP generation but verification fails = validation logic issue")
    print("   - Email verification errors = mail service configuration")

def main():
    """Run comprehensive diagnostic"""
    print("🔍 COMPREHENSIVE AUTHENTICATION DIAGNOSTIC")
    print("=" * 60)
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test both user accounts
    users_to_test = [
        ("<EMAIL>", "Aaa12345!"),
        ("<EMAIL>", "Aaa12345!")  # Assuming password reset or known
    ]
    
    for email, password in users_to_test:
        print(f"\n🧪 Testing user: {email}")
        print("-" * 50)
        
        # Test 1: Login and check verification status
        token, user_data = test_user_login_and_verification_status(email, password)
        
        if token:
            print(f"✅ User {email} is verified and can login successfully")
        else:
            print(f"⚠️  User {email} has verification issues")
            
            # Test 2: Try resend verification
            resend_success = test_resend_verification(email)
            
            if not resend_success:
                # Provide manual verification instructions
                manually_verify_user(email)
        
        # Test 3: OTP flow (regardless of verification status)
        print(f"\n🔢 Testing OTP flow for {email}...")
        otp_success = test_otp_flow(email)
        
        if otp_success:
            print(f"✅ OTP flow working for {email}")
        else:
            print(f"❌ OTP flow has issues for {email}")
    
    # Provide additional debugging instructions
    check_server_logs()
    
    print(f"\n🔍 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    print("✅ Tests completed. Check the results above for specific issues.")
    print("🔧 If issues persist, use the manual verification SQL commands.")
    print("📧 Check email inboxes for actual email delivery.")
    print("🎯 Monitor server logs for detailed error information.")

if __name__ == "__main__":
    main()
