#!/usr/bin/env python3
"""
Comprehensive authentication test suite
Tests all the issues mentioned by the user
"""

import requests
import json
import time

BASE_URL = "http://localhost:3002/api"

def test_otp_login_flow():
    """Test complete OTP login flow"""
    print("\n🔍 Testing OTP Login Flow...")
    
    try:
        # Step 1: Send OTP
        otp_payload = {
            "email": "<EMAIL>",
            "type": "login"
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Origin': 'http://localhost:4200'
        }
        
        print("📧 Sending OTP...")
        response = requests.post(
            f"{BASE_URL}/otp/send-email",
            json=otp_payload,
            headers=headers,
            timeout=30
        )
        
        print(f"OTP Send Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ OTP Response: {data}")
            return True
        else:
            print(f"❌ OTP Failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ OTP test failed: {str(e)}")
        return False

def test_registration_and_email_verification():
    """Test registration and email verification flow"""
    print("\n🔍 Testing Registration & Email Verification...")
    
    try:
        # Use a unique email for testing
        test_email = f"test.{int(time.time())}@example.com"
        
        # Step 1: Register
        register_payload = {
            "firstName": "Test",
            "lastName": "User",
            "email": test_email,
            "password": "TestPass123!",
            "confirmPassword": "TestPass123!"
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Origin': 'http://localhost:4200'
        }
        
        print(f"📝 Registering user: {test_email}")
        response = requests.post(
            f"{BASE_URL}/auth/signup",
            json=register_payload,
            headers=headers,
            timeout=30
        )
        
        print(f"Registration Status: {response.status_code}")
        if response.status_code == 201:
            data = response.json()
            print(f"✅ Registration Response: {data}")
            
            # Step 2: Try to login before verification
            login_payload = {
                "email": test_email,
                "password": "TestPass123!"
            }
            
            print("🔐 Attempting login before email verification...")
            login_response = requests.post(
                f"{BASE_URL}/auth/login",
                json=login_payload,
                headers=headers,
                timeout=30
            )
            
            print(f"Login Before Verification Status: {login_response.status_code}")
            login_data = login_response.json()
            print(f"Login Response: {json.dumps(login_data, indent=2)}")
            
            return True
        else:
            print(f"❌ Registration Failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Registration test failed: {str(e)}")
        return False

def test_forgot_password():
    """Test forgot password functionality"""
    print("\n🔍 Testing Forgot Password...")
    
    try:
        payload = {
            "email": "<EMAIL>"
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Origin': 'http://localhost:4200'
        }
        
        print("🔑 Sending forgot password request...")
        response = requests.post(
            f"{BASE_URL}/auth/forgot-password",
            json=payload,
            headers=headers,
            timeout=30
        )
        
        print(f"Forgot Password Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Response: {data}")
            return True
        else:
            print(f"❌ Failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Forgot password test failed: {str(e)}")
        return False

def test_cors_functionality():
    """Test CORS headers for all authentication endpoints"""
    print("\n🔍 Testing CORS Headers...")
    
    endpoints = [
        '/auth/login',
        '/auth/signup', 
        '/auth/verify-email',
        '/auth/forgot-password',
        '/otp/send-email',
        '/auth/verify-2fa'
    ]
    
    cors_results = []
    
    for endpoint in endpoints:
        try:
            response = requests.options(f"{BASE_URL}{endpoint}", headers={
                'Origin': 'http://localhost:4200',
                'Access-Control-Request-Method': 'POST'
            })
            
            cors_ok = (
                response.status_code == 200 and
                'Access-Control-Allow-Origin' in response.headers
            )
            
            cors_results.append({
                'endpoint': endpoint,
                'status': '✅' if cors_ok else '❌',
                'cors_headers': cors_ok
            })
            
        except Exception as e:
            cors_results.append({
                'endpoint': endpoint,
                'status': '❌',
                'error': str(e)
            })
    
    print("CORS Results:")
    for result in cors_results:
        print(f"  {result['endpoint']}: {result['status']}")
    
    return all(result.get('cors_headers', False) for result in cors_results)

if __name__ == "__main__":
    print("🧪 Comprehensive Authentication Test Suite")
    print("=" * 60)
    print(f"🌐 Testing against: {BASE_URL}")
    print("=" * 60)
    
    # Test 1: OTP Login
    otp_test = test_otp_login_flow()
    
    # Test 2: Registration & Email Verification
    reg_test = test_registration_and_email_verification()
    
    # Test 3: Forgot Password  
    forgot_test = test_forgot_password()
    
    # Test 4: CORS
    cors_test = test_cors_functionality()
    
    print("\n" + "=" * 60)
    print("📊 Final Test Results:")
    print(f"  OTP Login Flow: {'✅ PASS' if otp_test else '❌ FAIL'}")
    print(f"  Registration & Verification: {'✅ PASS' if reg_test else '❌ FAIL'}")  
    print(f"  Forgot Password: {'✅ PASS' if forgot_test else '❌ FAIL'}")
    print(f"  CORS Headers: {'✅ PASS' if cors_test else '❌ FAIL'}")
    
    total_passed = sum([otp_test, reg_test, forgot_test, cors_test])
    print(f"\n🎯 Overall Score: {total_passed}/4 tests passed")
    
    if total_passed == 4:
        print("\n🎉 All authentication functionality is working correctly!")
    else:
        print(f"\n⚠️  {4 - total_passed} issue(s) still need attention.")
