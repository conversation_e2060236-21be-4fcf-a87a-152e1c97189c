#!/usr/bin/env python3
"""
Test script to verify the soft-deleted user restoration fixes
"""

import os
import sys
import requests
import json

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Test configuration
BASE_URL = "http://localhost:3002/api"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "TestPassword123!"

def test_signup_with_soft_deleted_user():
    """Test signup process with a soft-deleted user that has preserved data"""
    print("🔍 Testing signup with soft-deleted user...")
    
    # Test data
    signup_data = {
        "firstName": "Test",
        "lastName": "User",
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD,
        "confirmPassword": TEST_PASSWORD
    }
    
    headers = {
        "Content-Type": "application/json",
        "Origin": "http://localhost:4200"
    }
    
    try:        response = requests.post(
            f"{BASE_URL}/auth/signup",  # Changed from /auth/register to /auth/signup
            json=signup_data,
            headers=headers,
            timeout=10
        )
        
        print(f"📥 Status Code: {response.status_code}")
        print(f"📥 Response Headers: {dict(response.headers)}")
        
        if response.status_code in [200, 422]:
            response_data = response.json()
            print(f"📥 Response Data: {json.dumps(response_data, indent=2)}")
            
            if response.status_code == 200:
                print("✅ Signup successful - user restored!")
                return True
            elif "preserved data" in response_data.get("message", "").lower():
                print("✅ Preserved data detected correctly!")
                return True
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print(f"📥 Response: {response.text}")
        
    except Exception as e:
        print(f"❌ Error testing signup: {e}")
    
    return False

def test_oauth_with_soft_deleted_user():
    """Test OAuth callback with soft-deleted user"""
    print("🔍 Testing OAuth callback with soft-deleted user...")
    
    # Simulate OAuth callback data
    oauth_data = {
        "code": "test_auth_code"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Origin": "http://localhost:4200"
    }
    
    try:        response = requests.post(
            f"{BASE_URL}/auth/oauth/google/callback",  # Fixed route path
            json=oauth_data,
            headers=headers,
            timeout=10
        )
        
        print(f"📥 OAuth Status Code: {response.status_code}")
        print(f"📥 OAuth Response: {response.text[:500]}...")
        
        # We expect this to fail with the test code, but it should not be a 500 error
        if response.status_code != 500:
            print("✅ OAuth handled gracefully (expected with test code)")
            return True
        
    except Exception as e:
        print(f"❌ Error testing OAuth: {e}")
    
    return False

def test_otp_login_with_soft_deleted_user():
    """Test OTP login with soft-deleted user"""
    print("🔍 Testing OTP login with soft-deleted user...")
    
    # Test OTP login data
    otp_data = {
        "identifier": TEST_EMAIL,
        "code": "123456"  # This will fail but should handle soft-deleted users
    }
    
    headers = {
        "Content-Type": "application/json",
        "Origin": "http://localhost:4200"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/otp/login",
            json=otp_data,
            headers=headers,
            timeout=10
        )
        
        print(f"📥 OTP Status Code: {response.status_code}")
        print(f"📥 OTP Response: {response.text[:500]}...")
        
        # We expect this to fail with invalid OTP, but it should find the user first
        if response.status_code == 401:
            response_data = response.json()
            if "User not found" not in response_data.get("error", {}).get("message", ""):
                print("✅ OTP login finds user (fails with invalid OTP as expected)")
                return True
        
    except Exception as e:
        print(f"❌ Error testing OTP login: {e}")
    
    return False

def main():
    print("🚀 Starting soft-deleted user restoration tests...")
    print(f"🎯 Target: {BASE_URL}")
    print(f"📧 Test Email: {TEST_EMAIL}")
    print("=" * 60)
    
    results = []
    
    # Test 1: Signup with soft-deleted user
    results.append(test_signup_with_soft_deleted_user())
    print()
    
    # Test 2: OAuth with soft-deleted user
    results.append(test_oauth_with_soft_deleted_user())
    print()
    
    # Test 3: OTP login with soft-deleted user
    results.append(test_otp_login_with_soft_deleted_user())
    print()
    
    # Summary
    print("=" * 60)
    print("📊 Test Results Summary:")
    tests = ["Signup", "OAuth", "OTP Login"]
    for i, result in enumerate(results):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {tests[i]}: {status}")
    
    total_passed = sum(results)
    print(f"\n🎯 Overall: {total_passed}/{len(results)} tests passed")
    
    if total_passed == len(results):
        print("🎉 All tests passed! Soft-deleted user restoration is working.")
    else:
        print("⚠️ Some tests failed. Check the server logs for details.")

if __name__ == "__main__":
    main()
