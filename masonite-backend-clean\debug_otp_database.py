#!/usr/bin/env python3
"""
Debug OTP database and verification logic
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_otp_database():
    """Check OTP records in database"""
    try:
        print("🔍 Checking OTP Database Records...")
        
        # Import models
        from app.models.OTP import OTP
        from datetime import datetime, timezone
        
        # Get recent OTP records
        recent_otps = OTP.where('created_at', '>', datetime.now(timezone.utc).replace(hour=0, minute=0, second=0))\
                         .order_by('created_at', 'desc')\
                         .limit(10)\
                         .get()
        
        print(f"📊 Found {len(recent_otps)} OTP records from today")
        
        for i, otp in enumerate(recent_otps):
            print(f"\n📋 OTP Record #{i+1}:")
            print(f"   ID: {otp.id}")
            print(f"   Identifier: {otp.identifier}")
            print(f"   Type: {otp.otp_type}")
            print(f"   Delivery: {otp.delivery_method}")
            print(f"   Created: {otp.created_at}")
            print(f"   Expires: {otp.expires_at}")
            print(f"   Used: {otp.used}")
            print(f"   Attempts: {otp.attempts}")
            print(f"   Max Attempts: {otp.max_attempts}")
            print(f"   Code Hash: {otp.code_hash[:20]}...")
            
            # Check if this OTP is still valid
            is_expired = datetime.now(timezone.utc) > otp.expires_at
            max_attempts_reached = otp.attempts >= otp.max_attempts
            
            print(f"   Status: {'✅ VALID' if not otp.used and not is_expired and not max_attempts_reached else '❌ INVALID'}")
            if is_expired:
                print(f"   ⚠️  EXPIRED")
            if max_attempts_reached:
                print(f"   ⚠️  MAX ATTEMPTS REACHED")
            if otp.used:
                print(f"   ⚠️  ALREADY USED")
        
        # Test with manual OTP verification
        print(f"\n🧪 Testing OTP Verification Logic...")
        if recent_otps:
            latest_otp = recent_otps[0]
            print(f"Testing with latest OTP (ID: {latest_otp.id})")
            
            # Ask for manual code input
            print("Enter the OTP code you received:")
            manual_code = input("OTP: ").strip()
            
            if manual_code:
                print(f"🔑 Testing code: {manual_code}")
                
                # Test the password context verification directly
                try:
                    from passlib.context import CryptContext
                    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
                    
                    print(f"🔍 Hash verification test...")
                    is_valid_hash = pwd_context.verify(manual_code, latest_otp.code_hash)
                    print(f"   Hash verification result: {is_valid_hash}")
                    
                    # Test the OTP model method
                    print(f"🔍 OTP model verification test...")
                    is_valid_model = latest_otp.verify_code(manual_code)
                    print(f"   Model verification result: {is_valid_model}")
                    
                    # Test the service method
                    print(f"🔍 Service verification test...")
                    from app.services.OTPService import OTPService
                    otp_service = OTPService()
                    service_result = otp_service.verify_otp(latest_otp.identifier, manual_code, latest_otp.otp_type)
                    print(f"   Service verification result: {service_result}")
                    
                except Exception as e:
                    print(f"❌ Verification test error: {str(e)}")
                    import traceback
                    traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 OTP Database Debug Test")
    print("=" * 50)
    
    test_otp_database()
