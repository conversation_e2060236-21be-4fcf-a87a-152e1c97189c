#!/usr/bin/env python3
"""
Comprehensive test for 422 error handling in Masonite backend
Tests both signup and change-password endpoints
"""

import requests
import json

BASE_URL = "http://localhost:3002/api"

def test_signup_422():
    """Test signup endpoint returns 422 for invalid data"""
    print("🧪 Testing signup 422 error handling...")
    
    # Test with missing required fields
    malformed_data = {
        "email": "<EMAIL>",
        "password": "Test123!"
        # Missing: firstName, lastName, confirmPassword, username
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/signup", 
                               json=malformed_data, 
                               headers=headers)
        
        if response.status_code == 422:
            print("✅ Signup correctly returns 422 for invalid data")
            error_data = response.json()
            print(f"   Error details: {error_data.get('error', {}).get('details', {})}")
            return True
        else:
            print(f"❌ Expected 422 but got {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing signup: {e}")
        return False

def test_change_password_422():
    """Test change-password endpoint returns 422 for invalid data"""
    print("🧪 Testing change-password 422 error handling...")
    
    # First create a valid user
    valid_signup = {
        "email": "<EMAIL>",
        "password": "Test123!",
        "confirmPassword": "Test123!",
        "username": "testchange4",
        "firstName": "Test",
        "lastName": "User"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }
    
    try:
        # Create user
        signup_response = requests.post(f"{BASE_URL}/auth/signup", 
                                      json=valid_signup, 
                                      headers=headers)
        
        if signup_response.status_code != 201:
            print(f"❌ Failed to create test user: {signup_response.status_code}")
            return False
            
        token = signup_response.json().get('token')
        if not token:
            print("❌ No token received from signup")
            return False
            
        # Test change password with missing currentPassword
        malformed_change = {
            "newPassword": "NewPass456!"
            # Missing: currentPassword
        }
        
        auth_headers = headers.copy()
        auth_headers['Authorization'] = f'Bearer {token}'
        
        change_response = requests.post(f"{BASE_URL}/auth/change-password",
                                      json=malformed_change,
                                      headers=auth_headers)
        
        if change_response.status_code == 422:
            print("✅ Change-password correctly returns 422 for invalid data")
            error_data = change_response.json()
            print(f"   Error details: {error_data.get('error', {}).get('details', {})}")
            return True
        else:
            print(f"❌ Expected 422 but got {change_response.status_code}")
            print(f"   Response: {change_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing change-password: {e}")
        return False

def test_successful_flows():
    """Test that valid requests still work"""
    print("🧪 Testing successful flows...")
    
    # Test successful signup
    valid_signup = {
        "email": "<EMAIL>",
        "password": "Test123!",
        "confirmPassword": "Test123!",
        "username": "testsuccess",
        "firstName": "Test",
        "lastName": "Success"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }
    
    try:
        signup_response = requests.post(f"{BASE_URL}/auth/signup", 
                                      json=valid_signup, 
                                      headers=headers)
        
        if signup_response.status_code == 201:
            print("✅ Valid signup works correctly")
            
            token = signup_response.json().get('token')
            if token:
                # Test successful password change
                valid_change = {
                    "currentPassword": "Test123!",
                    "newPassword": "NewPass456!"
                }
                
                auth_headers = headers.copy()
                auth_headers['Authorization'] = f'Bearer {token}'
                
                change_response = requests.post(f"{BASE_URL}/auth/change-password",
                                              json=valid_change,
                                              headers=auth_headers)
                
                if change_response.status_code == 200:
                    print("✅ Valid password change works correctly")
                    return True
                else:
                    print(f"❌ Password change failed: {change_response.status_code}")
                    print(f"   Response: {change_response.text}")
                    return False
            else:
                print("❌ No token received from successful signup")
                return False
        else:
            print(f"❌ Valid signup failed: {signup_response.status_code}")
            print(f"   Response: {signup_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing successful flows: {e}")
        return False

def main():
    print("🚀 Starting comprehensive 422 error handling tests...")
    print("=" * 60)
    
    results = {
        'signup_422': test_signup_422(),
        'change_password_422': test_change_password_422(),
        'successful_flows': test_successful_flows()
    }
    
    print("\n📊 Test Results:")
    print("=" * 30)
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    all_passed = all(results.values())
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 All tests passed! The 422 error handling is working correctly.")
        print("✅ Both /api/auth/signup and /api/auth/change-password endpoints")
        print("   properly return 422 for invalid data and work for valid data.")
    else:
        print("⚠️ Some tests failed. Please check the endpoints.")
    
    return all_passed

if __name__ == "__main__":
    main()
