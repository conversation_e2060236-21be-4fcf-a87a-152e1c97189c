#!/usr/bin/env python3
"""
Debug script to check what's actually in the preserved data fields
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:3002/api"
EMAIL = "<EMAIL>"

def debug_preserved_data():
    """Debug what's in the preserved data"""
    print("🔍 Debugging Preserved Data")
    print("=" * 50)
    
    # Check preserved data
    print(f"📝 Checking preserved data for: {EMAIL}")
    response = requests.post(f"{BASE_URL}/account/check-preserved-data", 
                           json={'email': EMAIL})
    
    if response.status_code != 200:
        print(f"❌ Check preserved data failed: {response.text}")
        return
    
    result = response.json()
    print(f"📊 Response: {json.dumps(result, indent=2)}")
    
    has_preserved = result.get('hasPreservedData', False)
    preserved_summary = result.get('preservedDataSummary', {})
    
    print(f"\n📋 Analysis:")
    print(f"   - Has preserved data: {has_preserved}")
    print(f"   - Preserved summary: {preserved_summary}")
    
    # Check each category
    for category, status in preserved_summary.items():
        print(f"   - {category}: {status}")
        
        if category == 'payments' and status:
            print(f"     ⚠️ ISSUE: Payment data shows as preserved but user only selected security logs!")
        elif category == 'profile' and status:
            print(f"     ⚠️ ISSUE: Profile data shows as preserved but user only selected security logs!")
        elif category == 'transactions' and status:
            print(f"     ⚠️ ISSUE: Transaction data shows as preserved but user only selected security logs!")
        elif category == 'security' and status:
            print(f"     ✅ CORRECT: Security data is preserved as requested")

if __name__ == "__main__":
    debug_preserved_data()
