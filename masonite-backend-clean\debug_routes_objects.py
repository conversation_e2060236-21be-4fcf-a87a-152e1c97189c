#!/usr/bin/env python3
"""
Route Object Analysis Script
Deep dive into Route object structure
"""

import os
import sys
import importlib.util

# Add the project root to the Python path
project_root = r"c:\Users\<USER>\Downloads\study\apps\ai\cody\Modular backend secure user system and payment_Cody\masonite-backend-clean"
sys.path.insert(0, project_root)
os.chdir(project_root)

print("=== Route Object Analysis ===")

try:
    # Import the routes directly
    spec = importlib.util.spec_from_file_location("api_routes", "routes/api.py")
    api_routes_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(api_routes_module)
    
    routes = api_routes_module.ROUTES
    print(f"Loaded {len(routes)} routes")
    
    # Analyze the first few routes to understand structure
    for i, route in enumerate(routes[:10]):
        print(f"\nRoute {i+1}:")
        print(f"  Type: {type(route)}")
        print(f"  Dir: {[attr for attr in dir(route) if not attr.startswith('_')]}")
        
        # Try different attribute names
        route_url = getattr(route, 'route_url', None) or getattr(route, 'route', None) or getattr(route, 'url', None)
        method = getattr(route, 'method_type', None) or getattr(route, 'method', None) or getattr(route, 'methods', None)
        controller = getattr(route, 'controller', None) or getattr(route, 'action', None)
        
        print(f"  Route URL: {route_url}")
        print(f"  Method: {method}")
        print(f"  Controller: {controller}")
        
        # Check for login-related routes
        if route_url and 'login' in str(route_url).lower():
            print(f"  *** FOUND LOGIN ROUTE: {method} {route_url} -> {controller} ***")
    
    # Search specifically for auth routes
    print(f"\n=== Searching for Auth Routes ===")
    auth_routes = []
    for i, route in enumerate(routes):
        route_url = getattr(route, 'route_url', None) or getattr(route, 'route', None) or getattr(route, 'url', None)
        method = getattr(route, 'method_type', None) or getattr(route, 'method', None) or getattr(route, 'methods', None)
        controller = getattr(route, 'controller', None) or getattr(route, 'action', None)
        
        if route_url and 'auth' in str(route_url).lower():
            auth_routes.append({
                'index': i,
                'url': route_url,
                'method': method,
                'controller': controller
            })
    
    print(f"Found {len(auth_routes)} auth-related routes:")
    for auth_route in auth_routes[:20]:  # Show first 20
        print(f"  {auth_route['method']} {auth_route['url']} -> {auth_route['controller']}")
    
    if len(auth_routes) > 20:
        print(f"  ... and {len(auth_routes) - 20} more")
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
