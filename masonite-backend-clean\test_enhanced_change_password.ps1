# Test change password endpoint
Write-Host "Testing change password endpoint..."

# First login
$loginBody = '{"email":"<EMAIL>","password":"EnhancedTest123!"}'
$headers = @{
    'Content-Type' = 'application/json'
    'Origin' = 'http://localhost:4200'
}

try {
    Write-Host "1. Logging in..."
    $loginResponse = Invoke-WebRequest -Uri "http://localhost:3002/api/auth/login" -Method POST -Body $loginBody -Headers $headers -UseBasicParsing
    $loginResult = $loginResponse.Content | ConvertFrom-Json
    
    if ($loginResult.token) {
        Write-Host "✅ Login successful"
          # Test change password
        $changePasswordBody = '{"currentPassword":"EnhancedTest123!","newPassword":"NewEnhancedPassword123!"}'
        $authHeaders = @{
            'Content-Type' = 'application/json'
            'Authorization' = "Bearer $($loginResult.token)"
            'Origin' = 'http://localhost:4200'
        }
        
        Write-Host "2. Testing change password..."
        $changeResponse = Invoke-WebRequest -Uri "http://localhost:3002/api/auth/change-password" -Method POST -Body $changePasswordBody -Headers $authHeaders -UseBasicParsing
        Write-Host "✅ Change password successful: $($changeResponse.StatusCode)"
        Write-Host "Response: $($changeResponse.Content)"
    }
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)"
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.Value__)"
    
    if ($_.Exception.Response) {
        $result = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($result)
        $responseBody = $reader.ReadToEnd()
        Write-Host "Error Body: $responseBody"
    }
}
