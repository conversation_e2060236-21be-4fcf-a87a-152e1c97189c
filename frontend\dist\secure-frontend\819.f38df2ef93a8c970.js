"use strict";(self.webpackChunksecure_frontend=self.webpackChunksecure_frontend||[]).push([[819],{9819:(A,p,r)=>{r.r(p),r.d(p,{PaymentModule:()=>D});var c=r(6396),s=r(9417),k=r(1736),u=r(5596),E=r(6687),y=r(6863),g=r(8834),f=r(1074),F=r(5781),h=r(9183),C=r(8822),v=r(1997),P=r(5312),t=r(7241),d=r(8564),M=r(8212),T=r(4796),l=r(3364),j=r(3496);function O(o,m){if(1&o){const n=t.RV6();t.j41(0,"button",27),t.bIt("click",function(){const a=d.eBV(n).$implicit,i=t.XpG();return d.Njj(i.selectTestAmount(a))}),t.EFF(1),t.k0s()}if(2&o){let n;const e=m.$implicit,a=t.XpG();t.Y8G("color",(null==(n=a.paymentForm.get("currency"))?null:n.value)===e.currency?"primary":""),t.R7$(),t.SpI(" ",e.label," ")}}function b(o,m){if(1&o&&(t.j41(0,"mat-option",28),t.EFF(1),t.k0s()),2&o){const n=m.$implicit;t.Y8G("value",n),t.R7$(),t.SpI(" ",n," ")}}function R(o,m){if(1&o&&(t.j41(0,"mat-hint"),t.EFF(1),t.k0s()),2&o){const n=t.XpG();t.R7$(),t.JRh(n.getEquivalentAmount())}}function $(o,m){1&o&&t.nrm(0,"mat-spinner",29)}function S(o,m){1&o&&(t.j41(0,"mat-icon"),t.EFF(1,"lock"),t.k0s())}function I(o,m){1&o&&(t.j41(0,"span"),t.EFF(1,"Pay Securely with Razorpay"),t.k0s())}function G(o,m){if(1&o&&(t.j41(0,"div",33)(1,"div",34)(2,"div",35),t.EFF(3),t.k0s(),t.j41(4,"div",36)(5,"div",37),t.EFF(6),t.k0s(),t.j41(7,"div",38),t.EFF(8),t.nI1(9,"date"),t.k0s(),t.j41(10,"div",39),t.EFF(11),t.k0s()()(),t.j41(12,"div",40)(13,"div",41)(14,"mat-icon"),t.EFF(15),t.k0s(),t.EFF(16),t.nI1(17,"titlecase"),t.k0s()()()),2&o){const n=m.$implicit,e=t.XpG(2);t.R7$(3),t.SpI(" ",e.formatCurrency(n.amount,n.currency)," "),t.R7$(3),t.JRh(n.description||"Test Payment"),t.R7$(2),t.JRh(t.i5U(9,7,n.createdAt,"medium")),t.R7$(3),t.SpI("ID: ",n.id),t.R7$(2),t.Y8G("ngClass","status-"+n.status),t.R7$(2),t.JRh(e.getStatusIcon(n.status)),t.R7$(),t.SpI(" ",t.bMT(17,10,n.status)," ")}}function _(o,m){if(1&o&&(t.j41(0,"mat-card",30)(1,"mat-card-header")(2,"mat-card-title")(3,"mat-icon"),t.EFF(4,"history"),t.k0s(),t.EFF(5," Payment History "),t.k0s(),t.j41(6,"mat-card-subtitle"),t.EFF(7," Your recent test transactions "),t.k0s()(),t.j41(8,"mat-card-content")(9,"div",31),t.DNE(10,G,18,12,"div",32),t.k0s()()()),2&o){const n=t.XpG();t.R7$(10),t.Y8G("ngForOf",n.payments)}}const x=[{path:"",redirectTo:"test",pathMatch:"full"},{path:"test",component:(()=>{class o{constructor(n,e,a,i){this.formBuilder=n,this.paymentService=e,this.authService=a,this.snackBar=i,this.loading=!1,this.payments=[],this.supportedCurrencies=P.c.razorpay.supportedCurrencies,this.testAmounts=[{amount:100,currency:"INR",label:"\u20b9100 - Basic Test"},{amount:500,currency:"INR",label:"\u20b9500 - Standard Test"},{amount:1e3,currency:"INR",label:"\u20b91,000 - Premium Test"},{amount:5,currency:"USD",label:"$5 - Basic Test"},{amount:25,currency:"USD",label:"$25 - Standard Test"},{amount:50,currency:"USD",label:"$50 - Premium Test"}],this.paymentForm=this.formBuilder.group({amount:["",[s.k0.required,s.k0.min(1),s.k0.max(1e6)]],currency:[P.c.razorpay.currency,[s.k0.required]],description:["Payment test transaction"]})}ngOnInit(){this.loadPaymentHistory()}onSubmit(){if(this.paymentForm.invalid)return void this.markFormGroupTouched(this.paymentForm);const n=this.paymentForm.value,e=this.paymentService.validateAmount(n.amount,n.currency);e.valid?(this.loading=!0,this.paymentService.processPayment(n).subscribe({next:a=>{a.success?(this.snackBar.open("Payment completed successfully!","Close",{duration:5e3}),this.loadPaymentHistory(),this.paymentForm.patchValue({amount:"",description:"Payment test transaction"})):this.snackBar.open(a.message||"Payment failed","Close",{duration:5e3}),this.loading=!1},error:a=>{this.snackBar.open(a.message||"Payment processing failed","Close",{duration:5e3}),this.loading=!1}})):this.snackBar.open(e.error,"Close",{duration:5e3})}selectTestAmount(n){this.paymentForm.patchValue({amount:n.amount,currency:n.currency,description:`Test payment - ${n.label}`})}loadPaymentHistory(){this.paymentService.getMyPayments().subscribe({next:n=>{this.payments=n.payments},error:n=>{console.error("Failed to load payment history:",n)}})}formatCurrency(n,e){return this.paymentService.formatCurrency(n,e)}getStatusIcon(n){return this.paymentService.getStatusIcon(n)}getFieldError(n){const e=this.paymentForm.get(n);if(e?.errors&&e.touched){if(e.errors.required)return`${n} is required`;if(e.errors.min)return`Minimum amount is ${e.errors.min.min}`;if(e.errors.max)return`Maximum amount is ${e.errors.max.max}`}return""}markFormGroupTouched(n){Object.keys(n.controls).forEach(e=>{n.get(e)?.markAsTouched()})}convertAmount(n,e,a){return this.paymentService.convertCurrency(n,e,a)}getEquivalentAmount(){const n=this.paymentForm.get("amount")?.value,e=this.paymentForm.get("currency")?.value;if(!n||!e)return"";const a="INR"===e?"USD":"INR",i=this.convertAmount(n,e,a);return`\u2248 ${this.formatCurrency(i,a)}`}static#t=this.\u0275fac=function(e){return new(e||o)(t.rXU(s.ok),t.rXU(M.W),t.rXU(T.u),t.rXU(C.UG))};static#n=this.\u0275cmp=t.VBU({type:o,selectors:[["app-payment-test"]],standalone:!1,decls:84,vars:11,consts:[[1,"payment-container"],[1,"container"],[1,"subtitle"],[1,"payment-card"],[1,"test-amounts"],[1,"amount-buttons"],["mat-stroked-button","",3,"color","click",4,"ngFor","ngForOf"],[1,"my-3"],[3,"ngSubmit","formGroup"],[1,"form-row"],["appearance","outline",1,"currency-field"],["formControlName","currency"],[3,"value",4,"ngFor","ngForOf"],["matSuffix",""],["appearance","outline",1,"amount-field"],["matInput","","type","number","formControlName","amount","min","1","step","0.01"],[4,"ngIf"],["appearance","outline",1,"form-field"],["matInput","","formControlName","description","maxlength","100"],["mat-raised-button","","color","primary","type","submit",1,"payment-button",3,"disabled"],["diameter","20",4,"ngIf"],[1,"security-notice"],["class","payment-history",4,"ngIf"],[1,"testing-guide"],[1,"guide-section"],[1,"test-cards"],[1,"test-card"],["mat-stroked-button","",3,"click","color"],[3,"value"],["diameter","20"],[1,"payment-history"],[1,"payment-list"],["class","payment-item",4,"ngFor","ngForOf"],[1,"payment-item"],[1,"payment-info"],[1,"payment-amount"],[1,"payment-details"],[1,"payment-description"],[1,"payment-date"],[1,"payment-id"],[1,"payment-actions"],[1,"payment-status",3,"ngClass"]],template:function(e,a){if(1&e&&(t.j41(0,"div",0)(1,"div",1)(2,"h1"),t.EFF(3,"Payment Testing Interface"),t.k0s(),t.j41(4,"p",2),t.EFF(5,"Test Razorpay integration with secure payment processing"),t.k0s(),t.j41(6,"mat-card",3)(7,"mat-card-header")(8,"mat-card-title")(9,"mat-icon"),t.EFF(10,"payment"),t.k0s(),t.EFF(11," Make a Test Payment "),t.k0s(),t.j41(12,"mat-card-subtitle"),t.EFF(13," Test payments using Razorpay's secure checkout "),t.k0s()(),t.j41(14,"mat-card-content")(15,"div",4)(16,"h3"),t.EFF(17,"Quick Test Amounts"),t.k0s(),t.j41(18,"div",5),t.DNE(19,O,2,2,"button",6),t.k0s()(),t.nrm(20,"mat-divider",7),t.j41(21,"form",8),t.bIt("ngSubmit",function(){return a.onSubmit()}),t.j41(22,"div",9)(23,"mat-form-field",10)(24,"mat-label"),t.EFF(25,"Currency"),t.k0s(),t.j41(26,"mat-select",11),t.DNE(27,b,2,2,"mat-option",12),t.k0s(),t.j41(28,"mat-icon",13),t.EFF(29,"attach_money"),t.k0s()(),t.j41(30,"mat-form-field",14)(31,"mat-label"),t.EFF(32,"Amount"),t.k0s(),t.nrm(33,"input",15),t.j41(34,"span",13),t.EFF(35),t.k0s(),t.j41(36,"mat-error"),t.EFF(37),t.k0s(),t.DNE(38,R,2,1,"mat-hint",16),t.k0s()(),t.j41(39,"mat-form-field",17)(40,"mat-label"),t.EFF(41,"Description (Optional)"),t.k0s(),t.nrm(42,"input",18),t.j41(43,"mat-icon",13),t.EFF(44,"description"),t.k0s()(),t.j41(45,"button",19),t.DNE(46,$,1,0,"mat-spinner",20)(47,S,2,0,"mat-icon",16)(48,I,2,0,"span",16),t.k0s()(),t.j41(49,"div",21)(50,"mat-icon"),t.EFF(51,"security"),t.k0s(),t.j41(52,"div")(53,"strong"),t.EFF(54,"Secure Payment Processing"),t.k0s(),t.j41(55,"p"),t.EFF(56,"All payments are processed securely through Razorpay's PCI DSS compliant platform."),t.k0s()()()()(),t.DNE(57,_,11,1,"mat-card",22),t.j41(58,"mat-card",23)(59,"mat-card-header")(60,"mat-card-title")(61,"mat-icon"),t.EFF(62,"help"),t.k0s(),t.EFF(63," Testing Guide "),t.k0s()(),t.j41(64,"mat-card-content")(65,"div",24)(66,"h4"),t.EFF(67,"Test Card Numbers"),t.k0s(),t.j41(68,"div",25)(69,"div",26)(70,"strong"),t.EFF(71,"Visa:"),t.k0s(),t.EFF(72," ************** 1111 "),t.k0s(),t.j41(73,"div",26)(74,"strong"),t.EFF(75,"Mastercard:"),t.k0s(),t.EFF(76," ************** 4444 "),t.k0s()(),t.j41(77,"p")(78,"strong"),t.EFF(79,"CVV:"),t.k0s(),t.EFF(80," Any 3-4 digit number | "),t.j41(81,"strong"),t.EFF(82,"Expiry:"),t.k0s(),t.EFF(83," Any future date"),t.k0s()()()()()()),2&e){let i;t.R7$(19),t.Y8G("ngForOf",a.testAmounts),t.R7$(2),t.Y8G("formGroup",a.paymentForm),t.R7$(6),t.Y8G("ngForOf",a.supportedCurrencies),t.R7$(8),t.JRh("INR"===(null==(i=a.paymentForm.get("currency"))?null:i.value)?"\u20b9":"$"),t.R7$(2),t.JRh(a.getFieldError("amount")),t.R7$(),t.Y8G("ngIf",a.getEquivalentAmount()),t.R7$(7),t.Y8G("disabled",a.loading),t.R7$(),t.Y8G("ngIf",a.loading),t.R7$(),t.Y8G("ngIf",!a.loading),t.R7$(),t.Y8G("ngIf",!a.loading),t.R7$(9),t.Y8G("ngIf",a.payments.length>0)}},dependencies:[c.YU,c.Sq,c.bT,s.qT,s.me,s.Q0,s.BC,s.cb,s.tU,s.VZ,s.j4,s.JD,u.RN,u.m2,u.MM,u.Lc,u.dh,l.j,l.M,l.c,l.b,l.g,y.fg,g.$z,f.An,F.g,j.M,h.LG,v.q,c.PV,c.vh],styles:[".my-3[_ngcontent-%COMP%]{margin:1.5rem 0}.form-row[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-bottom:1rem}.form-row[_ngcontent-%COMP%]   .currency-field[_ngcontent-%COMP%]{flex:0 0 120px}.form-row[_ngcontent-%COMP%]   .amount-field[_ngcontent-%COMP%]{flex:1}.test-amounts[_ngcontent-%COMP%]{margin-bottom:1.5rem}.test-amounts[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-bottom:1rem;color:#333}.test-amounts[_ngcontent-%COMP%]   .amount-buttons[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:.5rem}.test-amounts[_ngcontent-%COMP%]   .amount-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:.75rem;font-size:.875rem}.testing-guide[_ngcontent-%COMP%]   .guide-section[_ngcontent-%COMP%]{margin-bottom:1.5rem}.testing-guide[_ngcontent-%COMP%]   .guide-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#333;margin-bottom:.75rem}.testing-guide[_ngcontent-%COMP%]   .guide-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:.5rem 0;color:#666}.testing-guide[_ngcontent-%COMP%]   .test-cards[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:.5rem;margin-bottom:1rem}.testing-guide[_ngcontent-%COMP%]   .test-cards[_ngcontent-%COMP%]   .test-card[_ngcontent-%COMP%]{background:#f8f9fa;padding:.75rem;border-radius:6px;font-family:monospace;font-size:.875rem}.testing-guide[_ngcontent-%COMP%]   .test-cards[_ngcontent-%COMP%]   .test-card[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#333}@media (max-width: 768px){.form-row[_ngcontent-%COMP%]{flex-direction:column;gap:0}.form-row[_ngcontent-%COMP%]   .currency-field[_ngcontent-%COMP%]{flex:1}.amount-buttons[_ngcontent-%COMP%], .test-cards[_ngcontent-%COMP%]{grid-template-columns:1fr}}"]})}return o})()}];let D=(()=>{class o{static#t=this.\u0275fac=function(e){return new(e||o)};static#n=this.\u0275mod=t.$C({type:o});static#e=this.\u0275inj=d.G2t({imports:[c.MD,s.X1,k.iI.forChild(x),u.Hu,E.M,y.fS,g.Hl,f.m_,F.M,h.D6,C._T,v.w]})}return o})()}}]);