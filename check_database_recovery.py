#!/usr/bin/env python3
"""
Direct Database Check for Recovery Codes
"""

import sqlite3
import json

def check_database():
    """Check the database directly for user recovery codes"""
    try:
        # Connect to the database
        db_path = "c:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\masonite-backend-clean\\masonite.sqlite3"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Checking database for users with 2FA enabled...")
          # Check all users and their 2FA status
        cursor.execute("""
            SELECT id, email, two_factor_enabled, 
                   backup_code_1, backup_code_2, backup_code_3, 
                   backup_codes_remaining, backup_codes_generated_at
            FROM users
            LIMIT 10
        """)
        
        users = cursor.fetchall()
        
        if not users:
            print("❌ No users found in database")
            return
        
        print(f"📊 Found {len(users)} users:")
        
        for user in users:
            user_id, email, two_factor_enabled, code1, code2, code3, remaining, generated_at = user
            print(f"\n👤 User: {email} (ID: {user_id})")
            print(f"   2FA Enabled: {two_factor_enabled}")
            print(f"   Backup Code 1: {'✅' if code1 else '❌'}")
            print(f"   Backup Code 2: {'✅' if code2 else '❌'}")
            print(f"   Backup Code 3: {'✅' if code3 else '❌'}")
            print(f"   Remaining Codes: {remaining}")
            print(f"   Generated At: {generated_at}")
            
            if two_factor_enabled and (code1 or code2 or code3):
                print(f"   🎯 This user has 2FA enabled with recovery codes!")
                
                # If codes exist but remaining is 0, that's the issue
                if remaining == 0:
                    print(f"   ⚠️ BUG FOUND: Codes exist but remaining count is 0!")
                    
        conn.close()
        
        # Now let's create a test user with recovery codes if none exist
        print("\n" + "=" * 60)
        print("🔧 Creating test scenario...")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if test user exists
        cursor.execute("SELECT id FROM users WHERE email = ?", ('<EMAIL>',))
        test_user = cursor.fetchone()
        
        if not test_user:
            print("Creating test user...")
            # Create test user (using bcrypt for password)
            import bcrypt
            hashed_password = bcrypt.hashpw('SecurePass123!'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            cursor.execute("""
                INSERT INTO users (name, email, password, email_verified_at, created_at, updated_at)
                VALUES (?, ?, ?, datetime('now'), datetime('now'), datetime('now'))
            """, ('Test User', '<EMAIL>', hashed_password))
            
            test_user_id = cursor.lastrowid
            print(f"✅ Created test user with ID: {test_user_id}")
        else:
            test_user_id = test_user[0]
            print(f"✅ Test user exists with ID: {test_user_id}")
        
        # Add recovery codes to test user
        print("Adding recovery codes to test user...")
        
        # Generate test recovery codes (hashed)
        test_codes = ['ABC12345', 'DEF67890', 'GHI23456']
        hashed_codes = []
        
        for code in test_codes:
            hashed = bcrypt.hashpw(code.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            hashed_codes.append(hashed)
        
        cursor.execute("""
            UPDATE users 
            SET two_factor_enabled = 1,
                backup_code_1 = ?,
                backup_code_2 = ?,
                backup_code_3 = ?,
                backup_codes_remaining = 3,
                backup_codes_generated_at = datetime('now'),
                updated_at = datetime('now')
            WHERE id = ?
        """, (hashed_codes[0], hashed_codes[1], hashed_codes[2], test_user_id))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Updated test user with recovery codes:")
        for i, code in enumerate(test_codes):
            print(f"   Code {i+1}: {code}")
        
        print("\n🧪 Now you can test recovery code login with:")
        print(f"   Email: <EMAIL>")
        print(f"   Password: SecurePass123!")
        print(f"   Recovery Code: {test_codes[0]}")
        
    except Exception as e:
        print(f"❌ Database error: {e}")

if __name__ == '__main__':
    check_database()
