param(
    [string]$Method = "POST",
    [string]$Uri = "http://localhost:3002/api/auth/login",
    [string]$Body = '{"email":"<EMAIL>","password":"TestPassword123!"}'
)

try {
    $headers = @{
        'Content-Type' = 'application/json'
        'Origin' = 'http://localhost:4200'
    }
    
    Write-Host "Testing LOGIN endpoint: $Uri"
    
    $response = Invoke-WebRequest -Uri $Uri -Method $Method -Body $Body -Headers $headers -UseBasicParsing
    
    Write-Host "Login Status Code: $($response.StatusCode)"
    $loginResult = $response.Content | ConvertFrom-Json
    
    if ($loginResult.token) {
        Write-Host "Login successful, testing change password..."
        
        $changePasswordBody = '{"currentPassword":"TestPassword123!","newPassword":"NewTestPassword123!"}'
        $authHeaders = @{
            'Content-Type' = 'application/json'
            'Authorization' = "Bearer $($loginResult.token)"
            'Origin' = 'http://localhost:4200'
        }
        
        $changePasswordResponse = Invoke-WebRequest -Uri "http://localhost:3002/api/auth/change-password" -Method "POST" -Body $changePasswordBody -Headers $authHeaders -UseBasicParsing
        
        Write-Host "Change Password Status Code: $($changePasswordResponse.StatusCode)"
        Write-Host "Change Password Response: $($changePasswordResponse.Content)"
    } else {
        Write-Host "Login failed: $($response.Content)"
    }
    
} catch {
    Write-Host "Error occurred:"
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.Value__)"
    Write-Host "Status Description: $($_.Exception.Response.StatusDescription)"
    
    if ($_.Exception.Response) {
        $result = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($result)
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody"
    }
}
