#!/usr/bin/env python3
"""
Simple test to check the registration endpoint
"""

import requests
import json

BASE_URL = "http://localhost:3002"

def test_simple_registration():
    data = {
        'email': '<EMAIL>',
        'password': 'TestPassword123!',
        'confirmPassword': 'TestPassword123!',
        'firstName': 'Simple',
        'lastName': 'Test'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/signup", json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        return response
    except Exception as e:
        print(f"Error: {str(e)}")
        return None

if __name__ == "__main__":
    test_simple_registration()
