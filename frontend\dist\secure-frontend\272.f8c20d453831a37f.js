"use strict";(self.webpackChunksecure_frontend=self.webpackChunksecure_frontend||[]).push([[272],{3272:(Nt,M,d)=>{d.r(M),d.d(M,{AuthModule:()=>Dt});var p=d(6396),a=d(9417),w=d(1736),g=d(5596),A=d(6687),P=d(6863),f=d(8834),_=d(1074),y=d(2506),k=d(9183),F=d(8822),G=d(1997),x=d(357),$=d(5781),E=d(2544),t=d(7241),i=d(8564),C=d(4796),j=d(7097),R=d(3156),h=d(5333),m=d(3364),O=d(4179);function L(r,c){if(1&r){const e=t.RV6();t.j41(0,"div",8)(1,"div",9)(2,"mat-icon",10),t.<PERSON><PERSON>(3,"warning"),t.k0s(),t.j41(4,"h4"),t.EFF(5,"Account Temporarily Locked"),t.k0s()(),t.j41(6,"p",11),t.EFF(7," Your account has been locked due to multiple failed login attempts. Please wait 30 minutes or try one of these options to unlock immediately: "),t.k0s(),t.j41(8,"div",12)(9,"button",13),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.goToForgotPassword())}),t.j41(10,"mat-icon"),t.EFF(11,"lock_reset"),t.k0s(),t.EFF(12," Reset Password "),t.k0s(),t.j41(13,"button",14),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.toggleOTPLogin())}),t.j41(14,"mat-icon"),t.EFF(15,"sms"),t.k0s(),t.EFF(16," Login with OTP "),t.k0s(),t.j41(17,"a",15)(18,"mat-icon"),t.EFF(19,"support"),t.k0s(),t.EFF(20," Contact Support "),t.k0s()()()}}function V(r,c){1&r&&t.nrm(0,"mat-spinner",34)}function D(r,c){1&r&&(t.j41(0,"span"),t.EFF(1,"Sign In"),t.k0s())}function N(r,c){if(1&r){const e=t.RV6();t.j41(0,"button",35),t.bIt("click",function(){const o=i.eBV(e).$implicit,s=t.XpG(2);return i.Njj(s.loginWithOAuth(o.name))}),t.nrm(1,"i"),t.j41(2,"span"),t.EFF(3),t.k0s()()}if(2&r){const e=c.$implicit,n=t.XpG(2);t.xc7("border-color",e.color),t.Y8G("disabled",n.loading),t.R7$(),t.HbH(e.icon),t.xc7("color",e.color),t.R7$(2),t.JRh(e.displayName)}}function B(r,c){if(1&r){const e=t.RV6();t.j41(0,"form",16),t.bIt("ngSubmit",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.onSubmit())}),t.j41(1,"mat-form-field",17)(2,"mat-label"),t.EFF(3,"Email Address"),t.k0s(),t.nrm(4,"input",18),t.j41(5,"mat-icon",19),t.EFF(6,"email"),t.k0s(),t.j41(7,"mat-error"),t.EFF(8),t.k0s()(),t.j41(9,"mat-form-field",17)(10,"mat-label"),t.EFF(11,"Password"),t.k0s(),t.nrm(12,"input",20),t.j41(13,"button",21),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.hidePassword=!o.hidePassword)}),t.j41(14,"mat-icon"),t.EFF(15),t.k0s()(),t.j41(16,"mat-error"),t.EFF(17),t.k0s()(),t.j41(18,"button",22),t.DNE(19,V,1,0,"mat-spinner",23)(20,D,2,0,"span",24),t.k0s(),t.j41(21,"div",25)(22,"span"),t.EFF(23,"or"),t.k0s()(),t.j41(24,"button",26),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.toggleOTPLogin())}),t.j41(25,"mat-icon"),t.EFF(26,"sms"),t.k0s(),t.EFF(27," Login with OTP "),t.k0s(),t.j41(28,"div",27)(29,"div",25)(30,"span"),t.EFF(31,"or continue with"),t.k0s()(),t.j41(32,"div",28),t.DNE(33,N,4,8,"button",29),t.k0s()(),t.j41(34,"div",30)(35,"a",31),t.EFF(36,"Forgot Password?"),t.k0s()(),t.j41(37,"div",32)(38,"span"),t.EFF(39,"Don't have an account? "),t.k0s(),t.j41(40,"a",33),t.EFF(41,"Sign Up"),t.k0s()()()}if(2&r){const e=t.XpG();t.Y8G("formGroup",e.loginForm),t.R7$(8),t.JRh(e.getFieldError(e.loginForm,"email")),t.R7$(4),t.Y8G("type",e.hidePassword?"password":"text"),t.R7$(3),t.JRh(e.hidePassword?"visibility_off":"visibility"),t.R7$(2),t.JRh(e.getFieldError(e.loginForm,"password")),t.R7$(),t.Y8G("disabled",e.loading),t.R7$(),t.Y8G("ngIf",e.loading),t.R7$(),t.Y8G("ngIf",!e.loading),t.R7$(13),t.Y8G("ngForOf",e.oauthProviders)}}function X(r,c){if(1&r&&(t.j41(0,"mat-form-field",17)(1,"mat-label"),t.EFF(2,"Authentication Code"),t.k0s(),t.nrm(3,"input",48),t.j41(4,"mat-icon",19),t.EFF(5,"verified_user"),t.k0s(),t.j41(6,"mat-error"),t.EFF(7),t.k0s()()),2&r){const e=t.XpG(2);t.R7$(7),t.JRh(e.getFieldError(e.twoFactorForm,"twoFactorToken"))}}function U(r,c){if(1&r&&(t.j41(0,"mat-form-field",17)(1,"mat-label"),t.EFF(2,"Recovery Code"),t.k0s(),t.nrm(3,"input",49),t.j41(4,"mat-icon",19),t.EFF(5,"restore"),t.k0s(),t.j41(6,"mat-error"),t.EFF(7),t.k0s()()),2&r){const e=t.XpG(2);t.R7$(7),t.JRh(e.getFieldError(e.twoFactorForm,"recoveryCode"))}}function Y(r,c){1&r&&t.nrm(0,"mat-spinner",34)}function z(r,c){if(1&r&&(t.j41(0,"span"),t.EFF(1),t.k0s()),2&r){const e=t.XpG(2);t.R7$(),t.JRh(e.useRecoveryCode?"Use Recovery Code":"Verify & Sign In")}}function q(r,c){if(1&r){const e=t.RV6();t.j41(0,"button",50),t.bIt("click",function(){i.eBV(e);const o=t.XpG(2);return i.Njj(o.openDisable2FAFromRecovery())}),t.j41(1,"mat-icon"),t.EFF(2,"warning"),t.k0s(),t.EFF(3," All recovery codes used? "),t.k0s()}}function J(r,c){if(1&r){const e=t.RV6();t.j41(0,"form",16),t.bIt("ngSubmit",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.onTwoFactorSubmit())}),t.j41(1,"div",36)(2,"mat-icon",37),t.EFF(3,"security"),t.k0s(),t.j41(4,"h3"),t.EFF(5,"Two-Factor Authentication"),t.k0s(),t.j41(6,"p"),t.EFF(7),t.k0s()(),t.DNE(8,X,8,1,"mat-form-field",38)(9,U,8,1,"mat-form-field",38),t.j41(10,"button",22),t.DNE(11,Y,1,0,"mat-spinner",23)(12,z,2,1,"span",24),t.k0s(),t.j41(13,"div",39)(14,"button",40),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.toggleRecoveryCode())}),t.j41(15,"mat-icon"),t.EFF(16),t.k0s(),t.EFF(17),t.k0s()(),t.j41(18,"div",41)(19,"div",42)(20,"span"),t.EFF(21,"Need help with 2FA?"),t.k0s()(),t.j41(22,"div",43)(23,"button",44),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.openDisable2FADialog())}),t.j41(24,"mat-icon"),t.EFF(25,"mail"),t.k0s(),t.EFF(26," Can't access your device? "),t.k0s(),t.DNE(27,q,4,0,"button",45),t.k0s(),t.j41(28,"p",46),t.EFF(29," Lost access to your authenticator or used all recovery codes? We can send you a secure email to disable 2FA. "),t.k0s()(),t.j41(30,"button",47),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.backToLogin())}),t.j41(31,"mat-icon"),t.EFF(32,"arrow_back"),t.k0s(),t.EFF(33," Back to Login "),t.k0s()()}if(2&r){const e=t.XpG();t.Y8G("formGroup",e.twoFactorForm),t.R7$(7),t.JRh(e.useRecoveryCode?"Enter one of your recovery codes":"Enter the 6-digit code from your authenticator app"),t.R7$(),t.Y8G("ngIf",!e.useRecoveryCode),t.R7$(),t.Y8G("ngIf",e.useRecoveryCode),t.R7$(),t.Y8G("disabled",e.loading),t.R7$(),t.Y8G("ngIf",e.loading),t.R7$(),t.Y8G("ngIf",!e.loading),t.R7$(4),t.JRh(e.useRecoveryCode?"smartphone":"restore"),t.R7$(),t.SpI(" ",e.useRecoveryCode?"Use Authenticator App":"Use Recovery Code"," "),t.R7$(10),t.Y8G("ngIf",e.useRecoveryCode)}}function W(r,c){1&r&&t.nrm(0,"mat-spinner",34)}function H(r,c){1&r&&(t.j41(0,"span"),t.EFF(1,"Send OTP"),t.k0s())}function Z(r,c){if(1&r){const e=t.RV6();t.j41(0,"button",54),t.bIt("click",function(){i.eBV(e);const o=t.XpG(2);return i.Njj(o.sendOTP())}),t.DNE(1,W,1,0,"mat-spinner",23)(2,H,2,0,"span",24),t.k0s()}if(2&r){const e=t.XpG(2);t.Y8G("disabled",e.loading),t.R7$(),t.Y8G("ngIf",e.loading),t.R7$(),t.Y8G("ngIf",!e.loading)}}function Q(r,c){1&r&&t.nrm(0,"mat-spinner",34)}function K(r,c){1&r&&(t.j41(0,"span"),t.EFF(1,"Verify & Sign In"),t.k0s())}function tt(r,c){if(1&r){const e=t.RV6();t.j41(0,"div")(1,"mat-form-field",17)(2,"mat-label"),t.EFF(3,"Enter OTP"),t.k0s(),t.nrm(4,"input",55),t.j41(5,"mat-icon",19),t.EFF(6,"lock"),t.k0s(),t.j41(7,"mat-error"),t.EFF(8),t.k0s()(),t.j41(9,"button",56),t.bIt("click",function(){i.eBV(e);const o=t.XpG(2);return i.Njj(o.loginWithOTP())}),t.DNE(10,Q,1,0,"mat-spinner",23)(11,K,2,0,"span",24),t.k0s(),t.j41(12,"button",47),t.bIt("click",function(){i.eBV(e);const o=t.XpG(2);return i.Njj(o.sendOTP())}),t.j41(13,"mat-icon"),t.EFF(14,"refresh"),t.k0s(),t.EFF(15," Resend OTP "),t.k0s()()}if(2&r){const e=t.XpG(2);t.R7$(8),t.JRh(e.getFieldError(e.otpForm,"code")),t.R7$(),t.Y8G("disabled",e.loading),t.R7$(),t.Y8G("ngIf",e.loading),t.R7$(),t.Y8G("ngIf",!e.loading)}}function et(r,c){if(1&r){const e=t.RV6();t.j41(0,"form",51)(1,"div",36)(2,"mat-icon",37),t.EFF(3,"sms"),t.k0s(),t.j41(4,"h3"),t.EFF(5,"Login with OTP"),t.k0s(),t.j41(6,"p"),t.EFF(7,"Enter your email or phone number to receive a one-time password"),t.k0s()(),t.j41(8,"mat-form-field",17)(9,"mat-label"),t.EFF(10,"Email or Phone Number"),t.k0s(),t.nrm(11,"input",52),t.j41(12,"mat-icon",19),t.EFF(13,"contact_mail"),t.k0s(),t.j41(14,"mat-error"),t.EFF(15),t.k0s()(),t.DNE(16,Z,3,3,"button",53)(17,tt,16,4,"div",24),t.j41(18,"button",47),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.toggleOTPLogin())}),t.j41(19,"mat-icon"),t.EFF(20,"arrow_back"),t.k0s(),t.EFF(21," Back to Login "),t.k0s()()}if(2&r){const e=t.XpG();t.Y8G("formGroup",e.otpForm),t.R7$(15),t.JRh(e.getFieldError(e.otpForm,"identifier")),t.R7$(),t.Y8G("ngIf",!e.otpSent),t.R7$(),t.Y8G("ngIf",e.otpSent)}}let nt=(()=>{class r{constructor(e,n,o,s,u,b,v,l){this.formBuilder=e,this.authService=n,this.oauthService=o,this.twoFactorService=s,this.router=u,this.route=b,this.snackBar=v,this.dialog=l,this.loading=!1,this.hidePassword=!0,this.showOTPLogin=!1,this.showTwoFactor=!1,this.otpSent=!1,this.returnUrl="",this.oauthProviders=[],this.useRecoveryCode=!1,this.accountLocked=!1,this.loginForm=this.formBuilder.group({email:["",[a.k0.required,a.k0.email]],password:["",[a.k0.required,a.k0.minLength(8)]]}),this.otpForm=this.formBuilder.group({identifier:["",[a.k0.required]],code:["",[a.k0.required,a.k0.pattern(/^\d{6}$/)]]}),this.twoFactorForm=this.formBuilder.group({twoFactorToken:["",[a.k0.required,a.k0.pattern(/^\d{6}$/)]],recoveryCode:["",[a.k0.required]]})}ngOnInit(){this.returnUrl=this.route.snapshot.queryParams.returnUrl||"/dashboard",this.oauthProviders=this.oauthService.getAvailableProviders(),this.authService.isAuthenticated&&this.router.navigate([this.returnUrl]);const e=this.route.snapshot.queryParams.message;e&&this.snackBar.open(e,"Close",{duration:5e3});const n=this.route.snapshot.queryParams.code;n&&this.handleOAuthCallback(n,this.route.snapshot.queryParams.state)}onSubmit(){this.loginForm.invalid?this.markFormGroupTouched(this.loginForm):(this.loading=!0,this.authService.login(this.loginForm.value).subscribe({next:n=>{n.requiresTwoFactor?(this.showTwoFactor=!0,this.snackBar.open("Please enter your two-factor authentication code","Close",{duration:5e3})):(this.snackBar.open("Login successful!","Close",{duration:3e3}),this.router.navigate([this.returnUrl])),this.loading=!1},error:n=>{this.handleLoginError(n),this.loading=!1}}))}onTwoFactorSubmit(){const e=this.twoFactorForm.value.twoFactorToken,n=this.twoFactorForm.value.recoveryCode;if(this.useRecoveryCode&&!n)return void this.snackBar.open("Please enter a recovery code","Close",{duration:3e3});if(!this.useRecoveryCode&&!e)return void this.snackBar.open("Please enter the authentication code","Close",{duration:3e3});this.loading=!0;const o={...this.loginForm.value,twoFactorToken:this.useRecoveryCode?void 0:e,recoveryCode:this.useRecoveryCode?n:void 0};this.authService.login(o).subscribe({next:s=>{this.snackBar.open("Login successful!","Close",{duration:3e3}),this.router.navigate([this.returnUrl]),this.loading=!1},error:s=>{this.handleLoginError(s),this.loading=!1}})}sendOTP(){const e=this.otpForm.get("identifier")?.value;e?(this.loading=!0,this.authService.sendOTP({identifier:e,type:"login"}).subscribe({next:()=>{this.otpSent=!0,this.snackBar.open("OTP sent successfully!","Close",{duration:3e3}),this.loading=!1},error:o=>{this.snackBar.open(o.message||"Failed to send OTP","Close",{duration:5e3}),this.loading=!1}})):this.snackBar.open("Please enter email or phone number","Close",{duration:3e3})}loginWithOTP(){if(this.otpForm.invalid)return void this.markFormGroupTouched(this.otpForm);this.loading=!0;const{identifier:e,code:n}=this.otpForm.value;this.authService.loginWithOTP(e,n).subscribe({next:()=>{this.snackBar.open("Login successful!","Close",{duration:3e3}),this.router.navigate([this.returnUrl]),this.loading=!1},error:o=>{this.snackBar.open(o.message||"OTP login failed","Close",{duration:5e3}),this.loading=!1}})}toggleOTPLogin(){this.showOTPLogin=!this.showOTPLogin,this.showTwoFactor=!1,this.otpSent=!1,this.otpForm.reset(),this.accountLocked=!1}backToLogin(){this.showTwoFactor=!1,this.showOTPLogin=!1,this.otpSent=!1}toggleRecoveryCode(){this.useRecoveryCode=!this.useRecoveryCode;const e=this.twoFactorForm.get("twoFactorToken"),n=this.twoFactorForm.get("recoveryCode");this.useRecoveryCode?(e?.clearValidators(),n?.setValidators([a.k0.required])):(e?.setValidators([a.k0.required,a.k0.pattern(/^\d{6}$/)]),n?.clearValidators()),e?.updateValueAndValidity(),n?.updateValueAndValidity(),e?.setValue(""),n?.setValue("")}loginWithOAuth(e){this.loading=!0,this.oauthService.initiateOAuthLogin(e)}handleOAuthCallback(e,n){this.loading=!0,this.oauthService.handleOAuthCallback(e,n).subscribe({next:o=>{this.snackBar.open("OAuth login successful!","Close",{duration:3e3}),this.router.navigate([this.returnUrl]),this.loading=!1},error:o=>{this.snackBar.open(o.message||"OAuth login failed","Close",{duration:5e3}),this.loading=!1,this.router.navigate([],{relativeTo:this.route,queryParams:{},replaceUrl:!0})}})}getFieldError(e,n){const o=e.get(n);if(o?.errors&&o.touched){if(o.errors.required)return`${n} is required`;if(o.errors.email)return"Please enter a valid email";if(o.errors.minlength)return`${n} must be at least ${o.errors.minlength.requiredLength} characters`;if(o.errors.pattern)return"Please enter a valid format"}return""}markFormGroupTouched(e){Object.keys(e.controls).forEach(n=>{e.get(n)?.markAsTouched()})}handleLoginError(e){let n="Login failed";e?.message?n=e.message:e?.error?.error?.message?n=e.error.error.message:e?.error?.message&&(n=e.error.message),console.log("Login Error Details:",{error:e,extractedMessage:n}),n.includes("verify your email")||n.includes("email verification")||n.includes("unverified email")?this.snackBar.open("Please verify your email before logging in. Check your inbox for a verification link.","Resend Email",{duration:1e4,panelClass:["warning-snackbar"]}).onAction().subscribe(()=>{this.resendVerificationEmail()}):n.includes("temporarily locked")||n.includes("multiple failed login attempts")?(this.accountLocked=!0,console.log("Account lockout detected, showing lockout notice"),this.snackBar.open(n,"Close",{duration:15e3,panelClass:["error-snackbar"]})):(this.accountLocked=!1,this.snackBar.open(n,"Close",{duration:5e3}))}resendVerificationEmail(){const e=this.loginForm.get("email")?.value;e?this.authService.resendVerification(e).subscribe({next:n=>{this.snackBar.open("Verification email sent! Please check your inbox.","Close",{duration:5e3})},error:n=>{this.snackBar.open("Failed to send verification email. Please try again.","Close",{duration:5e3})}}):this.snackBar.open("Please enter your email address","Close",{duration:3e3})}goToForgotPassword(){this.router.navigate(["/auth/forgot-password"])}openDisable2FADialog(){const e=this.loginForm.get("email")?.value;e?this.dialog.open(E.p,{width:"500px",disableClose:!0,data:{email:e,allCodesUsed:!1,source:"login"}}).afterClosed().subscribe(o=>{o?.success&&console.log("2FA disable request sent successfully")}):this.snackBar.open("Please enter your email address first","Close",{duration:3e3})}openDisable2FAFromRecovery(){const e=this.loginForm.get("email")?.value;e?this.dialog.open(E.p,{width:"500px",disableClose:!0,data:{email:e,allCodesUsed:!0,source:"recovery"}}).afterClosed().subscribe(o=>{o?.success&&console.log("2FA disable request sent successfully from recovery")}):this.snackBar.open("Please enter your email address first","Close",{duration:3e3})}static#t=this.\u0275fac=function(n){return new(n||r)(t.rXU(a.ok),t.rXU(C.u),t.rXU(j.T),t.rXU(R.f),t.rXU(h.Ix),t.rXU(h.nX),t.rXU(F.UG),t.rXU(x.h))};static#e=this.\u0275cmp=t.VBU({type:r,selectors:[["app-login"]],standalone:!1,decls:16,vars:4,consts:[[1,"auth-container"],[1,"auth-card","fade-in"],[1,"auth-header"],[1,"security-badge"],[1,"auth-content"],["class","alert alert-warning lockout-notice","role","alert",4,"ngIf"],[3,"formGroup","ngSubmit",4,"ngIf"],[3,"formGroup",4,"ngIf"],["role","alert",1,"alert","alert-warning","lockout-notice"],[1,"lockout-header"],[1,"warning-icon"],[1,"mb-3"],[1,"unlock-options"],["mat-stroked-button","","color","primary",1,"unlock-btn",3,"click"],["mat-stroked-button","","color","accent",1,"unlock-btn",3,"click"],["href","mailto:<EMAIL>","mat-stroked-button","",1,"unlock-btn"],[3,"ngSubmit","formGroup"],["appearance","outline",1,"form-field"],["matInput","","type","email","formControlName","email","autocomplete","email"],["matSuffix",""],["matInput","","formControlName","password","autocomplete","current-password",3,"type"],["mat-icon-button","","matSuffix","","type","button",3,"click"],["mat-raised-button","","color","primary","type","submit",1,"submit-button",3,"disabled"],["diameter","20",4,"ngIf"],[4,"ngIf"],[1,"divider"],["mat-stroked-button","","type","button",1,"w-100",3,"click"],[1,"oauth-section","mt-3"],[1,"oauth-buttons"],["mat-stroked-button","","type","button","class","oauth-button",3,"border-color","disabled","click",4,"ngFor","ngForOf"],[1,"text-center","mt-3"],["routerLink","/auth/forgot-password",1,"text-primary"],[1,"text-center","mt-2"],["routerLink","/auth/register",1,"text-primary"],["diameter","20"],["mat-stroked-button","","type","button",1,"oauth-button",3,"click","disabled"],[1,"text-center","mb-3"],["color","primary",2,"font-size","48px","width","48px","height","48px"],["class","form-field","appearance","outline",4,"ngIf"],[1,"recovery-options","mt-3"],["mat-button","","type","button",1,"w-100",3,"click"],[1,"twofa-help-section","mt-3"],[1,"help-divider"],[1,"help-options"],["mat-button","","type","button",1,"help-button",3,"click"],["mat-button","","type","button","class","help-button warn-button",3,"click",4,"ngIf"],[1,"help-text"],["mat-button","","type","button",1,"w-100","mt-2",3,"click"],["matInput","","formControlName","twoFactorToken","placeholder","000000","maxlength","6","autocomplete","one-time-code"],["matInput","","formControlName","recoveryCode","placeholder","abcd-efgh-ijkl","autocomplete","one-time-code"],["mat-button","","type","button",1,"help-button","warn-button",3,"click"],[3,"formGroup"],["matInput","","formControlName","identifier","placeholder","<EMAIL> or +1234567890"],["mat-raised-button","","color","accent","type","button","class","submit-button",3,"disabled","click",4,"ngIf"],["mat-raised-button","","color","accent","type","button",1,"submit-button",3,"click","disabled"],["matInput","","formControlName","code","placeholder","000000","maxlength","6","autocomplete","one-time-code"],["mat-raised-button","","color","primary","type","button",1,"submit-button",3,"click","disabled"]],template:function(n,o){1&n&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"h1"),t.EFF(4,"Welcome Back"),t.k0s(),t.j41(5,"p"),t.EFF(6,"Sign in to your secure account"),t.k0s(),t.j41(7,"div",3)(8,"mat-icon"),t.EFF(9,"security"),t.k0s(),t.EFF(10," Secure Login "),t.k0s()(),t.j41(11,"div",4),t.DNE(12,L,21,0,"div",5)(13,B,42,9,"form",6)(14,J,34,10,"form",6)(15,et,22,4,"form",7),t.k0s()()()),2&n&&(t.R7$(12),t.Y8G("ngIf",o.accountLocked),t.R7$(),t.Y8G("ngIf",!o.showOTPLogin&&!o.showTwoFactor),t.R7$(),t.Y8G("ngIf",o.showTwoFactor),t.R7$(),t.Y8G("ngIf",o.showOTPLogin))},dependencies:[p.Sq,p.bT,a.qT,a.me,a.BC,a.cb,a.tU,a.j4,a.JD,w.Wk,m.j,m.M,m.b,m.g,P.fg,f.$z,O.M,_.An,k.LG],styles:['.w-100[_ngcontent-%COMP%]{width:100%}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:1rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:1rem}.text-center[_ngcontent-%COMP%]{text-align:center}.text-primary[_ngcontent-%COMP%]{color:#3f51b5;text-decoration:none}.text-primary[_ngcontent-%COMP%]:hover{text-decoration:underline}.oauth-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{position:relative;text-align:center;margin:1.5rem 0}.oauth-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]:before{content:"";position:absolute;top:50%;left:0;right:0;height:1px;background:#e0e0e0}.oauth-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{background:#fff;padding:0 1rem;color:#666;font-size:.875rem}.oauth-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.75rem}.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:.75rem;padding:.75rem 1rem;width:100%;border-radius:8px;transition:all .2s ease}.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.25rem}.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500}.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #0000001a}.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed;transform:none;box-shadow:none}.lockout-notice[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff3cd,#ffeaa7);border:2px solid #ffc107;border-radius:12px;padding:1.5rem;margin-bottom:2rem;box-shadow:0 4px 12px #ffc10733}.lockout-notice[_ngcontent-%COMP%]   .lockout-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem;margin-bottom:1rem}.lockout-notice[_ngcontent-%COMP%]   .lockout-header[_ngcontent-%COMP%]   .warning-icon[_ngcontent-%COMP%]{color:#ff9800;font-size:2rem;width:2rem;height:2rem}.lockout-notice[_ngcontent-%COMP%]   .lockout-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;color:#e65100;font-weight:600}.lockout-notice[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#8f5000;line-height:1.5;margin-bottom:1rem}.lockout-notice[_ngcontent-%COMP%]   .unlock-options[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.75rem}@media (min-width: 576px){.lockout-notice[_ngcontent-%COMP%]   .unlock-options[_ngcontent-%COMP%]{flex-direction:row;justify-content:space-between}}.lockout-notice[_ngcontent-%COMP%]   .unlock-options[_ngcontent-%COMP%]   .unlock-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:.5rem;padding:.75rem 1rem;font-size:.875rem;font-weight:500;border-radius:8px;transition:all .2s ease;text-decoration:none}.lockout-notice[_ngcontent-%COMP%]   .unlock-options[_ngcontent-%COMP%]   .unlock-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 8px #0000001a}.lockout-notice[_ngcontent-%COMP%]   .unlock-options[_ngcontent-%COMP%]   .unlock-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.125rem;width:1.125rem;height:1.125rem}  .error-snackbar{background-color:#f44336!important;color:#fff!important}  .error-snackbar .mat-simple-snackbar-action{color:#ffcdd2!important}.twofa-help-section[_ngcontent-%COMP%]{margin-top:1.5rem;padding:1rem;background:#f8f9fa;border-radius:8px;border:1px solid #e9ecef}.twofa-help-section[_ngcontent-%COMP%]   .help-divider[_ngcontent-%COMP%]{position:relative;text-align:center;margin-bottom:1rem}.twofa-help-section[_ngcontent-%COMP%]   .help-divider[_ngcontent-%COMP%]:before{content:"";position:absolute;top:50%;left:0;right:0;height:1px;background:#dee2e6}.twofa-help-section[_ngcontent-%COMP%]   .help-divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{background:#f8f9fa;padding:0 .75rem;color:#6c757d;font-size:.875rem;font-weight:500}.twofa-help-section[_ngcontent-%COMP%]   .help-options[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem;margin-bottom:1rem}@media (min-width: 480px){.twofa-help-section[_ngcontent-%COMP%]   .help-options[_ngcontent-%COMP%]{flex-direction:row;justify-content:space-between}}.twofa-help-section[_ngcontent-%COMP%]   .help-button[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:.5rem;padding:.5rem 1rem;font-size:.875rem;border-radius:6px;transition:all .2s ease;background:#fff;border:1px solid #dee2e6;color:#495057}.twofa-help-section[_ngcontent-%COMP%]   .help-button[_ngcontent-%COMP%]:hover{background:#e9ecef;transform:translateY(-1px);box-shadow:0 2px 4px #0000001a}.twofa-help-section[_ngcontent-%COMP%]   .help-button.warn-button[_ngcontent-%COMP%]{border-color:#ffc107;color:#856404;background:#fff3cd}.twofa-help-section[_ngcontent-%COMP%]   .help-button.warn-button[_ngcontent-%COMP%]:hover{background:#ffeaa7;border-color:#ffb302}.twofa-help-section[_ngcontent-%COMP%]   .help-button.warn-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#ff9800}.twofa-help-section[_ngcontent-%COMP%]   .help-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1rem;width:1rem;height:1rem}.twofa-help-section[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]{font-size:.8125rem;color:#6c757d;text-align:center;line-height:1.4;margin:0;font-style:italic}']})}return r})();var ot=d(3925),T=d(3890);function rt(r,c){if(1&r&&(t.j41(0,"div",28)(1,"div",29),t.nrm(2,"div",30),t.k0s(),t.j41(3,"span",31),t.EFF(4),t.k0s()()),2&r){const e=t.XpG();t.R7$(2),t.xc7("width",e.passwordStrength,"%"),t.Y8G("ngClass",e.getPasswordStrengthColor()),t.R7$(),t.Y8G("ngClass",e.getPasswordStrengthColor()),t.R7$(),t.JRh(e.passwordStrengthText)}}function st(r,c){if(1&r&&(t.j41(0,"div",32),t.EFF(1),t.k0s()),2&r){const e=t.XpG();t.R7$(),t.SpI(" ",e.getFieldError("acceptTerms")," ")}}function it(r,c){1&r&&t.nrm(0,"mat-spinner",33)}function at(r,c){1&r&&(t.j41(0,"span"),t.EFF(1,"Create Account"),t.k0s())}let ct=(()=>{class r{constructor(e,n,o,s,u,b){this.formBuilder=e,this.authService=n,this.router=o,this.snackBar=s,this.dialog=u,this.accountDeletionService=b,this.loading=!1,this.hidePassword=!0,this.hideConfirmPassword=!0,this.passwordStrength=0,this.passwordStrengthText="",this.registerForm=this.formBuilder.group({firstName:["",[a.k0.required,a.k0.minLength(2),a.k0.maxLength(50)]],lastName:["",[a.k0.required,a.k0.minLength(2),a.k0.maxLength(50)]],email:["",[a.k0.required,a.k0.email]],phone:["",[a.k0.pattern(/^\+?[1-9]\d{1,14}$/)]],password:["",[a.k0.required,a.k0.minLength(8),this.passwordValidator]],confirmPassword:["",[a.k0.required]],acceptTerms:[!1,[a.k0.requiredTrue]]},{validators:this.passwordMatchValidator})}ngOnInit(){this.authService.isAuthenticated&&this.router.navigate(["/dashboard"]),this.registerForm.get("password")?.valueChanges.subscribe(e=>{this.updatePasswordStrength(e)})}onSubmit(){if(this.registerForm.invalid)return void this.markFormGroupTouched(this.registerForm);this.loading=!0;const e=this.registerForm.value;this.authService.register(e).subscribe({next:n=>{n.data?.hasPreservedData?this.handlePreservedDataRestoration(n.data.userId,e.email,n.data.preservedDataSummary):this.handleSuccessfulRegistration(),this.loading=!1},error:n=>{this.snackBar.open(n.message||"Registration failed","Close",{duration:5e3}),this.loading=!1}})}getFieldError(e){const n=this.registerForm.get(e);if(n?.errors&&n.touched){if(n.errors.required)return`${this.getFieldLabel(e)} is required`;if(n.errors.email)return"Please enter a valid email address";if(n.errors.minlength)return`${this.getFieldLabel(e)} must be at least ${n.errors.minlength.requiredLength} characters`;if(n.errors.maxlength)return`${this.getFieldLabel(e)} must not exceed ${n.errors.maxlength.requiredLength} characters`;if(n.errors.pattern)return"phone"===e?"Please enter a valid phone number":"Please enter a valid format";if(n.errors.passwordStrength)return n.errors.passwordStrength;if(n.errors.passwordMismatch)return"Passwords do not match";if(n.errors.requiredTrue)return"You must accept the terms and conditions"}return""}getFieldLabel(e){return{firstName:"First name",lastName:"Last name",email:"Email",phone:"Phone number",password:"Password",confirmPassword:"Confirm password"}[e]||e}passwordValidator(e){const n=e.value;if(!n)return null;const o=[];return n.length<8&&o.push("at least 8 characters"),/[A-Z]/.test(n)||o.push("one uppercase letter"),/[a-z]/.test(n)||o.push("one lowercase letter"),/\d/.test(n)||o.push("one number"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(n)||o.push("one special character"),o.length>0?{passwordStrength:`Password must contain ${o.join(", ")}`}:null}passwordMatchValidator(e){const n=e.get("password"),o=e.get("confirmPassword");return n&&o?n.value!==o.value?(o.setErrors({passwordMismatch:!0}),{passwordMismatch:!0}):(o.errors?.passwordMismatch&&(delete o.errors.passwordMismatch,0===Object.keys(o.errors).length&&o.setErrors(null)),null):null}updatePasswordStrength(e){if(!e)return this.passwordStrength=0,void(this.passwordStrengthText="");let n=0;n=[e.length>=8,/[A-Z]/.test(e),/[a-z]/.test(e),/\d/.test(e),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e)].filter(u=>u).length,this.passwordStrength=n/5*100,this.passwordStrengthText=["Very Weak","Weak","Fair","Good","Strong"][n-1]||"Very Weak"}getPasswordStrengthColor(){return this.passwordStrength<20?"warn":this.passwordStrength<40?"accent":"primary"}markFormGroupTouched(e){Object.keys(e.controls).forEach(n=>{e.get(n)?.markAsTouched()})}handlePreservedDataRestoration(e,n,o){this.snackBar.open("Registration successful! We found preserved data from your previous account.","Close",{duration:8e3});const s=this.dialog.open(ot.DataRestorationComponent,{width:"600px",disableClose:!0,data:{email:n,userId:e,preservedData:{hasPreservedData:!0,preservedDataSummary:o}}});s.componentInstance.restorationComplete.subscribe(u=>{this.snackBar.open("Data restoration completed successfully!","Close",{duration:5e3}),this.proceedToEmailVerification(),s.close()}),s.componentInstance.restorationSkipped.subscribe(()=>{this.snackBar.open("Registration completed. Data restoration skipped.","Close",{duration:5e3}),this.proceedToEmailVerification(),s.close()})}handleSuccessfulRegistration(){this.snackBar.open("Registration successful! Please check your email for verification.","Close",{duration:8e3}),this.proceedToEmailVerification()}proceedToEmailVerification(){this.router.navigate(["/auth/login"],{queryParams:{message:"Please verify your email before logging in."}})}static#t=this.\u0275fac=function(n){return new(n||r)(t.rXU(a.ok),t.rXU(C.u),t.rXU(h.Ix),t.rXU(F.UG),t.rXU(x.h),t.rXU(T.z))};static#e=this.\u0275cmp=t.VBU({type:r,selectors:[["app-register"]],standalone:!1,decls:84,vars:16,consts:[[1,"auth-container"],[1,"auth-card","fade-in"],[1,"auth-header"],[1,"security-badge"],[1,"auth-content"],[3,"ngSubmit","formGroup"],[1,"name-row"],["appearance","outline",1,"form-field","half-width"],["matInput","","formControlName","firstName","autocomplete","given-name"],["matSuffix",""],["matInput","","formControlName","lastName","autocomplete","family-name"],["appearance","outline",1,"form-field"],["matInput","","type","email","formControlName","email","autocomplete","email"],["matInput","","type","tel","formControlName","phone","placeholder","+1234567890","autocomplete","tel"],["matInput","","formControlName","password","autocomplete","new-password",3,"type"],["mat-icon-button","","matSuffix","","type","button",3,"click"],["class","password-strength",4,"ngIf"],["matInput","","formControlName","confirmPassword","autocomplete","new-password",3,"type"],[1,"checkbox-field"],["formControlName","acceptTerms"],["href","/terms","target","_blank"],["href","/privacy","target","_blank"],["class","error-message",4,"ngIf"],["mat-raised-button","","color","primary","type","submit",1,"submit-button",3,"disabled"],["diameter","20",4,"ngIf"],[4,"ngIf"],[1,"text-center","mt-3"],["routerLink","/auth/login",1,"text-primary"],[1,"password-strength"],[1,"strength-bar"],[1,"strength-fill",3,"ngClass"],[1,"strength-text",3,"ngClass"],[1,"error-message"],["diameter","20"]],template:function(n,o){if(1&n&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"h1"),t.EFF(4,"Create Account"),t.k0s(),t.j41(5,"p"),t.EFF(6,"Join our secure platform today"),t.k0s(),t.j41(7,"div",3)(8,"mat-icon"),t.EFF(9,"verified_user"),t.k0s(),t.EFF(10," Secure Registration "),t.k0s()(),t.j41(11,"div",4)(12,"form",5),t.bIt("ngSubmit",function(){return o.onSubmit()}),t.j41(13,"div",6)(14,"mat-form-field",7)(15,"mat-label"),t.EFF(16,"First Name"),t.k0s(),t.nrm(17,"input",8),t.j41(18,"mat-icon",9),t.EFF(19,"person"),t.k0s(),t.j41(20,"mat-error"),t.EFF(21),t.k0s()(),t.j41(22,"mat-form-field",7)(23,"mat-label"),t.EFF(24,"Last Name"),t.k0s(),t.nrm(25,"input",10),t.j41(26,"mat-icon",9),t.EFF(27,"person"),t.k0s(),t.j41(28,"mat-error"),t.EFF(29),t.k0s()()(),t.j41(30,"mat-form-field",11)(31,"mat-label"),t.EFF(32,"Email Address"),t.k0s(),t.nrm(33,"input",12),t.j41(34,"mat-icon",9),t.EFF(35,"email"),t.k0s(),t.j41(36,"mat-error"),t.EFF(37),t.k0s()(),t.j41(38,"mat-form-field",11)(39,"mat-label"),t.EFF(40,"Phone Number (Optional)"),t.k0s(),t.nrm(41,"input",13),t.j41(42,"mat-icon",9),t.EFF(43,"phone"),t.k0s(),t.j41(44,"mat-error"),t.EFF(45),t.k0s(),t.j41(46,"mat-hint"),t.EFF(47,"For SMS verification and 2FA"),t.k0s()(),t.j41(48,"mat-form-field",11)(49,"mat-label"),t.EFF(50,"Password"),t.k0s(),t.nrm(51,"input",14),t.j41(52,"button",15),t.bIt("click",function(){return o.hidePassword=!o.hidePassword}),t.j41(53,"mat-icon"),t.EFF(54),t.k0s()(),t.j41(55,"mat-error"),t.EFF(56),t.k0s()(),t.DNE(57,rt,5,5,"div",16),t.j41(58,"mat-form-field",11)(59,"mat-label"),t.EFF(60,"Confirm Password"),t.k0s(),t.nrm(61,"input",17),t.j41(62,"button",15),t.bIt("click",function(){return o.hideConfirmPassword=!o.hideConfirmPassword}),t.j41(63,"mat-icon"),t.EFF(64),t.k0s()(),t.j41(65,"mat-error"),t.EFF(66),t.k0s()(),t.j41(67,"div",18)(68,"mat-checkbox",19),t.EFF(69," I agree to the "),t.j41(70,"a",20),t.EFF(71,"Terms of Service"),t.k0s(),t.EFF(72," and "),t.j41(73,"a",21),t.EFF(74,"Privacy Policy"),t.k0s()(),t.DNE(75,st,2,1,"div",22),t.k0s(),t.j41(76,"button",23),t.DNE(77,it,1,0,"mat-spinner",24)(78,at,2,0,"span",25),t.k0s(),t.j41(79,"div",26)(80,"span"),t.EFF(81,"Already have an account? "),t.k0s(),t.j41(82,"a",27),t.EFF(83,"Sign In"),t.k0s()()()()()()),2&n){let s;t.R7$(12),t.Y8G("formGroup",o.registerForm),t.R7$(9),t.JRh(o.getFieldError("firstName")),t.R7$(8),t.JRh(o.getFieldError("lastName")),t.R7$(8),t.JRh(o.getFieldError("email")),t.R7$(8),t.JRh(o.getFieldError("phone")),t.R7$(6),t.Y8G("type",o.hidePassword?"password":"text"),t.R7$(3),t.JRh(o.hidePassword?"visibility_off":"visibility"),t.R7$(2),t.JRh(o.getFieldError("password")),t.R7$(),t.Y8G("ngIf",null==(s=o.registerForm.get("password"))?null:s.value),t.R7$(4),t.Y8G("type",o.hideConfirmPassword?"password":"text"),t.R7$(3),t.JRh(o.hideConfirmPassword?"visibility_off":"visibility"),t.R7$(2),t.JRh(o.getFieldError("confirmPassword")),t.R7$(9),t.Y8G("ngIf",o.getFieldError("acceptTerms")),t.R7$(),t.Y8G("disabled",o.loading),t.R7$(),t.Y8G("ngIf",o.loading),t.R7$(),t.Y8G("ngIf",!o.loading)}},dependencies:[p.YU,p.bT,a.qT,a.me,a.BC,a.cb,a.j4,a.JD,w.Wk,m.j,m.M,m.c,m.b,m.g,P.fg,f.$z,O.M,_.An,y.So,k.LG],styles:[".name-row[_ngcontent-%COMP%]{display:flex;gap:1rem}.name-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%]{flex:1}.password-strength[_ngcontent-%COMP%]{margin-bottom:1rem}.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]{width:100%;height:4px;background:#e0e0e0;border-radius:2px;overflow:hidden;margin-bottom:.5rem}.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill[_ngcontent-%COMP%]{height:100%;transition:width .3s ease,background-color .3s ease}.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.warn[_ngcontent-%COMP%]{background:#f44336}.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.accent[_ngcontent-%COMP%]{background:#ff9800}.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.primary[_ngcontent-%COMP%]{background:#4caf50}.password-strength[_ngcontent-%COMP%]   .strength-text[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500}.password-strength[_ngcontent-%COMP%]   .strength-text.warn[_ngcontent-%COMP%]{color:#f44336}.password-strength[_ngcontent-%COMP%]   .strength-text.accent[_ngcontent-%COMP%]{color:#ff9800}.password-strength[_ngcontent-%COMP%]   .strength-text.primary[_ngcontent-%COMP%]{color:#4caf50}.checkbox-field[_ngcontent-%COMP%]{margin:1.5rem 0}.checkbox-field[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{font-size:.875rem}.checkbox-field[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#3f51b5;text-decoration:none}.checkbox-field[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}@media (max-width: 768px){.name-row[_ngcontent-%COMP%]{flex-direction:column;gap:0}}"]})}return r})();var S=d(467),I=d(4843);function lt(r,c){1&r&&(t.j41(0,"div",8)(1,"div",9),t.nrm(2,"i",10),t.k0s(),t.j41(3,"h3"),t.EFF(4,"Verifying your email..."),t.k0s(),t.j41(5,"p"),t.EFF(6,"Please wait while we verify your email address."),t.k0s()())}function dt(r,c){if(1&r){const e=t.RV6();t.j41(0,"div",11)(1,"div",12),t.nrm(2,"i",13),t.k0s(),t.j41(3,"h3"),t.EFF(4,"Email Verified Successfully!"),t.k0s(),t.j41(5,"p"),t.EFF(6),t.k0s(),t.j41(7,"div",14)(8,"button",15),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.goToLogin())}),t.nrm(9,"i",16),t.EFF(10," Continue to Login "),t.k0s()()()}if(2&r){const e=t.XpG();t.R7$(6),t.JRh(e.message)}}function ut(r,c){if(1&r){const e=t.RV6();t.j41(0,"div",17)(1,"div",12),t.nrm(2,"i",13),t.k0s(),t.j41(3,"h3"),t.EFF(4,"Email Verified Successfully!"),t.k0s(),t.j41(5,"div",18)(6,"div",19),t.nrm(7,"i",20),t.k0s(),t.j41(8,"p"),t.EFF(9),t.k0s(),t.j41(10,"div",21)(11,"button",15),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.restoreData())}),t.nrm(12,"i",22),t.EFF(13," Restore My Data "),t.k0s(),t.j41(14,"button",23),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.skipRestore())}),t.nrm(15,"i",24),t.EFF(16," Skip & Continue "),t.k0s()()()()}if(2&r){const e=t.XpG();t.R7$(9),t.JRh(e.message)}}function gt(r,c){if(1&r){const e=t.RV6();t.j41(0,"div",25)(1,"div",26),t.nrm(2,"i",27),t.k0s(),t.j41(3,"h3"),t.EFF(4,"Verification Failed"),t.k0s(),t.j41(5,"p"),t.EFF(6),t.k0s(),t.j41(7,"div",14)(8,"button",15),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.goToLogin())}),t.nrm(9,"i",16),t.EFF(10," Go to Login "),t.k0s(),t.j41(11,"button",23),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.resendVerification())}),t.nrm(12,"i",28),t.EFF(13," Request New Verification "),t.k0s()()()}if(2&r){const e=t.XpG();t.R7$(6),t.JRh(e.message)}}function mt(r,c){if(1&r){const e=t.RV6();t.j41(0,"div",29)(1,"div",30),t.nrm(2,"i",31),t.k0s(),t.j41(3,"h3"),t.EFF(4,"Verification Link Expired"),t.k0s(),t.j41(5,"p"),t.EFF(6),t.k0s(),t.j41(7,"div",14)(8,"button",15),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.resendVerification())}),t.nrm(9,"i",28),t.EFF(10," Request New Verification "),t.k0s(),t.j41(11,"button",23),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.goToLogin())}),t.nrm(12,"i",16),t.EFF(13," Go to Login "),t.k0s()()()}if(2&r){const e=t.XpG();t.R7$(6),t.JRh(e.message)}}let ht=(()=>{class r{constructor(e,n,o,s){this.route=e,this.router=n,this.authService=o,this.accountDeletionService=s,this.status="verifying",this.message="",this.token="",this.userEmail="",this.userId="",this.hasPreservedData=!1,this.preservedDataResponse=null}ngOnInit(){console.log("\u{1f50d} Email verification component initialized"),this.route.queryParams.subscribe(e=>{this.token=e.token,console.log("\u{1f511} Raw token from URL:",this.token),console.log("\u{1f511} Token length:",this.token?.length),console.log("\u{1f511} Token preview:",this.token?`${this.token.substring(0,20)}...`:"No token"),this.token?(this.token=this.token.trim(),console.log("\u{1f527} Cleaned token preview:",`${this.token.substring(0,20)}...`),this.verifyEmail()):(this.status="error",this.message="No verification token provided.",console.log("\u274c No verification token in URL parameters"))})}verifyEmail(){var e=this;return(0,S.A)(function*(){try{e.status="verifying",console.log("\u{1f504} Starting email verification with token:",e.token.substring(0,20)+"...");const n=yield(0,I._)(e.authService.verifyEmail(e.token));console.log("\u2705 Email verification successful:",n),console.log("\u{1f50d} Response user object:",n.user),e.status="success",e.message="Your email has been successfully verified! Checking for preserved data...",n.user?.email?(e.userEmail=n.user.email,e.userId=n.user.id||"",console.log("\u2705 User email stored:",e.userEmail),console.log("\u2705 User ID stored:",e.userId),yield e.checkForPreservedData()):(console.log("\u26a0\ufe0f No user email in verification response"),e.completeVerification())}catch(n){console.error("\u274c Email verification failed:",n),e.status="error",400===n.status?(e.message="Invalid or expired verification token.",e.status="expired"):e.message=404===n.status?"User not found or already verified.":"An error occurred during verification. Please try again.",console.log("\u274c Error details:",{status:n.status,message:n.message,response:n.error})}})()}checkForPreservedData(){var e=this;return(0,S.A)(function*(){try{console.log("\u{1f50d} Checking for preserved data...");const n=e.route.snapshot.queryParams.email;console.log("\u{1f50d} Email from query params:",n),console.log("\u{1f50d} Email from userEmail property:",e.userEmail);const o=e.userEmail||n;if(console.log("\u{1f50d} Final email to check preserved data:",o),o){console.log("\u{1f50d} Making preserved data API call with email:",o),console.log("\u{1f50d} API URL will be:",`${e.accountDeletionService.apiUrl}/check-preserved-data/${encodeURIComponent(o)}`),yield new Promise(u=>setTimeout(u,1e3));const s=yield(0,I._)(e.accountDeletionService.checkPreservedData(o));console.log("\u{1f50d} Preserved data API response:",s),s.hasPreservedData?(console.log("\u{1f4e6} Found preserved data for user"),e.hasPreservedData=!0,e.preservedDataResponse=s,e.status="data-found",e.message="Your email has been verified successfully! We found your previous account data. Would you like to restore it?"):(console.log("\u2705 No preserved data found, proceeding normally"),e.completeVerification())}else console.log("\u26a0\ufe0f No email available for preserved data check"),e.completeVerification()}catch(n){console.log("\u274c Error checking preserved data:",n),console.log("\u274c Error details:",{status:n.status,message:n.message,error:n.error}),404===n.status||n.message?.includes("No preserved data")?(console.log("\u2139\ufe0f No preserved data found (404 or not found message)"),e.completeVerification()):(console.log("\u2139\ufe0f Assuming no preserved data due to error, continuing normally"),e.completeVerification())}})()}completeVerification(){this.status="success",this.message="Your email has been successfully verified! You can now log in and access all features.",setTimeout(()=>{this.showLoginOption()},3e3)}showLoginOption(){this.message+="\n\nClick below to proceed to login."}restoreData(){console.log("\u{1f504} Navigating to data restoration..."),console.log("\u{1f4e6} Preserved data to pass:",this.preservedDataResponse),console.log("\u{1f194} User ID to pass:",this.userId);const e=this.userEmail||this.route.snapshot.queryParams.email;this.router.navigate(["/data-restoration"],{queryParams:{email:e,userId:this.userId,fromVerification:"true"},state:{preservedData:this.preservedDataResponse,userEmail:e,userId:this.userId}})}skipRestore(){console.log("\u23ed\ufe0f User skipped data restoration from verification"),this.completeVerification()}goToLogin(){this.router.navigate(["/auth/login"])}resendVerification(){this.status="verifying",this.message="Please go back to the login page to request a new verification email.",setTimeout(()=>{this.router.navigate(["/auth/login"])},3e3)}static#t=this.\u0275fac=function(n){return new(n||r)(t.rXU(h.nX),t.rXU(h.Ix),t.rXU(C.u),t.rXU(T.z))};static#e=this.\u0275cmp=t.VBU({type:r,selectors:[["app-email-verification"]],standalone:!1,decls:10,vars:5,consts:[[1,"email-verification-container"],[1,"verification-card"],[1,"header"],["class","status-content verifying",4,"ngIf"],["class","status-content success",4,"ngIf"],["class","status-content data-found",4,"ngIf"],["class","status-content error",4,"ngIf"],["class","status-content expired",4,"ngIf"],[1,"status-content","verifying"],[1,"spinner"],[1,"fas","fa-spinner","fa-spin"],[1,"status-content","success"],[1,"success-icon"],[1,"fas","fa-check-circle"],[1,"actions"],[1,"btn","btn-primary",3,"click"],[1,"fas","fa-sign-in-alt"],[1,"status-content","data-found"],[1,"data-restore-section"],[1,"restore-icon"],[1,"fas","fa-database"],[1,"restore-actions"],[1,"fas","fa-download"],[1,"btn","btn-secondary",3,"click"],[1,"fas","fa-times"],[1,"status-content","error"],[1,"error-icon"],[1,"fas","fa-times-circle"],[1,"fas","fa-envelope"],[1,"status-content","expired"],[1,"warning-icon"],[1,"fas","fa-exclamation-triangle"]],template:function(n,o){1&n&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"h2"),t.EFF(4,"Email Verification"),t.k0s()(),t.DNE(5,lt,7,0,"div",3)(6,dt,11,1,"div",4)(7,ut,17,1,"div",5)(8,gt,14,1,"div",6)(9,mt,14,1,"div",7),t.k0s()()),2&n&&(t.R7$(5),t.Y8G("ngIf","verifying"===o.status),t.R7$(),t.Y8G("ngIf","success"===o.status),t.R7$(),t.Y8G("ngIf","data-found"===o.status),t.R7$(),t.Y8G("ngIf","error"===o.status),t.R7$(),t.Y8G("ngIf","expired"===o.status))},dependencies:[p.bT],styles:[".email-verification-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2);padding:20px}.verification-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;box-shadow:0 20px 60px #0000001a;max-width:500px;width:100%;overflow:hidden;animation:_ngcontent-%COMP%_slideUp .6s ease-out}@keyframes _ngcontent-%COMP%_slideUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}.header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;padding:30px;text-align:center}.header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:28px;font-weight:600}.status-content[_ngcontent-%COMP%]{padding:40px 30px;text-align:center}.status-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:20px 0 15px;font-size:24px;font-weight:600}.status-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:16px;line-height:1.5;margin-bottom:30px}.spinner[_ngcontent-%COMP%]{font-size:48px;color:#667eea;margin-bottom:20px}.success-icon[_ngcontent-%COMP%]{font-size:64px;color:#4caf50;margin-bottom:20px}.error-icon[_ngcontent-%COMP%]{font-size:64px;color:#f44336;margin-bottom:20px}.warning-icon[_ngcontent-%COMP%]{font-size:64px;color:#ff9800;margin-bottom:20px}.verifying[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#667eea}.success[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#4caf50}.error[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#f44336}.expired[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#ff9800}.actions[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:15px;margin-top:30px}.btn[_ngcontent-%COMP%]{padding:14px 28px;border:none;border-radius:8px;font-size:16px;font-weight:600;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;justify-content:center;gap:10px;text-decoration:none}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff}.btn-primary[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #667eea4d}.btn-secondary[_ngcontent-%COMP%]{background:#f8f9fa;color:#667eea;border:2px solid #e9ecef}.btn-secondary[_ngcontent-%COMP%]:hover{background:#e9ecef;border-color:#667eea;transform:translateY(-2px)}@media (max-width: 600px){.email-verification-container[_ngcontent-%COMP%]{padding:10px}.verification-card[_ngcontent-%COMP%]{margin:10px}.header[_ngcontent-%COMP%]{padding:20px}.header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:24px}.status-content[_ngcontent-%COMP%]{padding:30px 20px}.status-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px}.actions[_ngcontent-%COMP%]{margin-top:20px}}"]})}return r})();function pt(r,c){1&r&&(t.j41(0,"mat-error"),t.EFF(1," Email is required "),t.k0s())}function ft(r,c){1&r&&(t.j41(0,"mat-error"),t.EFF(1," Please enter a valid email address "),t.k0s())}function _t(r,c){1&r&&t.nrm(0,"mat-spinner",11),2&r&&t.Y8G("diameter",20)}function bt(r,c){if(1&r){const e=t.RV6();t.j41(0,"div")(1,"form",5),t.bIt("ngSubmit",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.onSubmit())}),t.j41(2,"mat-form-field",6)(3,"mat-label"),t.EFF(4,"Email Address"),t.k0s(),t.nrm(5,"input",7),t.j41(6,"mat-icon",8),t.EFF(7,"email"),t.k0s(),t.DNE(8,pt,2,0,"mat-error",2)(9,ft,2,0,"mat-error",2),t.k0s(),t.j41(10,"button",9),t.DNE(11,_t,1,1,"mat-spinner",10),t.EFF(12),t.k0s()()()}if(2&r){let e,n,o;const s=t.XpG();t.R7$(),t.Y8G("formGroup",s.forgotPasswordForm),t.R7$(4),t.AVh("is-invalid",(null==(e=s.forgotPasswordForm.get("email"))?null:e.invalid)&&(null==(e=s.forgotPasswordForm.get("email"))?null:e.touched)),t.R7$(3),t.Y8G("ngIf",null==(n=s.forgotPasswordForm.get("email"))?null:n.hasError("required")),t.R7$(),t.Y8G("ngIf",null==(o=s.forgotPasswordForm.get("email"))?null:o.hasError("email")),t.R7$(),t.Y8G("disabled",s.forgotPasswordForm.invalid||s.isLoading),t.R7$(),t.Y8G("ngIf",s.isLoading),t.R7$(),t.SpI(" ",s.isLoading?"Sending...":"Send Reset Instructions"," ")}}function kt(r,c){1&r&&(t.j41(0,"div",12)(1,"mat-icon",13),t.EFF(2,"check_circle"),t.k0s(),t.j41(3,"h3"),t.EFF(4,"Check Your Email"),t.k0s(),t.j41(5,"p"),t.EFF(6," We've sent password reset instructions to your email address. Please check your inbox and follow the link to reset your password. "),t.k0s(),t.j41(7,"p",14)(8,"strong"),t.EFF(9,"Note:"),t.k0s(),t.EFF(10," If you don't see the email in your inbox, please check your spam or junk folder. "),t.k0s()())}let Ft=(()=>{class r{constructor(e,n,o,s){this.fb=e,this.authService=n,this.router=o,this.snackBar=s,this.isLoading=!1,this.isEmailSent=!1}ngOnInit(){this.forgotPasswordForm=this.fb.group({email:["",[a.k0.required,a.k0.email]]})}onSubmit(){if(this.forgotPasswordForm.valid&&!this.isLoading){this.isLoading=!0;const e=this.forgotPasswordForm.get("email")?.value;this.authService.forgotPassword(e).subscribe({next:n=>{this.isLoading=!1,this.isEmailSent=!0,this.snackBar.open("Password reset instructions have been sent to your email address","Close",{duration:5e3,panelClass:["success-snackbar"]})},error:n=>{this.isLoading=!1,this.snackBar.open(n?.error?.message||"Failed to send reset email. Please try again.","Close",{duration:5e3,panelClass:["error-snackbar"]})}})}}goBackToLogin(){this.router.navigate(["/auth/login"])}static#t=this.\u0275fac=function(n){return new(n||r)(t.rXU(a.ok),t.rXU(C.u),t.rXU(h.Ix),t.rXU(F.UG))};static#e=this.\u0275cmp=t.VBU({type:r,selectors:[["app-forgot-password"]],standalone:!1,decls:17,vars:2,consts:[[1,"auth-container"],[1,"auth-card"],[4,"ngIf"],["class","success-message",4,"ngIf"],["mat-button","","color","accent",1,"full-width",3,"click"],[1,"auth-form",3,"ngSubmit","formGroup"],["appearance","outline",1,"full-width"],["matInput","","type","email","formControlName","email","placeholder","Enter your email address"],["matSuffix",""],["mat-raised-button","","color","primary","type","submit",1,"full-width","submit-btn",3,"disabled"],["style","display: inline-block; margin-right: 8px;",3,"diameter",4,"ngIf"],[2,"display","inline-block","margin-right","8px",3,"diameter"],[1,"success-message"],[1,"success-icon"],[1,"note"]],template:function(n,o){1&n&&(t.j41(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"mat-card-title")(4,"mat-icon"),t.EFF(5,"lock_reset"),t.k0s(),t.EFF(6," Forgot Password "),t.k0s(),t.j41(7,"mat-card-subtitle"),t.EFF(8," Enter your email address to receive password reset instructions "),t.k0s()(),t.j41(9,"mat-card-content"),t.DNE(10,bt,13,8,"div",2)(11,kt,11,0,"div",3),t.k0s(),t.j41(12,"mat-card-actions")(13,"button",4),t.bIt("click",function(){return o.goBackToLogin()}),t.j41(14,"mat-icon"),t.EFF(15,"arrow_back"),t.k0s(),t.EFF(16," Back to Login "),t.k0s()()()()),2&n&&(t.R7$(10),t.Y8G("ngIf",!o.isEmailSent),t.R7$(),t.Y8G("ngIf",o.isEmailSent))},dependencies:[p.bT,a.qT,a.me,a.BC,a.cb,a.j4,a.JD,g.RN,g.YY,g.m2,g.MM,g.Lc,g.dh,m.j,m.M,m.b,m.g,P.fg,f.$z,_.An,k.LG],styles:[".auth-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;padding:20px;background:linear-gradient(135deg,#667eea,#764ba2)}.auth-card[_ngcontent-%COMP%]{width:100%;max-width:450px;border-radius:12px;box-shadow:0 10px 30px #0000001a}.auth-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px;margin-top:20px}.full-width[_ngcontent-%COMP%]{width:100%}.submit-btn[_ngcontent-%COMP%]{height:48px;font-size:16px;font-weight:500;margin-top:10px}.success-message[_ngcontent-%COMP%]{text-align:center;padding:20px 0}.success-message[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%]{font-size:48px;height:48px;width:48px;color:#4caf50;margin-bottom:16px}.success-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#333;margin-bottom:16px;font-weight:500}.success-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;line-height:1.5;margin-bottom:12px}.success-message[_ngcontent-%COMP%]   .note[_ngcontent-%COMP%]{font-size:14px;background-color:#f5f5f5;padding:12px;border-radius:6px;border-left:4px solid #2196f3}mat-card-header[_ngcontent-%COMP%]{text-align:center;padding-bottom:0}mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:8px;font-size:24px;color:#333}mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:28px;height:28px;width:28px}mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%]{color:#666;margin-top:8px;font-size:14px}mat-card-actions[_ngcontent-%COMP%]{padding-top:0}.is-invalid[_ngcontent-%COMP%]{border-color:#f44336}@media (max-width: 600px){.auth-container[_ngcontent-%COMP%]{padding:10px}.auth-card[_ngcontent-%COMP%]{max-width:100%}}"]})}return r})();function Ct(r,c){if(1&r){const e=t.RV6();t.j41(0,"div",5)(1,"mat-icon",6),t.EFF(2,"error"),t.k0s(),t.j41(3,"h3"),t.EFF(4,"Invalid Reset Link"),t.k0s(),t.j41(5,"p"),t.EFF(6," This password reset link is invalid or has expired. Please request a new password reset. "),t.k0s(),t.j41(7,"button",7),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.requestNewReset())}),t.EFF(8," Request New Reset "),t.k0s()()}}function Pt(r,c){1&r&&(t.j41(0,"mat-error"),t.EFF(1," Password is required "),t.k0s())}function vt(r,c){1&r&&(t.j41(0,"mat-error"),t.EFF(1," Password must be at least 8 characters long "),t.k0s())}function wt(r,c){1&r&&(t.j41(0,"mat-error"),t.EFF(1," Password must contain uppercase, lowercase, number and special character "),t.k0s())}function xt(r,c){1&r&&(t.j41(0,"mat-error"),t.EFF(1," Please confirm your password "),t.k0s())}function Ot(r,c){1&r&&(t.j41(0,"mat-error"),t.EFF(1," Passwords do not match "),t.k0s())}function Mt(r,c){1&r&&t.nrm(0,"mat-spinner",16),2&r&&t.Y8G("diameter",20)}function yt(r,c){if(1&r){const e=t.RV6();t.j41(0,"div")(1,"form",8),t.bIt("ngSubmit",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.onSubmit())}),t.j41(2,"mat-form-field",9)(3,"mat-label"),t.EFF(4,"New Password"),t.k0s(),t.nrm(5,"input",10),t.j41(6,"button",11),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.hidePassword=!o.hidePassword)}),t.j41(7,"mat-icon"),t.EFF(8),t.k0s()(),t.DNE(9,Pt,2,0,"mat-error",3)(10,vt,2,0,"mat-error",3)(11,wt,2,0,"mat-error",3),t.k0s(),t.j41(12,"mat-form-field",9)(13,"mat-label"),t.EFF(14,"Confirm New Password"),t.k0s(),t.nrm(15,"input",12),t.j41(16,"button",11),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.hideConfirmPassword=!o.hideConfirmPassword)}),t.j41(17,"mat-icon"),t.EFF(18),t.k0s()(),t.DNE(19,xt,2,0,"mat-error",3)(20,Ot,2,0,"mat-error",3),t.k0s(),t.j41(21,"div",13)(22,"h4"),t.EFF(23,"Password Requirements:"),t.k0s(),t.j41(24,"ul")(25,"li")(26,"mat-icon"),t.EFF(27),t.k0s(),t.EFF(28," At least 8 characters "),t.k0s(),t.j41(29,"li")(30,"mat-icon"),t.EFF(31),t.k0s(),t.EFF(32," One uppercase letter "),t.k0s(),t.j41(33,"li")(34,"mat-icon"),t.EFF(35),t.k0s(),t.EFF(36," One lowercase letter "),t.k0s(),t.j41(37,"li")(38,"mat-icon"),t.EFF(39),t.k0s(),t.EFF(40," One number "),t.k0s(),t.j41(41,"li")(42,"mat-icon"),t.EFF(43),t.k0s(),t.EFF(44),t.k0s()()(),t.j41(45,"button",14),t.DNE(46,Mt,1,1,"mat-spinner",15),t.EFF(47),t.k0s()()()}if(2&r){let e,n,o,s,u,b,v;const l=t.XpG();t.R7$(),t.Y8G("formGroup",l.resetPasswordForm),t.R7$(4),t.AVh("is-invalid",(null==(e=l.resetPasswordForm.get("password"))?null:e.invalid)&&(null==(e=l.resetPasswordForm.get("password"))?null:e.touched)),t.Y8G("type",l.hidePassword?"password":"text"),t.R7$(3),t.JRh(l.hidePassword?"visibility_off":"visibility"),t.R7$(),t.Y8G("ngIf",null==(n=l.resetPasswordForm.get("password"))?null:n.hasError("required")),t.R7$(),t.Y8G("ngIf",null==(o=l.resetPasswordForm.get("password"))?null:o.hasError("minlength")),t.R7$(),t.Y8G("ngIf",null==(s=l.resetPasswordForm.get("password"))?null:s.hasError("pattern")),t.R7$(4),t.AVh("is-invalid",(null==(u=l.resetPasswordForm.get("confirmPassword"))?null:u.invalid)&&(null==(u=l.resetPasswordForm.get("confirmPassword"))?null:u.touched)),t.Y8G("type",l.hideConfirmPassword?"password":"text"),t.R7$(3),t.JRh(l.hideConfirmPassword?"visibility_off":"visibility"),t.R7$(),t.Y8G("ngIf",null==(b=l.resetPasswordForm.get("confirmPassword"))?null:b.hasError("required")),t.R7$(),t.Y8G("ngIf",null==(v=l.resetPasswordForm.get("confirmPassword"))?null:v.hasError("mismatch")),t.R7$(5),t.AVh("valid",l.hasMinLength()),t.R7$(2),t.JRh(l.hasMinLength()?"check":"close"),t.R7$(2),t.AVh("valid",l.hasUppercase()),t.R7$(2),t.JRh(l.hasUppercase()?"check":"close"),t.R7$(2),t.AVh("valid",l.hasLowercase()),t.R7$(2),t.JRh(l.hasLowercase()?"check":"close"),t.R7$(2),t.AVh("valid",l.hasNumber()),t.R7$(2),t.JRh(l.hasNumber()?"check":"close"),t.R7$(2),t.AVh("valid",l.hasSpecialChar()),t.R7$(2),t.JRh(l.hasSpecialChar()?"check":"close"),t.R7$(),t.E5c(' One special character (!@#$%^&*(),.?"',":","","{","","}","|<>) "),t.R7$(),t.Y8G("disabled",l.resetPasswordForm.invalid||l.isLoading),t.R7$(),t.Y8G("ngIf",l.isLoading),t.R7$(),t.SpI(" ",l.isLoading?"Resetting...":"Reset Password"," ")}}let Et=(()=>{class r{constructor(e,n,o,s,u){this.fb=e,this.authService=n,this.router=o,this.route=s,this.snackBar=u,this.isLoading=!1,this.hidePassword=!0,this.hideConfirmPassword=!0,this.token=null,this.isTokenValid=!0}ngOnInit(){this.token=this.route.snapshot.queryParamMap.get("token"),this.token||(this.isTokenValid=!1,this.snackBar.open("Invalid reset link. Please request a new password reset.","Close",{duration:5e3,panelClass:["error-snackbar"]})),this.resetPasswordForm=this.fb.group({password:["",[a.k0.required,a.k0.minLength(8),a.k0.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)]],confirmPassword:["",[a.k0.required]]},{validators:this.passwordMatchValidator})}passwordMatchValidator(e){const n=e.get("password"),o=e.get("confirmPassword");return n&&o&&n.value!==o.value?(o.setErrors({mismatch:!0}),{mismatch:!0}):null}hasMinLength(){return(this.resetPasswordForm.get("password")?.value||"").length>=8}hasUppercase(){const e=this.resetPasswordForm.get("password")?.value||"";return/[A-Z]/.test(e)}hasLowercase(){const e=this.resetPasswordForm.get("password")?.value||"";return/[a-z]/.test(e)}hasNumber(){const e=this.resetPasswordForm.get("password")?.value||"";return/[0-9]/.test(e)}hasSpecialChar(){const e=this.resetPasswordForm.get("password")?.value||"";return/[!@#$%^&*(),.?":{}|<>]/.test(e)}passwordsMatch(){const e=this.resetPasswordForm.get("password")?.value,n=this.resetPasswordForm.get("confirmPassword")?.value;return e===n&&e&&n}onSubmit(){if(this.resetPasswordForm.valid&&!this.isLoading&&this.token){this.isLoading=!0;const e=this.resetPasswordForm.get("password")?.value;this.authService.resetPassword({token:this.token,password:e,confirmPassword:e}).subscribe({next:n=>{this.isLoading=!1,this.snackBar.open("Password reset successfully! You can now login with your new password.","Close",{duration:5e3,panelClass:["success-snackbar"]}),setTimeout(()=>{this.router.navigate(["/auth/login"])},2e3)},error:n=>{this.isLoading=!1;const o=n?.error?.message||"Failed to reset password. Please try again.";this.snackBar.open(o,"Close",{duration:5e3,panelClass:["error-snackbar"]}),o.includes("Invalid or expired")&&setTimeout(()=>{this.router.navigate(["/auth/forgot-password"])},3e3)}})}}goBackToLogin(){this.router.navigate(["/auth/login"])}requestNewReset(){this.router.navigate(["/auth/forgot-password"])}static#t=this.\u0275fac=function(n){return new(n||r)(t.rXU(a.ok),t.rXU(C.u),t.rXU(h.Ix),t.rXU(h.nX),t.rXU(F.UG))};static#e=this.\u0275cmp=t.VBU({type:r,selectors:[["app-reset-password"]],standalone:!1,decls:17,vars:2,consts:[[1,"auth-container"],[1,"auth-card"],["class","error-message",4,"ngIf"],[4,"ngIf"],["mat-button","","color","accent",1,"full-width",3,"click"],[1,"error-message"],[1,"error-icon"],["mat-raised-button","","color","primary",1,"full-width",3,"click"],[1,"auth-form",3,"ngSubmit","formGroup"],["appearance","outline",1,"full-width"],["matInput","","formControlName","password","placeholder","Enter new password",3,"type"],["mat-icon-button","","matSuffix","","type","button",3,"click"],["matInput","","formControlName","confirmPassword","placeholder","Confirm new password",3,"type"],[1,"password-requirements"],["mat-raised-button","","color","primary","type","submit",1,"full-width","submit-btn",3,"disabled"],["style","display: inline-block; margin-right: 8px;",3,"diameter",4,"ngIf"],[2,"display","inline-block","margin-right","8px",3,"diameter"]],template:function(n,o){1&n&&(t.j41(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"mat-card-title")(4,"mat-icon"),t.EFF(5,"lock_reset"),t.k0s(),t.EFF(6," Reset Password "),t.k0s(),t.j41(7,"mat-card-subtitle"),t.EFF(8," Enter your new password below "),t.k0s()(),t.j41(9,"mat-card-content"),t.DNE(10,Ct,9,0,"div",2)(11,yt,48,35,"div",3),t.k0s(),t.j41(12,"mat-card-actions")(13,"button",4),t.bIt("click",function(){return o.goBackToLogin()}),t.j41(14,"mat-icon"),t.EFF(15,"arrow_back"),t.k0s(),t.EFF(16," Back to Login "),t.k0s()()()()),2&n&&(t.R7$(10),t.Y8G("ngIf",!o.isTokenValid),t.R7$(),t.Y8G("ngIf",o.isTokenValid))},dependencies:[p.bT,a.qT,a.me,a.BC,a.cb,a.j4,a.JD,g.RN,g.YY,g.m2,g.MM,g.Lc,g.dh,m.j,m.M,m.b,m.g,P.fg,f.$z,O.M,_.An,k.LG],styles:[".auth-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;padding:20px;background:linear-gradient(135deg,#667eea,#764ba2)}.auth-card[_ngcontent-%COMP%]{width:100%;max-width:450px;border-radius:12px;box-shadow:0 10px 30px #0000001a}.auth-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px;margin-top:20px}.full-width[_ngcontent-%COMP%]{width:100%}.submit-btn[_ngcontent-%COMP%]{height:48px;font-size:16px;font-weight:500;margin-top:10px}.password-requirements[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:16px;border-radius:6px;margin-top:10px}.password-requirements[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;font-size:14px;font-weight:500;color:#333}.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0}.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:4px 0;font-size:13px;color:#666;transition:color .3s ease}.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li.valid[_ngcontent-%COMP%]{color:#4caf50}.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li.valid[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#4caf50}.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;height:16px;width:16px;color:#f44336}.error-message[_ngcontent-%COMP%]{text-align:center;padding:20px 0}.error-message[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%]{font-size:48px;height:48px;width:48px;color:#f44336;margin-bottom:16px}.error-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#333;margin-bottom:16px;font-weight:500}.error-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;line-height:1.5;margin-bottom:20px}mat-card-header[_ngcontent-%COMP%]{text-align:center;padding-bottom:0}mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:8px;font-size:24px;color:#333}mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:28px;height:28px;width:28px}mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%]{color:#666;margin-top:8px;font-size:14px}mat-card-actions[_ngcontent-%COMP%]{padding-top:0}.is-invalid[_ngcontent-%COMP%]{border-color:#f44336}@media (max-width: 600px){.auth-container[_ngcontent-%COMP%]{padding:10px}.auth-card[_ngcontent-%COMP%]{max-width:100%}.password-requirements[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:13px}.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{font-size:12px}}"]})}return r})(),jt=(()=>{class r{constructor(){this.callbackData=null,this.isProcessing=!1,this.wasProcessedInSession=!1}setCallbackData(e){(!this.callbackData||!this.callbackData.processed)&&(this.callbackData={...e},console.log("\u{1f504} OAuth State Service - Callback data set:",this.callbackData))}getCallbackData(){return this.callbackData}markAsProcessed(){this.callbackData&&(this.callbackData.processed=!0,this.wasProcessedInSession=!0,console.log("\u2705 OAuth State Service - Marked as processed"))}clear(){this.callbackData=null,this.isProcessing=!1,console.log("\u{1f9f9} OAuth State Service - Cleared")}setProcessing(e){this.isProcessing=e}getProcessing(){return this.isProcessing}isAlreadyProcessed(){return!0===this.callbackData?.processed||this.wasProcessedInSession}resetSession(){this.callbackData=null,this.isProcessing=!1,this.wasProcessedInSession=!1,console.log("\u{1f504} OAuth State Service - Session reset")}static#t=this.\u0275fac=function(n){return new(n||r)};static#e=this.\u0275prov=i.jDH({token:r,factory:r.\u0275fac,providedIn:"root"})}return r})();function Rt(r,c){if(1&r&&(t.j41(0,"div",3)(1,"mat-card",4)(2,"mat-card-content")(3,"div",5),t.nrm(4,"mat-spinner",6),t.j41(5,"h2"),t.EFF(6),t.k0s(),t.j41(7,"p"),t.EFF(8),t.k0s()()()()()),2&r){const e=t.XpG();t.R7$(6),t.JRh(e.statusMessage),t.R7$(2),t.JRh(e.subMessage)}}function Tt(r,c){if(1&r){const e=t.RV6();t.j41(0,"div",7)(1,"mat-card",8)(2,"mat-card-content")(3,"div",9)(4,"mat-icon",10),t.EFF(5,"error"),t.k0s(),t.j41(6,"h2"),t.EFF(7,"Authentication Failed"),t.k0s(),t.j41(8,"p"),t.EFF(9),t.k0s(),t.j41(10,"button",11),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.goToLogin())}),t.EFF(11," Try Again "),t.k0s()()()()()}if(2&r){const e=t.XpG();t.R7$(9),t.JRh(e.errorMessage)}}function At(r,c){1&r&&(t.j41(0,"div",5),t.nrm(1,"mat-spinner",6),t.j41(2,"h2"),t.EFF(3,"Processing your request..."),t.k0s(),t.j41(4,"p"),t.EFF(5,"Please wait while we disable two-factor authentication on your account."),t.k0s()())}function Gt(r,c){if(1&r&&(t.j41(0,"div",17)(1,"h3"),t.EFF(2,"Account Details:"),t.k0s(),t.j41(3,"p")(4,"strong"),t.EFF(5,"Email:"),t.k0s(),t.EFF(6),t.k0s(),t.j41(7,"p")(8,"strong"),t.EFF(9,"Name:"),t.k0s(),t.EFF(10),t.k0s()()),2&r){const e=t.XpG(2);t.R7$(6),t.SpI(" ",e.userInfo.email),t.R7$(4),t.Lme(" ",e.userInfo.firstName," ",e.userInfo.lastName)}}function $t(r,c){if(1&r){const e=t.RV6();t.j41(0,"div",7)(1,"mat-icon",8),t.EFF(2,"check_circle"),t.k0s(),t.j41(3,"h2"),t.EFF(4,"Two-Factor Authentication Disabled"),t.k0s(),t.j41(5,"p",9),t.EFF(6," Two-factor authentication has been successfully disabled on your account. "),t.k0s(),t.DNE(7,Gt,11,3,"div",10),t.j41(8,"div",11)(9,"mat-icon",12),t.EFF(10,"warning"),t.k0s(),t.j41(11,"div",13)(12,"h4"),t.EFF(13,"Important Security Notice"),t.k0s(),t.j41(14,"p"),t.EFF(15,"Your account is now less secure without two-factor authentication. Consider:"),t.k0s(),t.j41(16,"ul")(17,"li"),t.EFF(18,"Using a strong, unique password"),t.k0s(),t.j41(19,"li"),t.EFF(20,"Re-enabling 2FA from your profile settings"),t.k0s(),t.j41(21,"li"),t.EFF(22,"Monitoring your account for any suspicious activity"),t.k0s()()()(),t.j41(23,"div",14)(24,"button",15),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.goToDashboard())}),t.j41(25,"mat-icon"),t.EFF(26,"dashboard"),t.k0s(),t.EFF(27," Go to Dashboard "),t.k0s(),t.j41(28,"button",16),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.goToProfile())}),t.j41(29,"mat-icon"),t.EFF(30,"settings"),t.k0s(),t.EFF(31," Profile Settings "),t.k0s()()()}if(2&r){const e=t.XpG();t.R7$(7),t.Y8G("ngIf",e.userInfo)}}function Lt(r,c){if(1&r){const e=t.RV6();t.j41(0,"div",18)(1,"mat-icon",19),t.EFF(2,"error"),t.k0s(),t.j41(3,"h2"),t.EFF(4,"Unable to Disable 2FA"),t.k0s(),t.j41(5,"p",20),t.EFF(6),t.k0s(),t.j41(7,"div",21)(8,"h4"),t.EFF(9,"What you can do:"),t.k0s(),t.j41(10,"ul")(11,"li"),t.EFF(12,"Check if the link has expired (links are valid for 1 hour)"),t.k0s(),t.j41(13,"li"),t.EFF(14,"Request a new disable email from the login screen"),t.k0s(),t.j41(15,"li"),t.EFF(16,"Contact support if you continue to have issues"),t.k0s()()(),t.j41(17,"div",14)(18,"button",15),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.goToLogin())}),t.j41(19,"mat-icon"),t.EFF(20,"login"),t.k0s(),t.EFF(21," Back to Login "),t.k0s(),t.j41(22,"button",16),t.bIt("click",function(){i.eBV(e);const o=t.XpG();return i.Njj(o.requestNewLink())}),t.j41(23,"mat-icon"),t.EFF(24,"email"),t.k0s(),t.EFF(25," Request New Link "),t.k0s()()()}if(2&r){const e=t.XpG();t.R7$(6),t.JRh(e.errorMessage)}}const Vt=[{path:"",redirectTo:"login",pathMatch:"full"},{path:"login",component:nt},{path:"register",component:ct},{path:"verify-email",component:ht},{path:"forgot-password",component:Ft},{path:"reset-password",component:Et},{path:"oauth-success",component:(()=>{class r{constructor(e,n,o,s,u,b){this.route=e,this.router=n,this.authService=o,this.oauthService=s,this.snackBar=u,this.oauthStateService=b,this.isProcessing=!0,this.hasError=!1,this.statusMessage="Completing authentication...",this.subMessage="Please wait while we finalize your login.",this.errorMessage="",this.isRedirecting=!1}ngOnInit(){console.log("\u{1f504} OAuth Success Component - Initializing"),this.handleOAuthCallback()}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}handleOAuthCallback(){if(this.isRedirecting)return void console.log("\u{1f504} OAuth Success - Already redirecting, skipping callback handling");const e=this.oauthStateService.getCallbackData();return e&&!e.processed?(console.log("\ufffd OAuth Success - Using existing callback data:",e),void this.processCallbackData(e)):this.oauthStateService.isAlreadyProcessed()?(console.log("\u2705 OAuth Success - Already processed, showing success state"),void(this.isRedirecting||(this.isRedirecting=!0,this.statusMessage="Authentication completed",this.subMessage="Redirecting to dashboard...",setTimeout(()=>{console.log("\u{1f504} OAuth Success - Redirecting via window.location"),this.oauthStateService.resetSession(),window.location.href="/dashboard"},1e3)))):void(this.subscription=this.route.queryParams.subscribe(n=>{if(console.log("\ufffd OAuth Success - Query params received:",n),0===Object.keys(n).length){console.log("\u26a0\ufe0f OAuth Success - No query params, checking for existing data");const s=this.oauthStateService.getCallbackData();return void(s&&!s.processed?this.processCallbackData(s):this.oauthStateService.isAlreadyProcessed()?(console.log("\u2705 OAuth Success - Already processed, redirecting to dashboard"),this.isRedirecting||(this.isRedirecting=!0,this.statusMessage="Authentication completed",this.subMessage="Redirecting to dashboard...",setTimeout(()=>{console.log("\u{1f504} OAuth Success - Redirecting via window.location (subscription)"),this.oauthStateService.resetSession(),window.location.href="/dashboard"},500))):(console.log("\u274c OAuth Success - No authentication data available"),this.showError("No authentication data received. Please try again.")))}const o={code:n.code,token:n.token,isNewUser:"true"===n.isNewUser,provider:n.provider,error:n.error};this.oauthStateService.setCallbackData(o),this.processCallbackData(o)}))}processCallbackData(e){return console.log("\u{1f50d} OAuth Success - Processing callback data:",e),e.error?(console.log("\u274c OAuth Success - Error found:",e.error),void this.showError(`Authentication failed: ${e.error}`)):e.code?(console.log("\u{1f510} OAuth Success - Processing authorization code (secure flow)"),void this.exchangeCodeForToken(e.code,e.provider)):e.token?(console.log("\u26a0\ufe0f OAuth Success - Processing direct token (legacy flow - less secure)"),void this.completeAuthentication(e.token,e.isNewUser,e.provider)):(console.log("\u274c OAuth Success - No authorization code or token received"),void this.showError("No authentication credentials received. Please try again."))}exchangeCodeForToken(e,n){console.log("\u{1f510} OAuth Success - Exchanging authorization code for token"),this.oauthStateService.getProcessing()?console.log("\u26a0\ufe0f OAuth Success - Already processing, skipping"):(this.oauthStateService.setProcessing(!0),this.statusMessage="Securing your connection...",this.subMessage="Exchanging authorization code for access token",this.oauthService.exchangeAuthorizationCode(e).subscribe({next:o=>{console.log("\u2705 OAuth Success - Token exchange successful"),this.completeAuthentication(o.token,o.isNewUser||!1,o.provider||n)},error:o=>{console.error("\u274c OAuth Success - Token exchange failed:",o),this.oauthStateService.setProcessing(!1);let s="Failed to complete secure authentication.";400===o.status?s="Invalid or expired authorization code. Please try again.":404===o.status&&(s="User account not found. Please try again."),this.showError(s)}}))}completeAuthentication(e,n,o){this.statusMessage=n?"Creating your account...":"Signing you in...",this.subMessage=`Authenticated with ${this.formatProviderName(o)}`,setTimeout(()=>{try{console.log("\u{1f510} OAuth Success - Setting token and refreshing user data"),this.authService.setToken(e),this.authService.isAuthenticated?(console.log("\u2705 OAuth Success - Authentication state set successfully"),this.handleSuccessfulAuth(n,o)):(console.log("\u26a0\ufe0f OAuth Success - Auth state not set, refreshing user data from server"),this.authService.refreshUserData().subscribe({next:s=>{console.log("\u2705 OAuth Success - User data refreshed:",s),this.handleSuccessfulAuth(n,o)},error:s=>{console.error("\u274c OAuth Success - Failed to refresh user data:",s),this.authService.getToken()?(console.log("\u{1f504} OAuth Success - Token exists, proceeding with redirect"),this.handleSuccessfulAuth(n,o)):this.showError("Failed to complete authentication. Please try again.")}}))}catch(s){console.error("\u274c OAuth Success - Token processing error:",s),this.oauthStateService.setProcessing(!1),this.showError("Failed to process authentication. Please try again.")}},0)}handleSuccessfulAuth(e,n){this.oauthStateService.markAsProcessed(),this.statusMessage="Success!",this.subMessage=e?`Welcome! Your account has been created and linked to ${this.formatProviderName(n)}.`:`Welcome back! You've been signed in with ${this.formatProviderName(n)}.`,this.snackBar.open(e?`Account created and signed in with ${this.formatProviderName(n)}!`:`Signed in with ${this.formatProviderName(n)}!`,"Close",{duration:3e3});const o=this.authService.isAuthenticated,s=!!this.authService.getToken(),u=this.authService.currentUserValue;console.log("\u{1f50d} OAuth Success - Pre-redirect auth check:",{isAuthenticated:o,hasToken:s,hasUser:!!u,userEmailVerified:u?.emailVerified}),o&&s?(console.log("\u2705 OAuth Success - User is fully authenticated, redirecting to dashboard"),setTimeout(()=>{this.redirectToDashboard()},1500)):(console.error("\u274c OAuth Success - Authentication state check failed"),console.error("\u274c isAuthenticated:",o,"hasToken:",s,"user:",u),this.showError("Authentication was successful but user state could not be established. Please try logging in again."))}redirectToDashboard(){this.isRedirecting?console.log("\u{1f504} OAuth Success - Already redirecting, preventing duplicate"):(this.isRedirecting=!0,console.log("\u{1f504} OAuth Success - Starting navigation to dashboard"),this.router.navigate(["/dashboard"]).then(e=>{e?(console.log("\u2705 OAuth Success - Angular router navigation successful"),this.oauthStateService.resetSession()):(console.log("\u26a0\ufe0f OAuth Success - Angular router navigation failed, using window.location"),setTimeout(()=>{this.oauthStateService.resetSession(),window.location.href="/dashboard"},500))},e=>{console.error("\u274c OAuth Success - Angular router navigation error:",e),setTimeout(()=>{this.oauthStateService.resetSession(),window.location.href="/dashboard"},500)}))}showError(e){this.isProcessing=!1,this.hasError=!0,this.errorMessage=e,this.snackBar.open(e,"Close",{duration:6e3,panelClass:["error-snackbar"]})}formatProviderName(e){switch(e?.toLowerCase()){case"google":return"Google";case"github":return"GitHub";case"microsoft":return"Microsoft";default:return e||"OAuth Provider"}}goToLogin(){this.router.navigate(["/auth/login"])}static#t=this.\u0275fac=function(n){return new(n||r)(t.rXU(h.nX),t.rXU(h.Ix),t.rXU(C.u),t.rXU(j.T),t.rXU(F.UG),t.rXU(jt))};static#e=this.\u0275cmp=t.VBU({type:r,selectors:[["app-oauth-success"]],standalone:!1,decls:3,vars:2,consts:[[1,"oauth-success-container"],["class","loading-card",4,"ngIf"],["class","error-card",4,"ngIf"],[1,"loading-card"],[1,"success-card"],[1,"loading-content"],["diameter","60"],[1,"error-card"],[1,"error-card-content"],[1,"error-content"],["color","warn",1,"error-icon"],["mat-raised-button","","color","primary",1,"retry-button",3,"click"]],template:function(n,o){1&n&&(t.j41(0,"div",0),t.DNE(1,Rt,9,2,"div",1)(2,Tt,12,1,"div",2),t.k0s()),2&n&&(t.R7$(),t.Y8G("ngIf",o.isProcessing),t.R7$(),t.Y8G("ngIf",o.hasError))},dependencies:[p.bT,g.RN,g.m2,f.$z,_.An,k.LG],styles:[".oauth-success-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2);padding:20px}.success-card[_ngcontent-%COMP%], .error-card-content[_ngcontent-%COMP%]{max-width:500px;width:100%;margin:0 auto;box-shadow:0 10px 30px #0000004d;border-radius:15px}.loading-content[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.loading-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:20px 0 10px;color:#333;font-weight:500}.loading-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin-bottom:20px;line-height:1.5}.error-icon[_ngcontent-%COMP%]{font-size:48px;height:48px;width:48px;margin-bottom:20px}.retry-button[_ngcontent-%COMP%]{margin-top:20px;padding:12px 30px;border-radius:25px}mat-spinner[_ngcontent-%COMP%]{margin:0 auto 20px}@media (max-width: 600px){.oauth-success-container[_ngcontent-%COMP%]{padding:10px}.loading-content[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]{padding:30px 15px}}"]})}return r})()},{path:"oauth-error",component:(()=>{class r{constructor(e,n){this.route=e,this.router=n,this.errorMessage="An unexpected error occurred during authentication."}ngOnInit(){this.route.queryParams.subscribe(e=>{e.error&&(this.errorMessage=this.formatErrorMessage(e.error))})}formatErrorMessage(e){const n={access_denied:"You cancelled the authentication process. Please try again if you want to sign in.",invalid_request:"The authentication request was invalid. Please try again.",unauthorized_client:"The application is not authorized to perform this action.",unsupported_response_type:"The authentication provider doesn't support this login method.",invalid_scope:"The requested permissions are not valid.",server_error:"The authentication server encountered an error. Please try again later.",temporarily_unavailable:"The authentication service is temporarily unavailable. Please try again later."},o=e.toLowerCase();for(const[s,u]of Object.entries(n))if(o.includes(s))return u;return e.length>50?e:`Authentication failed: ${e}. Please try again or contact support if the problem persists.`}tryAgain(){this.router.navigate(["/auth/login"])}goHome(){this.router.navigate(["/"])}static#t=this.\u0275fac=function(n){return new(n||r)(t.rXU(h.nX),t.rXU(h.Ix))};static#e=this.\u0275cmp=t.VBU({type:r,selectors:[["app-oauth-error"]],standalone:!1,decls:15,vars:1,consts:[[1,"oauth-error-container"],[1,"error-card"],[1,"error-content"],["color","warn",1,"error-icon"],[1,"error-message"],[1,"error-actions"],["mat-raised-button","","color","primary",1,"action-button",3,"click"],["mat-stroked-button","",1,"action-button",3,"click"]],template:function(n,o){1&n&&(t.j41(0,"div",0)(1,"mat-card",1)(2,"mat-card-content")(3,"div",2)(4,"mat-icon",3),t.EFF(5,"error_outline"),t.k0s(),t.j41(6,"h2"),t.EFF(7,"OAuth Authentication Failed"),t.k0s(),t.j41(8,"p",4),t.EFF(9),t.k0s(),t.j41(10,"div",5)(11,"button",6),t.bIt("click",function(){return o.tryAgain()}),t.EFF(12," Try Again "),t.k0s(),t.j41(13,"button",7),t.bIt("click",function(){return o.goHome()}),t.EFF(14," Go to Home "),t.k0s()()()()()()),2&n&&(t.R7$(9),t.JRh(o.errorMessage))},dependencies:[g.RN,g.m2,f.$z,_.An],styles:[".oauth-error-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2);padding:20px}.error-card[_ngcontent-%COMP%]{max-width:500px;width:100%;margin:0 auto;box-shadow:0 10px 30px #0000004d;border-radius:15px}.error-content[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.error-icon[_ngcontent-%COMP%]{font-size:64px;height:64px;width:64px;margin-bottom:20px}.error-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:20px 0 15px;color:#333;font-weight:500}.error-message[_ngcontent-%COMP%]{color:#666;margin-bottom:30px;line-height:1.6;word-wrap:break-word}.error-actions[_ngcontent-%COMP%]{display:flex;gap:15px;justify-content:center;flex-wrap:wrap}.action-button[_ngcontent-%COMP%]{padding:12px 24px;border-radius:25px;min-width:120px}@media (max-width: 600px){.oauth-error-container[_ngcontent-%COMP%]{padding:10px}.error-content[_ngcontent-%COMP%]{padding:30px 15px}.error-actions[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.action-button[_ngcontent-%COMP%]{width:100%;max-width:200px}}"]})}return r})()},{path:"disable-2fa",component:(()=>{class r{constructor(e,n,o,s,u){this.route=e,this.router=n,this.twoFactorService=o,this.authService=s,this.snackBar=u,this.loading=!0,this.success=!1,this.errorMessage="",this.userInfo=null}ngOnInit(){const e=this.route.snapshot.queryParamMap.get("token");if(!e)return this.loading=!1,this.success=!1,void(this.errorMessage="Invalid or missing disable token. Please request a new disable link.");this.confirmDisable2FA(e)}confirmDisable2FA(e){this.twoFactorService.confirmDisable2FA(e).subscribe({next:n=>{this.loading=!1,this.success=n.success,this.userInfo=n.user,n.success?(this.authService.currentUserValue&&({...this.authService.currentUserValue}.twoFactorEnabled=!1),this.snackBar.open("Two-factor authentication has been disabled successfully.","Close",{duration:5e3,panelClass:["success-snackbar"]})):this.errorMessage=n.message||"Failed to disable 2FA"},error:n=>{console.error("Error confirming 2FA disable:",n),this.loading=!1,this.success=!1,this.errorMessage=400===n.status?"The disable link has expired or is invalid. Please request a new one.":n.error?.message||"An unexpected error occurred. Please try again."}})}goToDashboard(){this.router.navigate(["/dashboard"])}goToProfile(){this.router.navigate(["/profile"])}goToLogin(){this.router.navigate(["/auth/login"])}requestNewLink(){this.router.navigate(["/auth/login"],{queryParams:{message:'Please use the "Request disable 2FA" option to get a new link.'}})}static#t=this.\u0275fac=function(n){return new(n||r)(t.rXU(h.nX),t.rXU(h.Ix),t.rXU(R.f),t.rXU(C.u),t.rXU(F.UG))};static#e=this.\u0275cmp=t.VBU({type:r,selectors:[["app-disable-2fa-confirm"]],standalone:!1,decls:5,vars:3,consts:[[1,"disable-confirm-container"],[1,"disable-confirm-card"],["class","loading-state",4,"ngIf"],["class","success-state",4,"ngIf"],["class","error-state",4,"ngIf"],[1,"loading-state"],["diameter","60"],[1,"success-state"],[1,"success-icon"],[1,"success-message"],["class","user-info",4,"ngIf"],[1,"security-notice"],[1,"warning-icon"],[1,"notice-content"],[1,"action-buttons"],["mat-raised-button","","color","primary",3,"click"],["mat-button","",3,"click"],[1,"user-info"],[1,"error-state"],[1,"error-icon"],[1,"error-message"],[1,"error-help"]],template:function(n,o){1&n&&(t.j41(0,"div",0)(1,"mat-card",1),t.DNE(2,At,6,0,"div",2)(3,$t,32,1,"div",3)(4,Lt,26,1,"div",4),t.k0s()()),2&n&&(t.R7$(2),t.Y8G("ngIf",o.loading),t.R7$(),t.Y8G("ngIf",!o.loading&&o.success),t.R7$(),t.Y8G("ngIf",!o.loading&&!o.success))},dependencies:[p.bT,g.RN,f.$z,_.An,k.LG],styles:[".disable-confirm-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2);padding:20px}.disable-confirm-card[_ngcontent-%COMP%]{max-width:600px;width:100%;padding:40px;text-align:center;box-shadow:0 10px 30px #0000004d}.loading-state[_ngcontent-%COMP%]{padding:20px}.loading-state[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:20px 0 10px;color:#333}.loading-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin-bottom:0}.success-state[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%]{padding:20px}.success-icon[_ngcontent-%COMP%]{font-size:80px;width:80px;height:80px;color:#4caf50;margin-bottom:20px}.error-icon[_ngcontent-%COMP%]{font-size:80px;width:80px;height:80px;color:#f44336;margin-bottom:20px}.success-state[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0 0 20px;color:#333}.success-message[_ngcontent-%COMP%], .error-message[_ngcontent-%COMP%]{font-size:16px;color:#666;margin-bottom:30px;line-height:1.6}.user-info[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:8px;padding:20px;margin:20px 0;text-align:left}.user-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 15px;color:#333;font-size:16px}.user-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0;color:#555}.security-notice[_ngcontent-%COMP%]{background:#fff3e0;border:1px solid #ff9800;border-radius:8px;padding:20px;margin:20px 0;display:flex;align-items:flex-start;gap:16px;text-align:left}.warning-icon[_ngcontent-%COMP%]{color:#ff9800;font-size:32px;width:32px;height:32px;flex-shrink:0}.notice-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 10px;color:#f57c00;font-size:16px}.notice-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 10px;color:#e65100}.notice-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:0;padding-left:20px;color:#e65100}.notice-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:5px}.error-help[_ngcontent-%COMP%]{background:#ffebee;border:1px solid #f44336;border-radius:8px;padding:20px;margin:20px 0;text-align:left}.error-help[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 10px;color:#d32f2f;font-size:16px}.error-help[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:0;padding-left:20px;color:#c62828}.error-help[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:8px}.action-buttons[_ngcontent-%COMP%]{margin-top:30px;display:flex;gap:16px;justify-content:center;flex-wrap:wrap}.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:150px}@media (max-width: 600px){.disable-confirm-container[_ngcontent-%COMP%]{padding:10px}.disable-confirm-card[_ngcontent-%COMP%]{padding:20px}.action-buttons[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;max-width:250px}}"]})}return r})()}];let Dt=(()=>{class r{static#t=this.\u0275fac=function(n){return new(n||r)};static#e=this.\u0275mod=t.$C({type:r});static#n=this.\u0275inj=i.G2t({imports:[p.MD,a.X1,w.iI.forChild(Vt),g.Hu,A.M,P.fS,f.Hl,_.m_,y.g7,k.D6,F._T,G.w,x.l,$.M]})}return r})()}}]);