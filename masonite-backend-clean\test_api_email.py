#!/usr/bin/env python3
"""
Simple API test to verify OTP functionality via HTTP requests
Tests the actual endpoints that the frontend will use
"""

import requests
import json

BASE_URL = "http://localhost:3002/api"

def test_cors_headers():
    """Test CORS configuration"""
    try:
        print("🔍 Testing CORS Headers...")
        
        # Test OPTIONS request
        response = requests.options(f"{BASE_URL}/otp/send-email", headers={
            'Origin': 'http://localhost:4200',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
        })
        
        print(f"OPTIONS Response Status: {response.status_code}")
        print(f"CORS Headers: {dict(response.headers)}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ CORS test failed: {str(e)}")
        return False

def test_otp_send_email():
    """Test OTP email sending via API"""
    try:
        print("\n🔍 Testing OTP Email Send API...")
        
        payload = {
            "email": "<EMAIL>",
            "type": "login"
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Origin': 'http://localhost:4200'
        }
        
        print(f"🚀 Sending POST to {BASE_URL}/otp/send-email")
        print(f"📝 Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(
            f"{BASE_URL}/otp/send-email",
            json=payload,
            headers=headers,
            timeout=30
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"📊 Response Data: {json.dumps(response_data, indent=2)}")
        except:
            print(f"📊 Response Text: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ OTP send test failed: {str(e)}")
        return False

def test_forgot_password():
    """Test forgot password functionality"""
    try:
        print("\n🔍 Testing Forgot Password API...")
        
        payload = {
            "email": "<EMAIL>"
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Origin': 'http://localhost:4200'
        }
        
        print(f"🚀 Sending POST to {BASE_URL}/auth/forgot-password")
        
        response = requests.post(
            f"{BASE_URL}/auth/forgot-password",
            json=payload,
            headers=headers,
            timeout=30
        )
        
        print(f"📊 Response Status: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"📊 Response Data: {json.dumps(response_data, indent=2)}")
        except:
            print(f"📊 Response Text: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ Forgot password test failed: {str(e)}")
        return False

def test_server_connection():
    """Test basic server connectivity"""
    try:
        print("🔍 Testing Server Connection...")
        
        response = requests.get(f"{BASE_URL}/auth/login", timeout=10)
        print(f"📊 Server Response Status: {response.status_code}")
        
        return response.status_code in [200, 405, 422]  # Any response means server is up
        
    except Exception as e:
        print(f"❌ Server connection failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 API Email Functionality Test Suite")
    print("=" * 50)
    print(f"🌐 Testing against: {BASE_URL}")
    print()
    
    # Test 1: Server connectivity
    server_test = test_server_connection()
    
    # Test 2: CORS headers
    cors_test = test_cors_headers()
    
    # Test 3: OTP email sending
    otp_test = test_otp_send_email()
    
    # Test 4: Forgot password
    forgot_test = test_forgot_password()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"  Server Connection: {'✅ PASS' if server_test else '❌ FAIL'}")
    print(f"  CORS Headers: {'✅ PASS' if cors_test else '❌ FAIL'}")
    print(f"  OTP Email: {'✅ PASS' if otp_test else '❌ FAIL'}")
    print(f"  Forgot Password: {'✅ PASS' if forgot_test else '❌ FAIL'}")
    
    if otp_test and forgot_test:
        print("\n🎉 Email functionality is working!")
    else:
        print("\n⚠️  Some email tests failed. Check the responses above.")
