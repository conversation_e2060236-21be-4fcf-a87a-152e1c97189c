"""
Test Account Deletion Email Functionality
Tests that emails are actually sent when account deletion is requested
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3002"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "password123"

def test_account_deletion_email():
    """Test the complete account deletion email flow"""
    
    print("🧪 Testing Account Deletion Email Functionality")
    print("=" * 50)
    
    # Step 1: Login to get a valid token
    print("1. Logging in to get authentication token...")
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    try:
        login_response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        print(f"   Login Status: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get('access_token')
            print(f"   ✅ Login successful, token obtained")
        else:
            print(f"   ❌ Login failed: {login_response.text}")
            return
            
    except Exception as e:
        print(f"   ❌ Login error: {str(e)}")
        return
    
    # Step 2: Request account deletion
    print("\n2. Requesting account deletion...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    deletion_data = {
        "preservePaymentData": True,
        "preserveTransactionHistory": True,
        "preserveProfileData": False,
        "preserveSecurityLogs": False,
        "customRetentionPeriod": 30,
        "reason": "Testing email functionality"
    }
    
    try:
        deletion_response = requests.post(
            f"{BASE_URL}/api/account/request-deletion", 
            json=deletion_data,
            headers=headers
        )
        
        print(f"   Deletion Request Status: {deletion_response.status_code}")
        print(f"   Response Headers: {dict(deletion_response.headers)}")
        
        if deletion_response.status_code == 200:
            deletion_result = deletion_response.json()
            print(f"   ✅ Deletion request successful")
            print(f"   📧 Response: {json.dumps(deletion_result, indent=2)}")
            
            # Check if confirmation token is returned
            if 'confirmationToken' in deletion_result:
                print(f"   🔗 Confirmation token: {deletion_result['confirmationToken']}")
            
        else:
            print(f"   ❌ Deletion request failed")
            print(f"   Error: {deletion_response.text}")
            
    except Exception as e:
        print(f"   ❌ Deletion request error: {str(e)}")
    
    # Step 3: Check deletion status
    print("\n3. Checking deletion status...")
    
    try:
        status_response = requests.get(
            f"{BASE_URL}/api/account/deletion-status",
            headers=headers
        )
        
        print(f"   Status Check: {status_response.status_code}")
        
        if status_response.status_code == 200:
            status_result = status_response.json()
            print(f"   📊 Status: {json.dumps(status_result, indent=2)}")
        else:
            print(f"   ❌ Status check failed: {status_response.text}")
            
    except Exception as e:
        print(f"   ❌ Status check error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("✅ Test completed! Check server logs for email sending details.")
    print("📧 Check your email inbox for the deletion confirmation email.")

if __name__ == "__main__":
    test_account_deletion_email()
