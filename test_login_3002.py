"""
Test login endpoint on port 3002 to verify the auth.login fix
"""
import requests
import json

def test_login_endpoint():
    """Test the login endpoint with known user credentials on port 3002"""
    
    # API endpoint on port 3002
    url = "http://localhost:3002/api/auth/login"
    
    # Test credentials - using the verified test user
    payload = {
        "email": "<EMAIL>",
        "password": "Password123!"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Origin': 'http://localhost:3000'
    }
    
    print("🧪 Testing login endpoint on port 3002...")
    print(f"📡 URL: {url}")
    print(f"📝 Payload: {payload}")
    
    try:
        # Test with a longer timeout
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"✅ Response Status: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            print(f"📄 Response Body: {json.dumps(response.json(), indent=2)}")
        else:
            print(f"📄 Response Text: {response.text[:500]}...")
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the server running?")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_otp_verify_endpoint():
    """Test the OTP verify endpoint to check if the auth.login fix works"""
    
    url = "http://localhost:3002/api/otp/verify"
    
    payload = {
        "identifier": "<EMAIL>",
        "otp": "123456"  # This will likely fail but tests the endpoint
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Origin': 'http://localhost:3000'
    }
    
    print("\n🧪 Testing OTP verify endpoint...")
    print(f"📡 URL: {url}")
    print(f"📝 Payload: {payload}")
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=10)
        
        print(f"✅ Response Status: {response.status_code}")
        if response.headers.get('content-type', '').startswith('application/json'):
            print(f"📄 Response Body: {json.dumps(response.json(), indent=2)}")
        else:
            print(f"📄 Response Text: {response.text[:500]}...")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_login_endpoint()
    test_otp_verify_endpoint()
