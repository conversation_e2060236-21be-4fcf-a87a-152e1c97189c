#!/usr/bin/env python3
"""
Test for no data preservation scenario - all checkboxes unchecked
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3002/api"

def test_no_data_preservation():
    """Test when all data preservation checkboxes are unchecked"""
    email = f"test.none.{int(time.time())}@example.com"
    password = "TestPassword123!"
    
    print("🧪 Testing No Data Preservation (All Unchecked)")
    print("=" * 60)
    
    # Step 1: Register user
    print("📝 Step 1: Registering user...")
    register_data = {
        "email": email,
        "password": password,
        "confirmPassword": password,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+1234567890"
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=register_data)
    if response.status_code not in [200, 201]:
        print(f"❌ Registration failed: {response.text}")
        return False
    
    user_data = response.json()
    user_id = user_data.get('userId') or user_data.get('user', {}).get('id')
    token = user_data.get('token')
    print(f"✅ User registered with ID: {user_id}")
    
    # Step 2: Request deletion with NO data preservation
    print("\n📝 Step 2: Requesting deletion with NO data preservation...")
    deletion_data = {
        "preservePaymentData": False,
        "preserveTransactionHistory": False,
        "preserveProfileData": False,
        "preserveSecurityLogs": False,
        "customRetentionPeriod": 30,
        "reason": "Testing no data preservation"
    }
    
    response = requests.post(f"{BASE_URL}/account/request-deletion", 
                           json=deletion_data,
                           headers={'Authorization': f'Bearer {token}'})
    
    if response.status_code != 200:
        print(f"❌ Deletion request failed: {response.text}")
        return False
    
    deletion_response = response.json()
    deletion_id = deletion_response['deletionId']
    confirmation_token = deletion_response['confirmationToken']
    print(f"✅ Deletion requested with ID: {deletion_id}")
    
    # Step 3: Confirm deletion
    print("\n📝 Step 3: Confirming deletion...")
    response = requests.post(f"{BASE_URL}/account/confirm-deletion", 
                           json={'token': confirmation_token})
    
    if response.status_code != 200:
        print(f"❌ Deletion confirmation failed: {response.text}")
        return False
    
    confirm_response = response.json()
    print("✅ Deletion confirmed")
    print(f"📄 Preserved data summary: {confirm_response.get('preservedDataSummary', {})}")
    
    # Step 4: Check preserved data
    print("\n📝 Step 4: Checking preserved data...")
    response = requests.post(f"{BASE_URL}/account/check-preserved-data", 
                           json={'email': email})
    
    if response.status_code != 200:
        print(f"❌ Check preserved data failed: {response.text}")
        return False
    
    check_response = response.json()
    has_preserved = check_response.get('hasPreservedData', False)
    preserved_summary = check_response.get('preservedDataSummary', {})
    
    print(f"📊 Has preserved data: {has_preserved}")
    print(f"📊 Preserved data summary: {preserved_summary}")
    
    # Step 5: Validate expectations
    print("\n📝 Step 5: Validating expectations...")
    
    if has_preserved:
        print(f"❌ FAIL: Expected no preserved data but found: {preserved_summary}")
        return False
    
    if preserved_summary:
        print(f"❌ FAIL: Expected empty preserved summary but found: {preserved_summary}")
        return False
    
    print("✅ PASS: Correctly no preserved data when all categories unchecked")
    
    # Step 6: Test signup behavior
    print("\n📝 Step 6: Testing signup behavior...")
    response = requests.post(f"{BASE_URL}/auth/signup", json=register_data)
    
    if response.status_code not in [200, 201]:
        print(f"❌ Second registration failed: {response.text}")
        return False
    
    signup_response = response.json()
    signup_has_preserved = signup_response.get('hasPreservedData', False)
    signup_preserved_summary = signup_response.get('preservedDataSummary', {})
    
    print(f"📊 Signup response - Has preserved data: {signup_has_preserved}")
    print(f"📊 Signup response - Preserved summary: {signup_preserved_summary}")
    
    # Validate signup behavior matches deletion behavior
    if signup_has_preserved != has_preserved:
        print(f"❌ FAIL: Signup preserved data ({signup_has_preserved}) doesn't match deletion check ({has_preserved})")
        return False
    
    if signup_preserved_summary != preserved_summary:
        print(f"❌ FAIL: Signup preserved summary doesn't match deletion check")
        print(f"   Signup: {signup_preserved_summary}")
        print(f"   Check:  {preserved_summary}")
        return False
    
    print("✅ PASS: Signup behavior matches deletion behavior")
    print("✅ PASS: All validations successful!")
    
    return True

def main():
    """Main test function"""
    print("🚀 Starting No Data Preservation Test")
    print("Testing when all 4 data preservation checkboxes are unchecked")
    print("=" * 80)
    
    success = test_no_data_preservation()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 TEST PASSED! No data preservation logic is working correctly!")
    else:
        print("❌ TEST FAILED! No data preservation logic needs more work!")
    print("🏁 Test completed!")

if __name__ == "__main__":
    main()
