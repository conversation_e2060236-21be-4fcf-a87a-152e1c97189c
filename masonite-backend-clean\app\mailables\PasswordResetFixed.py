from masonite.mail import Mailable
from masonite.environment import env


class PasswordReset(Mailable):
    """
    Password Reset Mailable
    Compatible with LoopBack password reset flow
    """
    
    def __init__(self, user, token):
        """Initialize with user and reset token"""
        super().__init__()
        self.user = user
        self.token = token
    
    def build(self):
        """Build the password reset email"""
        reset_url = f"{env('FRONTEND_URL', 'http://localhost:4200')}/auth/reset-password?token={self.token}"
        
        html_content = f"""
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Reset Your Password</h2>
            <p>Hello {self.user.name},</p>
            <p>You requested to reset your password. Click the button below to set a new password:</p>
            <div style="text-align: center; margin: 30px 0;">
                <a href="{reset_url}"
                   style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                    Reset Password
                </a>
            </div>
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #666;">{reset_url}</p>
            <p style="color: #666; font-size: 12px;">This link will expire in 1 hour.</p>
            <p style="color: #666; font-size: 12px;">If you didn't request a password reset, please ignore this email.</p>
        </div>
        """
        
        text_content = f"""
        Reset Your Password
        
        Hello {self.user.name},
        
        You requested to reset your password. Please click the link below to set a new password:
        
        {reset_url}
        
        This link will expire in 1 hour.
        
        If you didn't request a password reset, please ignore this email.
        """
        
        return (
            self.subject("Reset Your Password")
            .from_(env("MAIL_FROM", "<EMAIL>"))
            .text(text_content)
            .html(html_content)
        )
