#!/usr/bin/env python3
"""
Script to PERMANENTLY (HARD) delete specific users by ID
Using multiple methods to ensure permanent deletion
"""

import os
import sys

# Add the masonite project to path
sys.path.append(os.path.join(os.path.dirname(__file__)))

# Initialize Masonite application
from wsgi import application

# Now we can import models after app initialization
from app.models.User import User

def force_delete_users():
    """Permanently delete specific users by ID (hard delete)"""
    user_ids_to_delete = [255, 265,270,271,275]  # IDs from the search results
    
    print("🚨 Starting PERMANENT deletion of test users by ID...")
    print("⚠️  WARNING: This will permanently remove users from the database!")
    
    for user_id in user_ids_to_delete:
        try:
            # Method 1: Try to find the user (including soft-deleted ones)
            user = User.with_trashed().find(user_id)
            
            if user:
                email = user.email
                username = user.name or 'Unknown'
                
                print(f"📍 Found user: {email} (ID: {user_id}, Name: {username})")
                
                # Method 2: Try force_delete() method if available (this should permanently delete)
                try:
                    if hasattr(user, 'force_delete'):
                        user.force_delete()
                        print(f"✅ PERMANENTLY deleted user: {email} (ID: {user_id}) using force_delete()")
                    else:
                        print(f"⚠️  force_delete() method not available on User model")
                        
                        # Method 3: Try using query builder to permanently delete
                        result = User.where('id', user_id).force_delete()
                        if result:
                            print(f"✅ PERMANENTLY deleted user: {email} (ID: {user_id}) using query force_delete()")
                        else:
                            print(f"⚠️  Query force_delete failed for user ID: {user_id}")
                            
                except Exception as force_error:
                    print(f"❌ Force delete failed: {str(force_error)}")
                    
                    # Method 4: Try direct model deletion without soft delete
                    try:
                        # This should permanently delete by bypassing soft delete
                        User.without_global_scopes().where('id', user_id).delete()
                        print(f"✅ PERMANENTLY deleted user: {email} (ID: {user_id}) using without_global_scopes")
                    except Exception as scope_error:
                        print(f"❌ without_global_scopes delete failed: {str(scope_error)}")
                        
                        # Method 5: Try using raw query through the model
                        try:
                            from masoniteorm.query import QueryBuilder
                            query = QueryBuilder().table('users').where('id', user_id).delete()
                            print(f"✅ PERMANENTLY deleted user ID {user_id} using raw QueryBuilder")
                        except Exception as raw_error:
                            print(f"❌ Raw query delete failed: {str(raw_error)}")
                    
            else:
                print(f"ℹ️  User not found with ID: {user_id} (may already be permanently deleted)")
                
        except Exception as e:
            print(f"❌ Error permanently deleting user ID {user_id}: {str(e)}")
    
    print("\n🎉 PERMANENT cleanup completed!")
    print("📋 Verifying deletion results...")
    
    # Verify the users are gone
    for user_id in user_ids_to_delete:
        try:
            # Check if user exists (including soft-deleted)
            user = User.with_trashed().find(user_id)
            if user:
                print(f"⚠️  User ID {user_id} still exists: {user.email}")
                if user.deleted_at:
                    print(f"    (Status: Soft deleted at {user.deleted_at})")
                else:
                    print(f"    (Status: Active)")
            else:
                print(f"✅ User ID {user_id} permanently deleted")
        except Exception as e:
            print(f"✅ User ID {user_id} not found (permanently deleted): {str(e)}")

if __name__ == "__main__":
    force_delete_users()
