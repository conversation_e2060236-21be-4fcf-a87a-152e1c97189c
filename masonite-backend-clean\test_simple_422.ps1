#!/usr/bin/env pwsh
# Simple test script for 422 error handling

$BaseUrl = "http://localhost:3002/api"

function Test-Signup {
    Write-Host "🧪 Testing Signup Endpoint..." -ForegroundColor Yellow
    
    # Test with missing fields (should return 422)
    $malformedData = @{
        email = "<EMAIL>"
        password = "Test123!"
        # Missing required fields: firstName, lastName, confirmPassword
    } | ConvertTo-Json
    
    $headers = @{
        'Content-Type' = 'application/json'
        'Origin' = 'http://localhost:4200'
    }
    
    try {
        $response = Invoke-WebRequest -Uri "$BaseUrl/auth/signup" -Method POST -Body $malformedData -Headers $headers -UseBasicParsing
        Write-Host "❌ Should have failed with 422 but got: $($response.StatusCode)" -ForegroundColor Red
        return $false
    } catch {
        if ($_.Exception.Response.StatusCode.Value__ -eq 422) {
            Write-Host "✅ Signup correctly returns 422 for invalid data" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Got unexpected status: $($_.Exception.Response.StatusCode.Value__)" -ForegroundColor Red
            return $false
        }
    }
}

function Test-ChangePassword {
    Write-Host "🧪 Testing Change Password Endpoint..." -ForegroundColor Yellow
    
    # First create a valid user
    $validSignup = @{
        email = "<EMAIL>"
        password = "Test123!"
        confirmPassword = "Test123!"
        username = "testchange3"
        firstName = "Test"
        lastName = "User"
    } | ConvertTo-Json
    
    $headers = @{
        'Content-Type' = 'application/json'
        'Origin' = 'http://localhost:4200'
    }
    
    try {
        # Create user and get token
        $signupResponse = Invoke-WebRequest -Uri "$BaseUrl/auth/signup" -Method POST -Body $validSignup -Headers $headers -UseBasicParsing
        if ($signupResponse.StatusCode -eq 201) {
            $signupData = $signupResponse.Content | ConvertFrom-Json
            $token = $signupData.token
            
            # Test change password with missing currentPassword (should return 422)
            $malformedChangePassword = @{
                newPassword = "NewPass123!"
                # Missing currentPassword
            } | ConvertTo-Json
            
            $authHeaders = $headers.Clone()
            $authHeaders['Authorization'] = "Bearer $token"
            
            try {
                $changeResponse = Invoke-WebRequest -Uri "$BaseUrl/auth/change-password" -Method POST -Body $malformedChangePassword -Headers $authHeaders -UseBasicParsing
                Write-Host "❌ Should have failed with 422 but got: $($changeResponse.StatusCode)" -ForegroundColor Red
                return $false
            } catch {
                if ($_.Exception.Response.StatusCode.Value__ -eq 422) {
                    Write-Host "✅ Change password correctly returns 422 for invalid data" -ForegroundColor Green
                    return $true
                } else {
                    Write-Host "❌ Got unexpected status: $($_.Exception.Response.StatusCode.Value__)" -ForegroundColor Red
                    return $false
                }
            }
        } else {
            Write-Host "❌ Failed to create test user for change password test" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error during change password test: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main execution
Write-Host "🚀 Starting 422 Error Handling Tests..." -ForegroundColor Cyan
Write-Host "=" * 50

$signupResult = Test-Signup
$changePasswordResult = Test-ChangePassword

Write-Host ""
Write-Host "📊 Test Results:" -ForegroundColor Cyan
Write-Host "=" * 30
Write-Host "Signup 422 handling: $(if ($signupResult) { '✅ PASS' } else { '❌ FAIL' })"
Write-Host "Change Password 422 handling: $(if ($changePasswordResult) { '✅ PASS' } else { '❌ FAIL' })"

if ($signupResult -and $changePasswordResult) {
    Write-Host ""
    Write-Host "🎉 All tests passed! The 422 error handling is working correctly." -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "⚠️ Some tests failed. Please check the endpoints." -ForegroundColor Yellow
}
