#!/usr/bin/env python3
"""
Comprehensive test for data preservation logic fix
Tests the scenario where user preferences should be respected
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3002/api"
TEST_EMAIL = f"test.preservation.{int(time.time())}@example.com"
TEST_PASSWORD = "TestPassword123!"

def register_user(email, password):
    """Register a new user"""
    register_data = {
        "email": email,
        "password": password,
        "confirmPassword": password,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+**********"
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=register_data)
    if response.status_code == 201:
        user_data = response.json()
        return {
            'success': True,
            'user_id': user_data.get('user', {}).get('id'),
            'token': user_data.get('token')
        }
    else:
        return {'success': False, 'error': response.text}

def request_deletion(token, preserve_data=False):
    """Request account deletion"""
    deletion_data = {
        "preservePaymentData": preserve_data,
        "preserveTransactionHistory": preserve_data,
        "preserveProfileData": preserve_data,
        "preserveSecurityLogs": preserve_data,
        "customRetentionPeriod": 30,
        "reason": "Testing data preservation logic"
    }
    
    response = requests.post(f"{BASE_URL}/account/request-deletion", 
                           json=deletion_data,
                           headers={'Authorization': f'Bearer {token}'})
    
    if response.status_code == 200:
        return {'success': True, 'data': response.json()}
    else:
        return {'success': False, 'error': response.text}

def confirm_deletion(token):
    """Confirm account deletion"""
    response = requests.post(f"{BASE_URL}/account/confirm-deletion", 
                           json={'token': token})
    
    if response.status_code == 200:
        return {'success': True, 'data': response.json()}
    else:
        return {'success': False, 'error': response.text}

def check_preserved_data(email):
    """Check if user has preserved data"""
    response = requests.post(f"{BASE_URL}/account/check-preserved-data", 
                           json={'email': email})
    
    if response.status_code == 200:
        return {'success': True, 'data': response.json()}
    else:
        return {'success': False, 'error': response.text}

def test_data_preservation_logic():
    """Test the complete data preservation logic"""
    print("🧪 Testing Data Preservation Logic Fix")
    print("=" * 60)
    
    # Step 1: Register first user
    print("📝 Step 1: Registering first user...")
    result = register_user(TEST_EMAIL, TEST_PASSWORD)
    if not result['success']:
        print(f"❌ Registration failed: {result['error']}")
        return False
    
    user_id_1 = result['user_id']
    token_1 = result['token']
    print(f"✅ User registered with ID: {user_id_1}")
    
    # Step 2: Request deletion WITH data preservation
    print("\n📝 Step 2: Requesting deletion WITH data preservation...")
    result = request_deletion(token_1, preserve_data=True)
    if not result['success']:
        print(f"❌ Deletion request failed: {result['error']}")
        return False
    
    deletion_id_1 = result['data']['deletionId']
    confirmation_token_1 = result['data']['confirmationToken']
    print(f"✅ Deletion requested with ID: {deletion_id_1}")
    
    # Step 3: Confirm first deletion
    print("\n📝 Step 3: Confirming first deletion...")
    result = confirm_deletion(confirmation_token_1)
    if not result['success']:
        print(f"❌ Deletion confirmation failed: {result['error']}")
        return False
    
    print("✅ First deletion confirmed")
    
    # Step 4: Check preserved data (should exist)
    print("\n📝 Step 4: Checking preserved data after first deletion...")
    result = check_preserved_data(TEST_EMAIL)
    if not result['success']:
        print(f"❌ Check preserved data failed: {result['error']}")
        return False
    
    has_preserved = result['data'].get('hasPreservedData', False)
    print(f"✅ Has preserved data: {has_preserved}")
    
    if not has_preserved:
        print("❌ INCORRECT: Should have preserved data from first deletion")
        return False
    else:
        print("✅ CORRECT: Found preserved data from first deletion")
    
    # Step 5: Register second user with same email (will auto-restore)
    print("\n📝 Step 5: Registering second user with same email...")
    response = requests.post(f"{BASE_URL}/auth/signup", json={
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD,
        "confirmPassword": TEST_PASSWORD,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+**********"
    })

    if response.status_code == 200:
        # User was automatically restored
        user_data = response.json()
        user_id_2 = user_data.get('userId')
        token_2 = user_data.get('token')
        print(f"✅ User automatically restored with ID: {user_id_2}")
        print(f"📊 Preserved data summary: {user_data.get('preservedDataSummary')}")
    elif response.status_code == 201:
        # New user created
        user_data = response.json()
        user_id_2 = user_data.get('user', {}).get('id')
        token_2 = user_data.get('token')
        print(f"✅ New user created with ID: {user_id_2}")
    else:
        print(f"❌ Registration failed: {response.text}")
        return False
    
    # Step 6: Request deletion WITHOUT data preservation
    print("\n📝 Step 6: Requesting deletion WITHOUT data preservation...")
    result = request_deletion(token_2, preserve_data=False)
    if not result['success']:
        print(f"❌ Second deletion request failed: {result['error']}")
        return False
    
    deletion_id_2 = result['data']['deletionId']
    confirmation_token_2 = result['data']['confirmationToken']
    print(f"✅ Second deletion requested with ID: {deletion_id_2}")
    
    # Step 7: Confirm second deletion
    print("\n📝 Step 7: Confirming second deletion...")
    result = confirm_deletion(confirmation_token_2)
    if not result['success']:
        print(f"❌ Second deletion confirmation failed: {result['error']}")
        return False
    
    print("✅ Second deletion confirmed")
    
    # Step 8: Check preserved data (should NOT exist due to our fix)
    print("\n📝 Step 8: Checking preserved data after second deletion...")
    result = check_preserved_data(TEST_EMAIL)
    if not result['success']:
        print(f"❌ Check preserved data failed: {result['error']}")
        return False
    
    has_preserved = result['data'].get('hasPreservedData', False)
    print(f"✅ Has preserved data: {has_preserved}")
    
    if has_preserved:
        print("❌ INCORRECT: Should NOT have preserved data after choosing not to preserve")
        print("❌ This indicates the data preservation logic fix is NOT working")
        return False
    else:
        print("✅ CORRECT: No preserved data found after choosing not to preserve")
        print("✅ Data preservation logic fix is working correctly!")
        return True

def main():
    """Main test function"""
    print("🚀 Starting Data Preservation Logic Test")
    print("Testing the fix for persistent data restoration issue")
    print("=" * 60)
    
    success = test_data_preservation_logic()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED! Data preservation logic fix is working correctly!")
    else:
        print("❌ TESTS FAILED! Data preservation logic fix needs more work!")
    print("🏁 Tests completed!")

if __name__ == "__main__":
    main()
