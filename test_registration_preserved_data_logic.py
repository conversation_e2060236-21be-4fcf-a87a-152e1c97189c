#!/usr/bin/env python3
"""
Quick Test: Registration Logic for Preserved Data
Tests the registration logic to ensure preserved data is only cleaned up when explicitly requested.
"""

import requests
import json
import sys
import time

# Configuration
BASE_URL = "http://localhost:3002"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "TestPassword123!"

def print_header(title):
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_step(step, description):
    print(f"\n🔸 Step {step}: {description}")

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_warning(message):
    print(f"⚠️  {message}")

def make_request(method, endpoint, data=None, headers=None):
    """Make HTTP request with error handling"""
    try:
        url = f"{BASE_URL}{endpoint}"
        if headers is None:
            headers = {'Content-Type': 'application/json'}
        
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, headers=headers)
        else:
            raise Exception(f"Unsupported method: {method}")
        
        return response
    except requests.exceptions.ConnectionError:
        print_error("Cannot connect to server. Make sure the server is running on http://localhost:3002")
        sys.exit(1)
    except Exception as e:
        print_error(f"Request failed: {str(e)}")
        return None

def register_user(email, password, first_name, last_name, restore_preserved_data=None):
    """Register a new user"""
    data = {
        'email': email,
        'password': password,
        'confirmPassword': password,  # Add required confirmPassword field
        'firstName': first_name,
        'lastName': last_name
    }
    
    # Add the restore flag if specified
    if restore_preserved_data is not None:
        data['restorePreservedData'] = restore_preserved_data
    
    return make_request('POST', '/api/auth/signup', data)

def check_preserved_data_get(email):
    """Check preserved data using GET endpoint"""
    return make_request('GET', f'/api/account/check-preserved-data/{email}')

def check_preserved_data_post(email):
    """Check preserved data using POST endpoint"""
    data = {'email': email}
    return make_request('POST', '/api/account/check-preserved-data', data)

def login_user(email, password):
    """Login user"""
    data = {
        'email': email,
        'password': password
    }
    return make_request('POST', '/api/auth/login', data)

def delete_account(token, preserve_data=True):
    """Delete user account with preservation preferences"""
    data = {
        'preserve_payment_data': preserve_data,
        'preserve_transaction_history': preserve_data,
        'preserve_profile_data': preserve_data,
        'preserve_security_logs': preserve_data,
        'reason': 'Testing preserved data logic'
    }
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    return make_request('POST', '/api/account/request-deletion', data, headers)

def confirm_deletion(confirmation_token):
    """Confirm account deletion"""
    data = {
        'token': confirmation_token
    }
    return make_request('POST', '/api/account/confirm-deletion', data)

def main():
    print_header("REGISTRATION PRESERVED DATA LOGIC TEST")
    print("Testing the registration logic to ensure preserved data is only cleaned up when explicitly requested.")
    
    # Test Scenario 1: Registration without restorePreservedData parameter
    print_step(1, "Test registration WITHOUT restorePreservedData parameter")
    
    # Clean up any existing user first
    response = register_user(TEST_EMAIL, TEST_PASSWORD, "Test", "User")
    if response and response.status_code in [201, 200]:
        print("Cleaned up existing test user")
        
        # Login and delete to create preserved data
        login_response = login_user(TEST_EMAIL, TEST_PASSWORD)
        if login_response and login_response.status_code == 200:
            token = login_response.json().get('token')
            if token:
                delete_response = delete_account(token, preserve_data=True)
                if delete_response and delete_response.status_code == 200:
                    token_data = delete_response.json()
                    confirmation_token = token_data.get('confirmationToken')
                    if confirmation_token:
                        confirm_response = confirm_deletion(confirmation_token)
                        if confirm_response and confirm_response.status_code == 200:
                            print_success("Created test user with preserved data")
                            time.sleep(1)
    
    # Now test registration without restorePreservedData parameter
    print("\n🧪 Testing registration without restorePreservedData parameter:")
    response = register_user(TEST_EMAIL, "NewPassword456!", "New", "User")
    
    if response and response.status_code == 200:
        data = response.json()
        message = data.get('message', '')
        data_cleaned_up = data.get('dataCleanedUp', False)
        has_preserved_data = data.get('hasPreservedData', False)
        
        print(f"📊 Registration Response:")
        print(f"   • Message: {message}")
        print(f"   • Data Cleaned Up: {data_cleaned_up}")
        print(f"   • Has Preserved Data: {has_preserved_data}")
        
        # Verify preserved data still exists
        print("\n🔍 Checking if preserved data still exists:")
        check_response = check_preserved_data_get(TEST_EMAIL)
        if check_response and check_response.status_code == 200:
            check_data = check_response.json()
            still_has_data = check_data.get('hasPreservedData', False)
            
            if not data_cleaned_up and still_has_data:
                print_success("✅ CORRECT: Data was NOT cleaned up when parameter was not provided")
            else:
                print_error("❌ INCORRECT: Data was cleaned up when it shouldn't have been")
        else:
            print_warning("Could not check preserved data status")
    else:
        print_error("Registration failed")
        return False
    
    # Test Scenario 2: Registration with restorePreservedData=false
    print_step(2, "Test registration WITH restorePreservedData=false")
    
    response = register_user(TEST_EMAIL, "AnotherPassword789!", "Another", "User", restore_preserved_data=False)
    
    if response and response.status_code == 200:
        data = response.json()
        message = data.get('message', '')
        data_cleaned_up = data.get('dataCleanedUp', False)
        has_preserved_data = data.get('hasPreservedData', False)
        
        print(f"📊 Registration Response:")
        print(f"   • Message: {message}")
        print(f"   • Data Cleaned Up: {data_cleaned_up}")
        print(f"   • Has Preserved Data: {has_preserved_data}")
        
        if data_cleaned_up and 'fresh start' in message.lower() and 'cleaned up' in message.lower():
            print_success("✅ CORRECT: Data was cleaned up when explicitly requested")
        else:
            print_error("❌ INCORRECT: Data was not cleaned up when explicitly requested")
            
        # Verify preserved data no longer exists
        print("\n🔍 Checking if preserved data was actually cleaned up:")
        check_response = check_preserved_data_get(TEST_EMAIL)
        if check_response and check_response.status_code == 200:
            check_data = check_response.json()
            still_has_data = check_data.get('hasPreservedData', False)
            
            if not still_has_data:
                print_success("✅ CORRECT: Preserved data was actually cleaned up")
            else:
                print_warning("⚠️  Preserved data still exists after cleanup")
        else:
            print_warning("Could not check preserved data status")
    else:
        print_error("Registration failed")
        return False
    
    print_header("TEST COMPLETED")
    print("🎯 Summary:")
    print("   • Registration without restorePreservedData parameter should NOT clean up data")
    print("   • Registration with restorePreservedData=false should clean up data")
    print("   • Both GET and POST check-preserved-data endpoints should work")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {str(e)}")
        sys.exit(1)
