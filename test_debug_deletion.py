#!/usr/bin/env python3
"""
Debug test to see what's happening during deletion
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3002/api"
TEST_EMAIL = f"test.debug.{int(time.time())}@example.com"
TEST_PASSWORD = "TestPassword123!"

def register_user(email, password):
    """Register a new user"""
    register_data = {
        "email": email,
        "password": password,
        "confirmPassword": password,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+**********"
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=register_data)
    if response.status_code == 201:
        user_data = response.json()
        return {
            'success': True,
            'user_id': user_data.get('user', {}).get('id'),
            'token': user_data.get('token')
        }
    else:
        return {'success': False, 'error': response.text}

def request_deletion(token, preserve_data=False):
    """Request account deletion"""
    deletion_data = {
        "preservePaymentData": preserve_data,
        "preserveTransactionHistory": preserve_data,
        "preserveProfileData": preserve_data,
        "preserveSecurityLogs": preserve_data,
        "customRetentionPeriod": 30,
        "reason": "Testing deletion debug"
    }
    
    response = requests.post(f"{BASE_URL}/account/request-deletion", 
                           json=deletion_data,
                           headers={'Authorization': f'Bearer {token}'})
    
    if response.status_code == 200:
        return {'success': True, 'data': response.json()}
    else:
        return {'success': False, 'error': response.text}

def confirm_deletion(token):
    """Confirm account deletion"""
    response = requests.post(f"{BASE_URL}/account/confirm-deletion", 
                           json={'token': token})
    
    if response.status_code == 200:
        return {'success': True, 'data': response.json()}
    else:
        return {'success': False, 'error': response.text}

def check_preserved_data(email):
    """Check if user has preserved data"""
    response = requests.post(f"{BASE_URL}/account/check-preserved-data", 
                           json={'email': email})
    
    if response.status_code == 200:
        return {'success': True, 'data': response.json()}
    else:
        return {'success': False, 'error': response.text}

def main():
    """Debug test"""
    print("🐛 Debug Deletion Test")
    print("=" * 50)
    
    # Step 1: Register user
    print("📝 Step 1: Registering user...")
    result = register_user(TEST_EMAIL, TEST_PASSWORD)
    if not result['success']:
        print(f"❌ Registration failed: {result['error']}")
        return
    
    user_id = result['user_id']
    token = result['token']
    print(f"✅ User registered with ID: {user_id}")
    
    # Step 2: Request deletion WITHOUT data preservation
    print("\n📝 Step 2: Requesting deletion WITHOUT data preservation...")
    result = request_deletion(token, preserve_data=False)
    if not result['success']:
        print(f"❌ Deletion request failed: {result['error']}")
        return
    
    deletion_id = result['data']['deletionId']
    confirmation_token = result['data']['confirmationToken']
    print(f"✅ Deletion requested with ID: {deletion_id}")
    print(f"🔑 Confirmation token: {confirmation_token}")
    
    # Step 3: Confirm deletion
    print("\n📝 Step 3: Confirming deletion...")
    result = confirm_deletion(confirmation_token)
    if not result['success']:
        print(f"❌ Deletion confirmation failed: {result['error']}")
        return
    
    print("✅ Deletion confirmed")
    print(f"📄 Confirmation response: {json.dumps(result['data'], indent=2)}")
    
    # Step 4: Check preserved data
    print("\n📝 Step 4: Checking preserved data...")
    result = check_preserved_data(TEST_EMAIL)
    if not result['success']:
        print(f"❌ Check preserved data failed: {result['error']}")
        return
    
    print(f"📄 Preserved data response: {json.dumps(result['data'], indent=2)}")
    
    has_preserved = result['data'].get('hasPreservedData', False)
    if has_preserved:
        print("❌ PROBLEM: Found preserved data when none should exist!")
        print("❌ The _clear_existing_preserved_data method is not working correctly")
    else:
        print("✅ CORRECT: No preserved data found")

if __name__ == "__main__":
    main()
