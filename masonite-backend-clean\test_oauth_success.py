#!/usr/bin/env python3
"""Test OAuth Success Redirect"""

import requests
import time

def test_oauth_success():
    """Test OAuth success redirect with valid code and state"""
    base_url = "http://localhost:3002"
    
    print("🔄 Testing OAuth success redirect...")
    
    # Wait for server to be ready
    print("⏳ Waiting for server to be ready...")
    for i in range(10):
        try:
            response = requests.get(f"{base_url}/", timeout=2)
            if response.status_code == 200:
                print("✅ Server is ready!")
                break
        except:
            time.sleep(1)
    else:
        print("❌ Server not responding")
        return
    
    # Test OAuth success redirect
    try:
        response = requests.get(
            f"{base_url}/api/auth/oauth/callback",
            params={
                'code': 'test_auth_code_12345',
                'state': 'google_1234567890_abcdef123456'  # Format: provider_timestamp_random
            },
            allow_redirects=False,
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 302:
            redirect_url = response.headers.get('Location', 'No Location')
            print(f"Redirect URL: {redirect_url}")
            
            expected_pattern = "http://localhost:4200/auth/oauth-success"
            
            if redirect_url.startswith(expected_pattern):
                print("✅ Success redirect URL is correct!")
                
                # Check if it contains the expected parameters
                if "code=" in redirect_url and "provider=" in redirect_url:
                    print("✅ Success redirect contains expected parameters!")
                else:
                    print("❌ Success redirect missing expected parameters")
            else:
                print(f"❌ Wrong success redirect URL")
                print(f"Expected pattern: {expected_pattern}")
                print(f"Actual: {redirect_url}")
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    test_oauth_success()
