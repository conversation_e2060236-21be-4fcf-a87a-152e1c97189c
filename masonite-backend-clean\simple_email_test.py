#!/usr/bin/env python3
"""
Simple Email Test - Send actual email via Brevo SMTP
"""

import os
import sys

# Add the masonite project to path
sys.path.append(os.path.join(os.path.dirname(__file__)))

# Initialize Masonite application
from wsgi import application

from masonite.facades import Mail
from app.mailables.EmailVerification import EmailVerification
from app.models.User import User

def send_test_email():
    """Send a test email to verify SMTP is working"""
    
    print("📧 Sending Test Email via Brevo SMTP...")
    print("=" * 40)
    
    try:
        # Use an existing user or create a simple test user data
        test_user_data = type('User', (), {
            'name': 'Test User',
            'email': '<EMAIL>'
        })()
        
        verification_token = "test_token_123"
        
        print("📤 Sending verification email...")
        print(f"   To: {test_user_data.email}")
        print(f"   Via: Brevo SMTP (smtp-relay.brevo.com:587)")
        
        # Create and send the email
        mailable = EmailVerification(test_user_data, verification_token)
        Mail.mailable(mailable.to(test_user_data.email)).send()
        
        print("✅ SUCCESS: Email sent via Brevo SMTP!")
        print("📬 Check your email inbox (including spam folder)")
        print("🔍 Also check your Brevo dashboard for sending statistics")
        
        return True
        
    except Exception as e:
        print(f"❌ FAILED: Email sending error: {e}")
        
        # Provide specific guidance based on the error
        error_str = str(e)
        print("\n🔧 Error Analysis:")
        
        if "535" in error_str:
            print("   🔑 Authentication failed - check your Brevo credentials")
        elif "550" in error_str:
            print("   📧 Sender not allowed - verify your sender email in Brevo")
        elif "timeout" in error_str.lower():
            print("   🌐 Connection timeout - check internet/firewall")
        else:
            print(f"   📋 Raw error: {error_str}")
        
        return False

if __name__ == "__main__":
    send_test_email()
