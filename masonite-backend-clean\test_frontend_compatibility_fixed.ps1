param(
    [string]$TestType = "all"
)

$BaseUrl = "http://localhost:3002/api"

# Function to test signup with exact frontend data structure
function Test-SignupLikeFrontend {
    Write-Host "🧪 Testing SIGNUP with frontend-like data..."
    
    # Test the actual signup request
    $signupData = @{
        email = "<EMAIL>"
        firstName = "Frontend"
        lastName = "Test"
        password = "FrontendTest123!"
        confirmPassword = "FrontendTest123!"
    } | ConvertTo-Json
    
    $headers = @{
        'Content-Type' = 'application/json'
        'Origin' = 'http://localhost:4200'
        'X-Requested-With' = 'XMLHttpRequest'
    }
    
    try {
        Write-Host "📤 Sending signup request..."
        Write-Host "Data: $signupData"
        
        $response = Invoke-WebRequest -Uri "$BaseUrl/auth/signup" -Method POST -Body $signupData -Headers $headers -UseBasicParsing
        
        Write-Host "✅ Signup Status: $($response.StatusCode)"
        Write-Host "Response: $($response.Content)"
        
    } catch {
        Write-Host "❌ Signup failed:"
        Write-Host "  Status: $($_.Exception.Response.StatusCode.Value__)"
        Write-Host "  Error: $($_.Exception.Message)"
        
        if ($_.Exception.Response) {
            $result = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($result)
            $responseBody = $reader.ReadToEnd()
            Write-Host "  Body: $responseBody"
        }
    }
}

# Function to test change password with exact frontend data structure
function Test-ChangePasswordLikeFrontend {
    Write-Host "🧪 Testing CHANGE PASSWORD with frontend-like data..."
    
    # First login to get a token
    $loginData = @{
        email = "<EMAIL>"
        password = "TestPassword123!"
    } | ConvertTo-Json
    
    $headers = @{
        'Content-Type' = 'application/json'
        'Origin' = 'http://localhost:4200'
        'X-Requested-With' = 'XMLHttpRequest'
    }
    
    try {
        Write-Host "🔐 Logging in first..."
        $loginResponse = Invoke-WebRequest -Uri "$BaseUrl/auth/login" -Method POST -Body $loginData -Headers $headers -UseBasicParsing
        $loginObj = $loginResponse.Content | ConvertFrom-Json
        $token = $loginObj.token
        
        if (-not $token) {
            Write-Host "❌ No token received from login"
            return
        }
        
        Write-Host "✅ Login successful, got token"
        
        # Test change password with frontend structure
        $changePasswordData = @{
            currentPassword = "TestPassword123!"
            newPassword = "NewFrontendTest123!"
        } | ConvertTo-Json
        
        $authHeaders = @{
            'Content-Type' = 'application/json'
            'Authorization' = "Bearer $token"
            'Origin' = 'http://localhost:4200'
            'X-Requested-With' = 'XMLHttpRequest'
        }
        
        Write-Host "📤 Sending change password request..."
        Write-Host "Data: $changePasswordData"
        
        $response = Invoke-WebRequest -Uri "$BaseUrl/auth/change-password" -Method POST -Body $changePasswordData -Headers $authHeaders -UseBasicParsing
        
        Write-Host "✅ Change Password Status: $($response.StatusCode)"
        Write-Host "Response: $($response.Content)"
        
    } catch {
        Write-Host "❌ Change Password failed:"
        Write-Host "  Status: $($_.Exception.Response.StatusCode.Value__)"
        Write-Host "  Error: $($_.Exception.Message)"
        
        if ($_.Exception.Response) {
            $result = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($result)
            $responseBody = $reader.ReadToEnd()
            Write-Host "  Body: $responseBody"
        }
    }
}

# Function to test with malformed data (to trigger 422)
function Test-MalformedRequests {
    Write-Host "🧪 Testing MALFORMED requests (should return 422)..."
    
    # Test signup with missing fields
    $malformedSignup = @{
        email = "<EMAIL>"
        # Missing firstName, lastName, password, confirmPassword
    } | ConvertTo-Json
    
    $headers = @{
        'Content-Type' = 'application/json'
        'Origin' = 'http://localhost:4200'
        'X-Requested-With' = 'XMLHttpRequest'
    }
    
    try {
        Write-Host "📤 Testing malformed signup (missing fields)..."
        $response = Invoke-WebRequest -Uri "$BaseUrl/auth/signup" -Method POST -Body $malformedSignup -Headers $headers -UseBasicParsing
        Write-Host "❌ Should have failed but got: $($response.StatusCode)"
    } catch {
        Write-Host "✅ Correctly rejected malformed signup with status: $($_.Exception.Response.StatusCode.Value__)"
        
        if ($_.Exception.Response.StatusCode.Value__ -eq 422) {
            $result = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($result)
            $responseBody = $reader.ReadToEnd()
            Write-Host "  422 Error Body: $responseBody"
        }
    }
}

# Main execution
Write-Host "🚀 Frontend-Backend API Compatibility Test"
Write-Host ("=" * 60)

switch ($TestType) {
    "signup" { Test-SignupLikeFrontend }
    "change-password" { Test-ChangePasswordLikeFrontend }
    "malformed" { Test-MalformedRequests }
    "all" {
        Test-SignupLikeFrontend
        Test-ChangePasswordLikeFrontend
        Test-MalformedRequests
    }
    default { 
        Write-Host "Usage: .\test_frontend_compatibility.ps1 -TestType [signup|change-password|malformed|all]"
    }
}

Write-Host "`n✅ Test completed!"
