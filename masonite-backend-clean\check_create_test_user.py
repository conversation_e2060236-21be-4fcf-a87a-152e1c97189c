#!/usr/bin/env python3
"""
Check existing users and create a test user with 2FA if needed
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.database import DB
from app.models.User import User
from datetime import datetime

def check_users():
    """Check existing users in the database"""
    print("📊 Checking existing users...")
    
    try:
        users = User.all()
        print(f"📈 Found {len(users)} users in database")
        
        for user in users:
            print(f"👤 User: {user.email} (ID: {user.id})")
            print(f"   - Email verified: {user.is_email_verified()}")
            print(f"   - 2FA enabled: {user.two_factor_enabled or False}")
            print(f"   - Active: {user.is_active if hasattr(user, 'is_active') else 'Unknown'}")
            print()
            
        return users
    except Exception as e:
        print(f"❌ Error checking users: {e}")
        return []

def create_test_user_with_2fa():
    """Create a test user with 2FA enabled"""
    print("🔨 Creating test user with 2FA...")
    
    try:
        # Check if test user already exists
        existing_user = User.where('email', '<EMAIL>').first()
        if existing_user:
            print("⚠️  Test user already exists, updating 2FA settings...")
            user = existing_user
        else:
            # Create new test user
            user = User()
            user.email = '<EMAIL>'
            user.first_name = 'Test'
            user.last_name = 'User2FA'
            user.name = 'Test User2FA'
        
        # Set password
        user.set_password('password123')
        
        # Enable 2FA
        user.two_factor_enabled = True
        user.two_factor_secret = 'JBSWY3DPEHPK3PXP'  # Test secret for TOTP
        
        # Mark email as verified
        user.email_verified_at = datetime.now()
        user.is_active = True
        
        # Save user
        user.save()
        
        print(f"✅ Test user created/updated:")
        print(f"   Email: {user.email}")
        print(f"   Password: password123")
        print(f"   2FA Enabled: {user.two_factor_enabled}")
        print(f"   Email Verified: {user.is_email_verified()}")
        
        return user
        
    except Exception as e:
        print(f"❌ Error creating test user: {e}")
        return None

def main():
    """Main function"""
    print("🚀 User Database Check and Setup")
    print("=" * 40)
    
    # Check existing users
    users = check_users()
    
    # Create test user if needed
    if len(users) == 0:
        print("📝 No users found, creating test user...")
        create_test_user_with_2fa()
    else:
        print("📝 Users exist, checking for 2FA test user...")
        test_user = None
        for user in users:
            if user.two_factor_enabled:
                test_user = user
                break
        
        if not test_user:
            print("📝 No 2FA users found, creating one...")
            create_test_user_with_2fa()
        else:
            print(f"✅ Found 2FA user: {test_user.email}")

if __name__ == "__main__":
    main()
