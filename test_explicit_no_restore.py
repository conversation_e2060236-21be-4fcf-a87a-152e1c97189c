#!/usr/bin/env python3
"""
Test explicit no restore scenario - user explicitly chooses not to restore data
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3002/api"

def test_explicit_no_restore():
    """Test when user explicitly chooses not to restore preserved data"""
    email = f"test.explicit.{int(time.time())}@example.com"
    password = "TestPassword123!"
    
    print("🧪 Testing Explicit No Restore (restorePreservedData: false)")
    print("=" * 70)
    
    # Step 1: Register user
    print("📝 Step 1: Registering user...")
    register_data = {
        "email": email,
        "password": password,
        "confirmPassword": password,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+**********"
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=register_data)
    if response.status_code not in [200, 201]:
        print(f"❌ Registration failed: {response.text}")
        return False
    
    user_data = response.json()
    user_id = user_data.get('userId') or user_data.get('user', {}).get('id')
    token = user_data.get('token')
    print(f"✅ User registered with ID: {user_id}")
    
    # Step 2: Request deletion with profile and security preservation
    print("\n📝 Step 2: Requesting deletion with profile and security preservation...")
    deletion_data = {
        "preservePaymentData": False,
        "preserveTransactionHistory": False,
        "preserveProfileData": True,
        "preserveSecurityLogs": True,
        "customRetentionPeriod": 30,
        "reason": "Testing explicit no restore"
    }
    
    response = requests.post(f"{BASE_URL}/account/request-deletion", 
                           json=deletion_data,
                           headers={'Authorization': f'Bearer {token}'})
    
    if response.status_code != 200:
        print(f"❌ Deletion request failed: {response.text}")
        return False
    
    deletion_response = response.json()
    deletion_id = deletion_response['deletionId']
    confirmation_token = deletion_response['confirmationToken']
    print(f"✅ Deletion requested with ID: {deletion_id}")
    
    # Step 3: Confirm deletion
    print("\n📝 Step 3: Confirming deletion...")
    response = requests.post(f"{BASE_URL}/account/confirm-deletion", 
                           json={'token': confirmation_token})
    
    if response.status_code != 200:
        print(f"❌ Deletion confirmation failed: {response.text}")
        return False
    
    confirm_response = response.json()
    print("✅ Deletion confirmed")
    print(f"📄 Preserved data summary: {confirm_response.get('preservedDataSummary', {})}")
    
    # Step 4: Check preserved data
    print("\n📝 Step 4: Checking preserved data...")
    response = requests.post(f"{BASE_URL}/account/check-preserved-data", 
                           json={'email': email})
    
    if response.status_code != 200:
        print(f"❌ Check preserved data failed: {response.text}")
        return False
    
    check_response = response.json()
    has_preserved = check_response.get('hasPreservedData', False)
    preserved_summary = check_response.get('preservedDataSummary', {})
    
    print(f"📊 Has preserved data: {has_preserved}")
    print(f"📊 Preserved data summary: {preserved_summary}")
    
    if not has_preserved:
        print("❌ FAIL: Expected preserved data but found none")
        return False
    
    # Step 5: Test signup with explicit NO restore
    print("\n📝 Step 5: Testing signup with explicit NO restore...")
    signup_data = {
        "email": email,
        "password": password,
        "confirmPassword": password,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+**********",
        "restorePreservedData": False  # EXPLICITLY choose not to restore
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=signup_data)
    
    if response.status_code not in [200, 201]:
        print(f"❌ Second registration failed: {response.text}")
        return False
    
    signup_response = response.json()
    signup_has_preserved = signup_response.get('hasPreservedData', False)
    signup_preserved_summary = signup_response.get('preservedDataSummary', {})
    data_cleaned_up = signup_response.get('dataCleanedUp', False)
    preserved_data_restored = signup_response.get('preservedDataRestored', False)
    
    print(f"📊 Signup response - Has preserved data: {signup_has_preserved}")
    print(f"📊 Signup response - Preserved summary: {signup_preserved_summary}")
    print(f"📊 Signup response - Data cleaned up: {data_cleaned_up}")
    print(f"📊 Signup response - Data restored: {preserved_data_restored}")
    
    # Step 6: Validate expectations
    print("\n📝 Step 6: Validating expectations...")
    
    # Data should NOT be restored
    if preserved_data_restored:
        print("❌ FAIL: Data was restored when user explicitly chose not to restore")
        return False
    
    # Data should be cleaned up
    if not data_cleaned_up:
        print("❌ FAIL: Data should have been cleaned up when user chose not to restore")
        return False
    
    print("✅ PASS: Data was not restored and was cleaned up as expected")
    
    # Step 7: Check preserved data after cleanup
    print("\n📝 Step 7: Checking preserved data after cleanup...")
    response = requests.post(f"{BASE_URL}/account/check-preserved-data", 
                           json={'email': email})
    
    if response.status_code != 200:
        print(f"❌ Check preserved data failed: {response.text}")
        return False
    
    final_check_response = response.json()
    final_has_preserved = final_check_response.get('hasPreservedData', False)
    final_preserved_summary = final_check_response.get('preservedDataSummary', {})
    
    print(f"📊 Final check - Has preserved data: {final_has_preserved}")
    print(f"📊 Final check - Preserved summary: {final_preserved_summary}")
    
    # After cleanup, there should be no preserved data
    if final_has_preserved:
        print(f"❌ FAIL: Still has preserved data after cleanup: {final_preserved_summary}")
        return False
    
    print("✅ PASS: No preserved data found after cleanup")
    print("✅ PASS: All validations successful!")
    
    return True

def main():
    """Main test function"""
    print("🚀 Starting Explicit No Restore Test")
    print("Testing when user explicitly chooses not to restore preserved data")
    print("=" * 80)
    
    success = test_explicit_no_restore()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 TEST PASSED! Explicit no restore logic is working correctly!")
    else:
        print("❌ TEST FAILED! Explicit no restore logic needs more work!")
    print("🏁 Test completed!")

if __name__ == "__main__":
    main()
