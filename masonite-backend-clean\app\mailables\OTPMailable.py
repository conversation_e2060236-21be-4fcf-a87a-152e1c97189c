from masonite.mail import Mailable
from masonite.environment import env


class OTPMailable(Mailable):
    """
    OTP Email Mailable
    Sends one-time password codes via email for authentication
    """

    def __init__(self, otp_code, otp_type='login'):
        """Initialize with OTP details"""
        super().__init__()
        self.otp_code = otp_code
        self.otp_type = otp_type

    def build(self):
        """Build the OTP email"""
        if self.otp_type == 'login':
            subject = "Your Login Code"
            greeting = "Your secure login code"
        elif self.otp_type == '2fa':
            subject = "Two-Factor Authentication Code"
            greeting = "Your two-factor authentication code"
        elif self.otp_type == 'verification':
            subject = "Account Verification Code"
            greeting = "Your account verification code"
        else:
            subject = "Your Security Code"
            greeting = "Your security code"
        
        # HTML version
        html_content = f"""
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="margin: 0; font-size: 28px;">🔐 Secure Access</h1>
            </div>
            <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
                <h2 style="color: #333; margin-top: 0;">{greeting}</h2>
                <p style="color: #666; font-size: 16px; line-height: 1.6;">
                    Use the following code to complete your authentication:
                </p>
                <div style="background: #fff; border: 2px solid #667eea; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0;">
                    <div style="font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px; font-family: 'Courier New', monospace;">
                        {self.otp_code}
                    </div>
                </div>
                <p style="color: #666; font-size: 14px; margin-bottom: 0;">
                    This code will expire in 10 minutes. If you didn't request this code, please ignore this email.
                </p>
                <hr style="border: none; border-top: 1px solid #e9ecef; margin: 20px 0;">
                <p style="color: #999; font-size: 12px; margin-bottom: 0;">
                    This is an automated message from SecureApp. Please do not reply to this email.
                </p>
            </div>
        </div>
        """        # Plain text version
        text_content = f"""
        {greeting.upper()}

        Use the following code to complete your authentication:

        {self.otp_code}

        This code will expire in 10 minutes.
        
        If you didn't request this code, please ignore this email.

        ---
        This is an automated message from SecureApp.
        """

        return (
            self.subject(subject)
            .html(html_content)
            .text(text_content)
            .from_(env("MAIL_FROM", "<EMAIL>"))
        )
