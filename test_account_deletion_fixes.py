#!/usr/bin/env python3
"""
Test script for account deletion and data preservation fixes
Tests both CORS route fix and data preservation logic
"""

import requests
import json
import time

# Configuration
import time
BASE_URL = "http://localhost:3002/api"
TEST_EMAIL = f"test.deletion.{int(time.time())}@example.com"  # Unique email
TEST_PASSWORD = "TestPassword123!"

def test_cors_route_fix():
    """Test the CORS route fix for check-preserved-data endpoint"""
    print("🧪 Testing CORS Route Fix...")

    # Test OPTIONS request (CORS preflight)
    options_url = f"{BASE_URL}/account/check-preserved-data"
    print(f"📡 Testing OPTIONS request to: {options_url}")

    try:
        response = requests.options(options_url, headers={
            'Origin': 'http://localhost:4200',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
        })

        print(f"✅ OPTIONS Response Status: {response.status_code}")
        print(f"📋 CORS Headers: {dict(response.headers)}")

        if response.status_code == 200:
            print("✅ CORS preflight successful!")
        else:
            print(f"❌ CORS preflight failed with status: {response.status_code}")

    except Exception as e:
        print(f"❌ CORS preflight error: {str(e)}")

    # Test POST request
    post_url = f"{BASE_URL}/account/check-preserved-data"
    print(f"📡 Testing POST request to: {post_url}")

    try:
        response = requests.post(post_url,
                               json={'email': TEST_EMAIL},
                               headers={'Origin': 'http://localhost:4200'})

        print(f"✅ POST Response Status: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        print(f"📄 Response Body: {response.text}")

        if response.status_code == 200:
            print("✅ POST request successful!")
            return True
        else:
            print(f"❌ POST request failed with status: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ POST request error: {str(e)}")
        return False

def test_account_deletion_flow():
    """Test the complete account deletion flow"""
    print("\n🧪 Testing Account Deletion Flow...")
    
    # Step 1: Register a test user
    print("📝 Step 1: Registering test user...")
    register_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD,
        "confirmPassword": TEST_PASSWORD,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+**********"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/signup", json=register_data)
        print(f"✅ Registration Status: {response.status_code}")
        
        if response.status_code == 201:
            user_data = response.json()
            user_id = user_data.get('user', {}).get('id')
            token = user_data.get('token')
            print(f"✅ User registered with ID: {user_id}")
        else:
            print(f"❌ Registration failed: {response.text}")
            return
            
    except Exception as e:
        print(f"❌ Registration error: {str(e)}")
        return
    
    # Step 2: Request account deletion with NO data preservation
    print("\n📝 Step 2: Requesting account deletion with NO data preservation...")
    deletion_data = {
        "preservePaymentData": False,
        "preserveTransactionHistory": False,
        "preserveProfileData": False,
        "preserveSecurityLogs": False,
        "customRetentionPeriod": 30,
        "reason": "Testing deletion flow"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/account/request-deletion", 
                               json=deletion_data,
                               headers={'Authorization': f'Bearer {token}'})
        print(f"✅ Deletion Request Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            deletion_response = response.json()
            deletion_id = deletion_response.get('deletionId')
            print(f"✅ Deletion requested with ID: {deletion_id}")
        else:
            print(f"❌ Deletion request failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Deletion request error: {str(e)}")
    
    # Step 3: Check preserved data (should be none)
    print("\n📝 Step 3: Checking preserved data...")
    try:
        response = requests.post(f"{BASE_URL}/account/check-preserved-data",
                               json={'email': TEST_EMAIL})
        print(f"✅ Check Preserved Data Status: {response.status_code}")
        print(f"📄 Response: {response.text}")

        if response.status_code == 200:
            preserved_data = response.json()
            has_preserved = preserved_data.get('hasPreservedData', False)
            print(f"✅ Has preserved data: {has_preserved}")

            if not has_preserved:
                print("✅ CORRECT: No preserved data found (as expected)")
                return True
            else:
                print("❌ INCORRECT: Found preserved data when none should exist")
                return False
        else:
            print(f"❌ Check preserved data failed: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Check preserved data error: {str(e)}")
        return False

def test_server_health():
    """Test if server is running"""
    print("🏥 Testing Server Health...")

    try:
        response = requests.get(f"{BASE_URL}/auth/profile")
        print(f"✅ Server Health Status: {response.status_code}")
        print(f"📄 Response: {response.text[:200]}...")

        if response.status_code in [200, 401]:  # 401 is expected for unauthenticated request
            print("✅ Server is running!")
            return True
        else:
            print(f"❌ Server health check failed: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Server health check error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Account Deletion and Data Preservation Tests")
    print("=" * 60)

    # Test server health first
    if not test_server_health():
        print("❌ Server is not running. Please start the server first.")
        return

    # Test CORS route fix
    test_cors_route_fix()

    # Test account deletion flow
    test_account_deletion_flow()

    print("\n" + "=" * 60)
    print("🏁 Tests completed!")

if __name__ == "__main__":
    main()
