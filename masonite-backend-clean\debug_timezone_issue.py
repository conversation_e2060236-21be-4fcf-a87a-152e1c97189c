#!/usr/bin/env python
"""Debug timezone issues with OTP verification"""

import os
import sys
from datetime import datetime, timezone, timedelta

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_timezone_issues():
    """Debug timezone handling in OTP verification"""
    try:
        from app.models.OTP import OTP
        
        print("🧪 OTP Timezone Debug Test")
        print("=" * 50)
        
        # Get current time in different formats
        now_utc = datetime.now(timezone.utc)
        now_naive = datetime.now()
        
        print(f"🕒 Current time (UTC aware): {now_utc}")
        print(f"🕒 Current time (naive): {now_naive}")
        print(f"🕒 Current timezone: {now_utc.tzinfo}")
        print()
        
        # Get recent OTPs
        recent_otps = OTP.where('identifier', '<EMAIL>')\
                         .where('otp_type', 'login')\
                         .order_by('created_at', 'desc')\
                         .limit(5)\
                         .get()
        
        print(f"📊 Found {len(recent_otps)} recent <NAME_EMAIL>")
        print()
        
        for i, otp in enumerate(recent_otps, 1):
            print(f"📋 OTP #{i} (ID: {otp.id})")
            print(f"   Created: {otp.created_at}")
            print(f"   Expires: {otp.expires_at}")
            print(f"   Expires Type: {type(otp.expires_at)}")
            
            if hasattr(otp.expires_at, 'tzinfo') and otp.expires_at.tzinfo:
                print(f"   Expires TZ: {otp.expires_at.tzinfo}")
            else:
                print(f"   Expires TZ: None (naive)")
            
            print(f"   Used: {otp.used}")
            print(f"   Attempts: {otp.attempts}/{otp.max_attempts}")
            
            # Manual expiry check
            if hasattr(otp.expires_at, 'tzinfo') and otp.expires_at.tzinfo:
                # It's timezone aware
                time_diff = otp.expires_at - now_utc
                expired = now_utc > otp.expires_at
            else:
                # It's naive, assume UTC
                expires_aware = otp.expires_at.replace(tzinfo=timezone.utc)
                time_diff = expires_aware - now_utc
                expired = now_utc > expires_aware
                
            print(f"   Time until expiry: {time_diff}")
            print(f"   Expired: {expired}")
            print(f"   Valid (manual): {not otp.used and not expired and otp.attempts < otp.max_attempts}")
            print(f"   Valid (model): {otp.is_valid()}")
            print()
        
        # Test find_valid_otp
        print("🔍 Testing find_valid_otp method...")
        valid_otp = OTP.find_valid_otp('<EMAIL>', 'login')
        
        if valid_otp:
            print(f"✅ Found valid OTP: ID {valid_otp.id}")
            print(f"   Created: {valid_otp.created_at}")
            print(f"   Expires: {valid_otp.expires_at}")
        else:
            print("❌ No valid OTP found")
        
        # Test the query manually
        print("\n🔍 Testing query components manually...")
        
        base_query = OTP.where('identifier', '<EMAIL>')\
                        .where('otp_type', 'login')
        print(f"📊 Base query count: {base_query.count()}")
        
        unused_query = base_query.where('used', False)
        print(f"📊 Unused OTPs: {unused_query.count()}")
        
        # Test expiry comparison
        unexpired_query = unused_query.where('expires_at', '>', now_utc)
        print(f"📊 Unexpired OTPs (UTC): {unexpired_query.count()}")
        
        # Test with naive datetime
        unexpired_naive = unused_query.where('expires_at', '>', now_naive)
        print(f"📊 Unexpired OTPs (naive): {unexpired_naive.count()}")
        
        # Test attempts
        valid_attempts = unexpired_query.where('attempts', '<', 3)
        print(f"📊 Valid attempts: {valid_attempts.count()}")
        
        # Get all unexpired OTPs to debug
        unexpired_otps = unused_query.where('expires_at', '>', now_utc).get()
        print(f"\n📋 Unexpired OTPs details:")
        for otp in unexpired_otps:
            print(f"   ID {otp.id}: expires={otp.expires_at}, attempts={otp.attempts}")
            
    except Exception as e:
        print(f"❌ Error during debug: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_timezone_issues()
