#!/usr/bin/env python3
"""
Register the account owner email and test password reset
"""
import requests
import json
from datetime import datetime
import time

def register_account_owner():
    """Register the account owner email"""
    endpoint_url = "http://localhost:3002/api/auth/signup"
    
    user_data = {
        "firstName": "Ji<PERSON><PERSON>",
        "lastName": "Ahuja", 
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "confirmPassword": "TestPassword123!"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }
    
    print(f"📧 Registering account owner: {user_data['email']}")
    
    try:
        response = requests.post(endpoint_url, json=user_data, headers=headers, timeout=15)
        print(f"📊 Registration Status: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            print(f"✅ User registered successfully!")
            print(f"   User ID: {data.get('user', {}).get('id', 'Unknown')}")
            return True
        else:
            try:
                error_data = response.json()
                if "already exists" in str(error_data).lower():
                    print(f"ℹ️  User already exists, proceeding with password reset")
                    return True
                else:
                    print(f"❌ Registration failed: {json.dumps(error_data, indent=2)}")
                    return False
            except:
                print(f"❌ Registration failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return False

def test_password_reset():
    """Test password reset for account owner"""
    endpoint_url = 'http://localhost:3002/api/auth/forgot-password'
    test_email = '<EMAIL>'
    
    payload = {'email': test_email}
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }
    
    print(f'\n🎯 Testing password reset for: {test_email}')
    print(f'⏰ Time: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    
    try:
        response = requests.post(endpoint_url, json=payload, headers=headers, timeout=15)
        print(f'📊 Status: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'✅ SUCCESS: {data.get("message", "No message")}')
            print(f'📧 Check inbox: {test_email}')
            print(f'📁 Also check spam/junk folder')
            print(f'⏰ Email should arrive within 1-5 minutes')
            return True
        else:
            print(f'❌ FAILED: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ ERROR: {e}')
        return False

def main():
    print("=" * 60)
    print(" TESTING PASSWORD RESET WITH ACCOUNT OWNER EMAIL")
    print("=" * 60)
    
    # Step 1: Register account owner
    if register_account_owner():
        print("\n⏳ Waiting 2 seconds for user registration to complete...")
        time.sleep(2)
        
        # Step 2: Test password reset
        if test_password_reset():
            print(f"\n✅ COMPLETE SUCCESS!")
            print(f"📋 SUMMARY:")
            print(f"   1. ✅ User <EMAIL> is registered")
            print(f"   2. ✅ Password reset request sent successfully")
            print(f"   3. ✅ Email should be sent via Brevo API")
            print(f"   4. 📧 Check the email inbox for the reset link")
            print(f"\n🔍 WHAT TO CHECK:")
            print(f"   - Gmail <NAME_EMAIL>")
            print(f"   - Spam/Junk folder")
            print(f"   - Server logs should show 'Password reset email sent via Brevo API'")
            print(f"   - Brevo dashboard may show activity with delay")
        else:
            print(f"\n❌ Password reset failed")
    else:
        print(f"\n❌ Cannot proceed without user registration")

if __name__ == "__main__":
    main()
