#!/usr/bin/env python3
"""
Test script to verify OAuth redirects work correctly after the response.redirect() fix
"""

import requests
import sys

def test_oauth_redirects():
    """Test OAuth redirects to ensure they preserve port numbers correctly"""
    print("🧪 OAuth Redirect Port Preservation Test")
    print("=" * 60)
    
    backend_url = "http://localhost:3002"
    
    # Test cases for OAuth redirects
    test_cases = [
        {
            "name": "OAuth Error Redirect",
            "url": f"{backend_url}/api/auth/oauth/callback",
            "params": {"error": "access_denied", "state": "test_state"},
            "expected_redirect_contains": "localhost:4200/auth/oauth-error"
        },
        {
            "name": "OAuth Invalid State Redirect", 
            "url": f"{backend_url}/api/auth/oauth/callback",
            "params": {"code": "test_code", "state": "invalid_state"},
            "expected_redirect_contains": "localhost:4200/auth/oauth-error"
        },
        {
            "name": "OAuth Missing Code Redirect",
            "url": f"{backend_url}/api/auth/oauth/callback", 
            "params": {"state": "google_test_state"},
            "expected_redirect_contains": "localhost:4200/auth/oauth-error"
        }
    ]
    
    all_passed = True
    
    for test_case in test_cases:
        print(f"\n🔬 Testing: {test_case['name']}")
        print(f"   URL: {test_case['url']}")
        print(f"   Params: {test_case['params']}")
        
        try:
            response = requests.get(
                test_case['url'], 
                params=test_case['params'], 
                allow_redirects=False, 
                timeout=10
            )
            
            if response.status_code == 302:
                redirect_url = response.headers.get('Location', '')
                print(f"   ✅ Got 302 redirect")
                print(f"   🔗 Redirect URL: {redirect_url}")
                
                if test_case['expected_redirect_contains'] in redirect_url:
                    print(f"   ✅ Port preservation: PASSED")
                else:
                    print(f"   ❌ Port preservation: FAILED")
                    print(f"   Expected to contain: {test_case['expected_redirect_contains']}")
                    all_passed = False
            else:
                print(f"   ❌ Expected 302 redirect, got {response.status_code}")
                all_passed = False
                
        except Exception as e:
            print(f"   ❌ Test failed with error: {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✅ ALL OAUTH REDIRECT TESTS PASSED!")
        print("🎉 Port numbers are correctly preserved in all redirect URLs.")
    else:
        print("❌ Some OAuth redirect tests failed.")
        print("💡 Check the server logs and redirect implementation.")
    
    return all_passed

if __name__ == "__main__":
    success = test_oauth_redirects()
    sys.exit(0 if success else 1)
