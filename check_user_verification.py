#!/usr/bin/env python3
"""
User Verification Status Checker and Fixer
"""

import sys
import os

# Add the Masonite project root to Python path
project_root = os.path.join(os.path.dirname(__file__), 'masonite-backend-clean')
sys.path.insert(0, project_root)

# Change to project directory for proper Masonite initialization
os.chdir(project_root)

# Setup environment for Masonite
os.environ.setdefault('APP_ENV', 'local')

try:
    # Initialize Masonite application
    from bootstrap.start import app
    from app.models.User import User
    from datetime import datetime
    
    print("✅ Masonite initialized successfully")
except Exception as e:
    print(f"❌ Error initializing Masonite: {e}")
    print("Trying alternative initialization...")
    
    try:
        # Alternative initialization
        import sys
        sys.path.append('.')
        from app.models.User import User
        from datetime import datetime
        print("✅ Alternative initialization successful")
    except Exception as e2:
        print(f"❌ Alternative initialization failed: {e2}")
        exit(1)

def check_user_status():
    """Check and display user verification status"""
    print("🔍 Checking User Verification Status")
    print("=" * 50)
    
    # Check <EMAIL>
    yahoo_user = User.where('email', '<EMAIL>').first()
    if yahoo_user:
        print(f"📧 Yahoo User (ID: {yahoo_user.id})")
        print(f"   Email: {yahoo_user.email}")
        print(f"   Email Verified: {yahoo_user.email_verified_at is not None}")
        print(f"   Email Verified At: {yahoo_user.email_verified_at}")
        print(f"   Verification Token: {yahoo_user.email_verification_token}")
        print(f"   Verification Expires: {yahoo_user.email_verification_expires}")
        print(f"   Is Active: {getattr(yahoo_user, 'is_active', 'N/A')}")
    else:
        print(f"❌ Yahoo user not found")
    
    print()
    
    # Check <EMAIL>
    gmail_user = User.where('email', '<EMAIL>').first()
    if gmail_user:
        print(f"📧 Gmail User (ID: {gmail_user.id})")
        print(f"   Email: {gmail_user.email}")
        print(f"   Email Verified: {gmail_user.email_verified_at is not None}")
        print(f"   Email Verified At: {gmail_user.email_verified_at}")
        print(f"   Verification Token: {gmail_user.email_verification_token}")
        print(f"   Verification Expires: {gmail_user.email_verification_expires}")
        print(f"   Is Active: {getattr(gmail_user, 'is_active', 'N/A')}")
    else:
        print(f"❌ Gmail user not found")

def verify_yahoo_user():
    """Manually verify the yahoo user"""
    print("\n🔧 Manually Verifying Yahoo User")
    print("=" * 50)
    
    yahoo_user = User.where('email', '<EMAIL>').first()
    if yahoo_user:
        # Mark as verified
        yahoo_user.email_verified_at = datetime.now()
        yahoo_user.email_verification_token = None
        yahoo_user.email_verification_expires = None
        yahoo_user.save()
        
        print(f"✅ Yahoo user verified successfully!")
        print(f"   Email: {yahoo_user.email}")
        print(f"   Verified At: {yahoo_user.email_verified_at}")
    else:
        print(f"❌ Yahoo user not found")

def reset_gmail_password():
    """Reset Gmail user password"""
    print("\n🔧 Resetting Gmail User Password")
    print("=" * 50)
    
    gmail_user = User.where('email', '<EMAIL>').first()
    if gmail_user:
        # Reset password to known value
        gmail_user.set_password('Aaa12345!')
        gmail_user.save()
        
        print(f"✅ Gmail user password reset successfully!")
        print(f"   Email: {gmail_user.email}")
        print(f"   New Password: Aaa12345!")
    else:
        print(f"❌ Gmail user not found")

if __name__ == "__main__":
    print("🔍 USER VERIFICATION STATUS INVESTIGATION")
    print("=" * 60)
    
    # Check current status
    check_user_status()
    
    # Fix issues
    verify_yahoo_user()
    reset_gmail_password()
    
    print("\n🔍 Updated Status Check")
    print("=" * 50)
    check_user_status()
    
    print("\n✅ User setup completed!")
    print("📝 Credentials:")
    print("   <EMAIL> - Password: Aaa12345! (Verified)")
    print("   <EMAIL> - Password: Aaa12345! (Status checked)")
