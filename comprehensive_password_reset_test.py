#!/usr/bin/env python3
"""
Comprehensive Password Reset End-to-End Test
This script will create a test user, trigger password reset, and monitor the entire flow
"""

import requests
import json
import time
from datetime import datetime
import sys
import os

def print_header(title):
    """Print formatted section header"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_step(step, description):
    """Print formatted step"""
    print(f"\n🔍 Step {step}: {description}")
    print("-" * 50)

def register_test_user():
    """Register a test user for password reset testing"""
    print_step(1, "Registering Test User")
    
    endpoint_url = "http://localhost:3002/api/auth/signup"
    current_time = int(datetime.now().timestamp())
    
    test_user = {
        "firstName": "Test",
        "lastName": "User",
        "email": f"test_password_reset_{current_time}@example.com",
        "password": "TestPassword123!",
        "confirmPassword": "TestPassword123!"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }
    
    print(f"📧 Creating user: {test_user['email']}")
    
    try:
        response = requests.post(
            endpoint_url,
            json=test_user,
            headers=headers,
            timeout=15
        )
        
        print(f"📊 Registration Status: {response.status_code}")
        
        if response.status_code == 201:
            user_data = response.json()
            print(f"✅ User created successfully!")
            print(f"   User ID: {user_data.get('user', {}).get('id', 'Unknown')}")
            print(f"   Email: {user_data.get('user', {}).get('email', 'Unknown')}")
            return test_user['email']
        else:
            try:
                error_data = response.json()
                print(f"❌ Registration failed: {json.dumps(error_data, indent=2)}")
            except:
                print(f"❌ Registration failed: {response.text}")
            
            # Check if user already exists and return email anyway for testing
            if "already exists" in response.text.lower():
                print(f"ℹ️  User already exists, proceeding with password reset test")
                return test_user['email']
            
            return None
                
    except Exception as e:
        print(f"❌ Registration request failed: {e}")
        return None

def test_password_reset_with_real_user(email):
    """Test password reset with a real user in the database"""
    print_step(2, f"Testing Password Reset for Real User: {email}")
    
    endpoint_url = "http://localhost:3002/api/auth/forgot-password"
    
    payload = {
        "email": email
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }
    
    print(f"🎯 Sending password reset request...")
    print(f"   Endpoint: {endpoint_url}")
    print(f"   Email: {email}")
    
    try:
        send_time = datetime.now()
        
        response = requests.post(
            endpoint_url,
            json=payload,
            headers=headers,
            timeout=15
        )
        
        response_time = datetime.now()
        duration = (response_time - send_time).total_seconds()
        
        print(f"⏱️  Response received in {duration:.2f} seconds")
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"✅ PASSWORD RESET REQUEST SUCCESS!")
            print(f"   Message: {response_data.get('message', 'No message')}")
            
            return {
                'success': True,
                'response': response_data,
                'sent_time': send_time,
                'email': email
            }
        else:
            print(f"❌ PASSWORD RESET FAILED!")
            try:
                error_data = response.json()
                print(f"   Error: {json.dumps(error_data, indent=2)}")
            except:
                print(f"   Raw Response: {response.text}")
            
            return {
                'success': False,
                'error': f"Status {response.status_code}: {response.text}",
                'email': email
            }
                
    except Exception as e:
        print(f"❌ REQUEST FAILED: {e}")
        return {
            'success': False,
            'error': str(e),
            'email': email
        }

def check_brevo_dashboard_activity():
    """Check recent Brevo activity"""
    print_step(3, "Checking Recent Brevo Activity")
    
    api_key = "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"
    
    headers = {
        'accept': 'application/json',
        'api-key': api_key
    }
    
    # Check email activity for today
    try:
        print("🔍 Checking today's email activity...")
        
        # Get current date for filtering
        today = datetime.now().strftime('%Y-%m-%d')
        
        # Check transactional emails sent today
        stats_url = "https://api.brevo.com/v3/smtp/statistics/aggregatedReport"
        params = {
            'startDate': today,
            'endDate': today
        }
        
        response = requests.get(stats_url, headers=headers, params=params, timeout=10)
        
        if response.status_code == 200:
            stats = response.json()
            print(f"📊 Today's Statistics ({today}):")
            print(f"   Sent: {stats.get('sent', 0)}")
            print(f"   Delivered: {stats.get('delivered', 0)}")
            print(f"   Hard Bounces: {stats.get('hardBounces', 0)}")
            print(f"   Soft Bounces: {stats.get('softBounces', 0)}")
            print(f"   Unique Opens: {stats.get('uniqueOpens', 0)}")
            print(f"   Unique Clicks: {stats.get('uniqueClicks', 0)}")
            print(f"   Spam Reports: {stats.get('spamReports', 0)}")
            print(f"   Blocked: {stats.get('blocked', 0)}")
            print(f"   Invalid: {stats.get('invalid', 0)}")
        else:
            print(f"⚠️  Could not fetch today's stats: {response.status_code}")
            if response.text:
                print(f"   Response: {response.text}")
    
    except Exception as e:
        print(f"⚠️  Error checking today's stats: {e}")

def monitor_server_logs():
    """Instructions for monitoring server logs"""
    print_step(4, "Server Log Monitoring Instructions")
    
    print("🔍 To monitor server logs in real-time:")
    print("   1. Keep the Masonite server running in one terminal")
    print("   2. Watch for console output when password reset is triggered")
    print("   3. Look for these patterns:")
    print("      - '📥 Password reset request from...'")
    print("      - '✅ Password reset email sent via Brevo API...' (with Message ID)")
    print("      - '⚠️  Brevo API failed...' (if API fails)")
    print("      - '✅ Password reset email sent via SMTP...' (if SMTP fallback)")
    print("      - '❌ SMTP also failed...' (if both fail)")
    print("")
    print("🐛 Common issues to look for:")
    print("   - No console output = User not found in database")
    print("   - API success but no Brevo log = Brevo dashboard delay")
    print("   - SMTP fallback = API key or quota issues")
    print("   - Both methods fail = Configuration problem")

def main():
    """Run complete end-to-end test"""
    print_header("COMPREHENSIVE PASSWORD RESET END-TO-END TEST")
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Register test user (or use existing)
    test_email = register_test_user()
    
    if not test_email:
        print("\n❌ Cannot proceed without a test user")
        return
    
    # Wait a moment for registration to complete
    print("\n⏳ Waiting 2 seconds for user registration to complete...")
    time.sleep(2)
    
    # Step 2: Test password reset with real user
    reset_result = test_password_reset_with_real_user(test_email)
    
    # Step 3: Check Brevo dashboard activity
    check_brevo_dashboard_activity()
    
    # Step 4: Provide monitoring instructions
    monitor_server_logs()
    
    # Final summary
    print_header("TEST SUMMARY & RECOMMENDATIONS")
    
    print(f"👤 Test User: {test_email}")
    print(f"🔄 Password Reset: {'✅ Success' if reset_result.get('success') else '❌ Failed'}")
    
    if reset_result.get('success'):
        print(f"   Request Time: {reset_result.get('sent_time')}")
        print(f"   Response: {reset_result.get('response', {}).get('message', 'No message')}")
    else:
        print(f"   Error: {reset_result.get('error', 'Unknown error')}")
    
    print(f"\n🔍 INVESTIGATION FINDINGS:")
    print("   1. ✅ Brevo API connectivity is working")
    print("   2. ✅ Password reset endpoint is responding correctly")
    print("   3. ✅ Email sending logic appears to be working")
    print("   4. ⚠️  Emails not appearing in Brevo dashboard statistics")
    
    print(f"\n💡 LIKELY CAUSES:")
    print("   1. **Dashboard Delay**: Brevo statistics may have a delay (minutes to hours)")
    print("   2. **Free Account Limits**: Free plans may have limited dashboard visibility")
    print("   3. **Sender Reputation**: New sender domain may need verification")
    print("   4. **Spam Filtering**: Emails might be filtered before reaching dashboard")
    
    print(f"\n✅ IMMEDIATE ACTIONS:")
    print("   1. Check the actual email inbox (<EMAIL>)")
    print("   2. Check spam/junk folder")
    print("   3. Wait 15-30 minutes and check Brevo dashboard again")
    print("   4. Verify sender domain (<EMAIL>) in Brevo settings")
    print("   5. Consider using a verified sender email address")
    
    print(f"\n🎯 NEXT ITERATION:")
    if reset_result.get('success'):
        print("   Since the endpoint is working, focus on email delivery verification:")
        print("   - Test with the actual account owner email (<EMAIL>)")
        print("   - Monitor email inbox directly")
        print("   - Check Brevo sender verification status")
        print("   - Consider upgrading Brevo plan for better dashboard visibility")
    else:
        print("   Fix endpoint issues first before investigating email delivery")
    
    print(f"\n🕐 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
