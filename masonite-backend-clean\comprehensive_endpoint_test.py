#!/usr/bin/env python3
"""
🧪 Comprehensive Masonite Backend API Endpoint Test
====================================================
This script tests all major API endpoints to ensure they are working correctly.
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:3002/api"
TEST_EMAIL_VERIFIED = "<EMAIL>"
TEST_EMAIL_UNVERIFIED = "<EMAIL>"
TEST_PASSWORD = "test123"

class APITester:
    def __init__(self):
        self.auth_token = None
        self.results = {
            "passed": 0,
            "failed": 0,
            "skipped": 0
        }
    
    def print_header(self):
        print("🧪 Comprehensive Masonite Backend API Test")
        print("=" * 55)
        print(f"🔗 Base URL: {BASE_URL}")
        print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
    
    def test_endpoint(self, method, endpoint, data=None, auth=False, expected_status=None, description=""):
        """Generic method to test any endpoint"""
        url = f"{BASE_URL}{endpoint}"
        headers = {"Content-Type": "application/json"}
        
        if auth and self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers)
            elif method.upper() == "POST":
                response = requests.post(url, json=data, headers=headers)
            elif method.upper() == "PATCH":
                response = requests.patch(url, json=data, headers=headers)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=headers)
            else:
                print(f"❌ Unsupported method: {method}")
                self.results["failed"] += 1
                return None
            
            # Determine if test passed
            if expected_status:
                passed = response.status_code == expected_status
            else:
                passed = 200 <= response.status_code < 500  # Any valid response
            
            status_icon = "✅" if passed else "❌"
            
            print(f"{status_icon} {method.upper()} {endpoint}")
            if description:
                print(f"   📝 {description}")
            print(f"   📊 Status: {response.status_code}")
            
            # Show response preview (truncated)
            response_text = response.text[:200] + "..." if len(response.text) > 200 else response.text
            print(f"   📄 Response: {response_text}")
            print()
            
            if passed:
                self.results["passed"] += 1
            else:
                self.results["failed"] += 1
            
            return response
            
        except Exception as e:
            print(f"❌ {method.upper()} {endpoint}")
            if description:
                print(f"   📝 {description}")
            print(f"   💥 Error: {str(e)}")
            print()
            self.results["failed"] += 1
            return None
    
    def test_authentication_endpoints(self):
        print("🔐 Testing Authentication Endpoints")
        print("-" * 40)
        
        # Test login with verified user
        login_data = {
            "email": TEST_EMAIL_VERIFIED,
            "password": TEST_PASSWORD
        }
        response = self.test_endpoint("POST", "/auth/login", login_data, 
                                    description="Login with verified user")
        
        # Extract auth token if login successful
        if response and response.status_code == 200:
            try:
                token_data = response.json()
                if "access_token" in token_data:
                    self.auth_token = token_data["access_token"]
                elif "token" in token_data:
                    self.auth_token = token_data["token"]
                print(f"   🔑 Auth token acquired")
            except:
                pass
        
        # Test login with unverified user
        unverified_login_data = {
            "email": TEST_EMAIL_UNVERIFIED,
            "password": TEST_PASSWORD
        }
        self.test_endpoint("POST", "/auth/login", unverified_login_data,
                          description="Login with unverified user (should fail)")
        
        # Test signup endpoint
        signup_data = {
            "email": f"test_{int(time.time())}@example.com",
            "password": "password123",
            "name": "Test User"
        }
        self.test_endpoint("POST", "/auth/signup", signup_data,
                          description="User registration")
        
        # Test resend verification
        resend_data = {"email": TEST_EMAIL_UNVERIFIED}
        self.test_endpoint("POST", "/auth/resend-verification", resend_data,
                          description="Resend email verification")
        
        # Test profile (requires auth)
        self.test_endpoint("GET", "/auth/profile", auth=True,
                          description="Get user profile")
        
        # Test logout (requires auth)
        self.test_endpoint("POST", "/auth/logout", auth=True,
                          description="User logout")
    
    def test_otp_endpoints(self):
        print("📱 Testing OTP Endpoints")
        print("-" * 25)
        
        # Test OTP send
        otp_data = {
            "identifier": "<EMAIL>",
            "type": "login"
        }
        self.test_endpoint("POST", "/otp/send", otp_data,
                          description="Send OTP")
        
        # Test OTP verification
        verify_data = {
            "identifier": "<EMAIL>",
            "otp": "123456",
            "type": "login"
        }
        self.test_endpoint("POST", "/otp/verify", verify_data,
                          description="Verify OTP (expected to fail with wrong code)")
        
        # Test OTP status
        self.test_endpoint("GET", "/otp/status?identifier=<EMAIL>",
                          description="Get OTP status")
    
    def test_2fa_endpoints(self):
        print("🔒 Testing 2FA Endpoints")
        print("-" * 25)
        
        # These require authentication
        self.test_endpoint("GET", "/2fa/status", auth=True,
                          description="Get 2FA status")
        
        self.test_endpoint("POST", "/2fa/setup", auth=True,
                          description="Setup 2FA")
        
        self.test_endpoint("GET", "/2fa/recovery-codes", auth=True,
                          description="Get recovery codes")
    
    def test_oauth_endpoints(self):
        print("🌐 Testing OAuth Endpoints")
        print("-" * 28)
        
        # Test get providers
        self.test_endpoint("GET", "/auth/oauth/providers",
                          description="Get OAuth providers")
        
        # Test get OAuth URL for Google
        self.test_endpoint("GET", "/auth/oauth/google/url",
                          description="Get Google OAuth URL")
    
    def test_payment_endpoints(self):
        print("💳 Testing Payment Endpoints")
        print("-" * 30)
        
        # Test payment test endpoint
        self.test_endpoint("GET", "/payments/test",
                          description="Payment system test")
        
        # Test user payments (requires auth)
        self.test_endpoint("GET", "/payments/my-payments", auth=True,
                          description="Get user payments")
        
        # Test payment analytics (requires auth)
        self.test_endpoint("GET", "/payments/analytics", auth=True,
                          description="Payment analytics")
    
    def test_security_endpoints(self):
        print("🛡️ Testing Security Endpoints")
        print("-" * 31)
        
        # Test security dashboard (requires auth)
        self.test_endpoint("GET", "/security/dashboard", auth=True,
                          description="Security dashboard")
        
        # Test security events (requires auth)
        self.test_endpoint("GET", "/security/events", auth=True,
                          description="Security events")
        
        # Test account status (requires auth)
        self.test_endpoint("GET", "/security/account-status", auth=True,
                          description="Account status check")
    
    def test_notification_endpoints(self):
        print("🔔 Testing Notification Endpoints")
        print("-" * 35)
        
        # Test get notifications (requires auth)
        self.test_endpoint("GET", "/notifications", auth=True,
                          description="Get user notifications")
    
    def test_account_management_endpoints(self):
        print("👤 Testing Account Management Endpoints")
        print("-" * 42)
        
        # Test deletion status (requires auth)
        self.test_endpoint("GET", "/account/deletion-status", auth=True,
                          description="Get account deletion status")
        
        # Test export data (requires auth)
        self.test_endpoint("GET", "/account/export-data", auth=True,
                          description="Export user data")
    
    def test_queue_endpoints(self):
        print("⚡ Testing Queue Endpoints")
        print("-" * 26)
        
        # Test queue status (requires auth)
        self.test_endpoint("GET", "/queue/status", auth=True,
                          description="Queue status")
          # Test queue stats (requires auth)
        self.test_endpoint("GET", "/queue/stats", auth=True,
                          description="Queue statistics")
    
    def test_connectivity(self):
        """Test basic server connectivity"""
        print("🌐 Testing Server Connectivity")
        print("-" * 30)
        
        try:
            # Test with a basic API endpoint that should respond
            response = requests.get(f"{BASE_URL}/auth/oauth/providers", timeout=5)
            if response.status_code < 500:  # Any response under 500 means server is working
                print("✅ Server is accessible")
                return True
            else:
                print(f"⚠️ Server responded with status: {response.status_code}")
                return False
        except Exception as e:
            print("❌ Server is not accessible")
            print(f"💡 Please ensure the server is running on localhost:3002")
            print(f"Error: {str(e)}")
            return False
    
    def print_summary(self):
        print("📊 Test Summary")
        print("=" * 20)
        print(f"✅ Passed: {self.results['passed']}")
        print(f"❌ Failed: {self.results['failed']}")
        print(f"⏭️ Skipped: {self.results['skipped']}")
        print(f"📈 Total: {sum(self.results.values())}")
        
        if self.results['failed'] == 0:
            print("\n🎉 All tests passed! Your API is working correctly.")
        else:
            print(f"\n⚠️ {self.results['failed']} tests failed. Check the output above for details.")
    
    def run_all_tests(self):
        self.print_header()
        
        # Check connectivity first
        if not self.test_connectivity():
            print("\n❌ Cannot proceed - server not accessible")
            return
        
        print()
        
        # Run all test suites
        self.test_authentication_endpoints()
        self.test_otp_endpoints()
        self.test_2fa_endpoints()
        self.test_oauth_endpoints()
        self.test_payment_endpoints()
        self.test_security_endpoints()
        self.test_notification_endpoints()
        self.test_account_management_endpoints()
        self.test_queue_endpoints()
        
        self.print_summary()

def main():
    tester = APITester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
