"""
Test script to verify <PERSON><PERSON><PERSON>, Sign<PERSON>, and OTP fixes for soft-deleted users
"""
import sys
import os
import uuid

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models.User import User
from app.models.AccountDeletionRecord import AccountDeletionRecord
from app.services.OAuthService import OAuthService
from app.services.OTPService import OTPService
from app.services.AccountDeletionService import AccountDeletionService

def test_soft_deleted_user_scenarios():
    """Test different scenarios with soft-deleted users"""
    
    test_email = "<EMAIL>"
    print(f"🧪 Testing soft-deleted user scenarios for: {test_email}")
    
    try:
        # 1. Create a test user
        print("\n1. Creating test user...")
        test_user = User.where('email', test_email).first()
        if test_user:
            # Force delete existing user for clean test
            test_user.force_delete()
        
        user = User.create({
            'name': 'Test User',
            'email': test_email,
            'password': 'password123',
            'first_name': 'Test',
            'last_name': 'User',
            'is_active': True,
            'roles': 'user'
        })
        print(f"✅ User created: {user.id}")
          # 2. Create deletion record with preserved data
        print("\n2. Creating deletion record with preserved data...")
        deletion_service = AccountDeletionService()
        
        # Simulate account deletion with preserved data
        deletion_id = str(uuid.uuid4())  # Generate deletion_id
        deletion_record = AccountDeletionRecord.create({
            'user_id': str(user.id),
            'email': test_email,
            'deletion_id': deletion_id,  # Add the required deletion_id
            'deletion_status': 'completed',
            'preserve_payment_data': False,
            'preserve_transaction_history': False,
            'preserve_profile_data': True,
            'preserve_security_logs': False,
            'custom_retention_period': 30,
            'preserved_user_data': '{"first_name": "Test", "last_name": "User", "phone": "+**********"}',
            'requested_at': '2025-06-19 12:00:00',
            'completed_at': '2025-06-19 12:01:00',
            'expires_at': '2025-12-31 23:59:59'
        })
        print(f"✅ Deletion record created: {deletion_record.id}")
        
        # 3. Soft delete the user
        print("\n3. Soft deleting user...")
        user.delete()  # This should soft delete
        print("✅ User soft deleted")
        
        # 4. Test preserved data check
        print("\n4. Testing preserved data check...")
        preserved_data = deletion_service.check_preserved_data(test_email)
        print(f"✅ Preserved data check result: {preserved_data}")
        
        # 5. Test OAuth service with soft-deleted user
        print("\n5. Testing OAuth service with soft-deleted user...")
        oauth_service = OAuthService()
        try:
            oauth_user_data = {
                'email': test_email,
                'firstName': 'Test',
                'lastName': 'User',
                'avatarUrl': 'https://example.com/avatar.jpg',
                'emailVerified': True
            }
            restored_user = oauth_service.find_or_create_oauth_user('google', oauth_user_data)
            print(f"✅ OAuth service restored user: {restored_user.id}")
            print(f"✅ User is active: {restored_user.is_active}")
            print(f"✅ User deleted_at: {restored_user.deleted_at}")
            
            # Clean up for next test
            restored_user.delete()
        except Exception as e:
            print(f"❌ OAuth service error: {e}")
        
        # 6. Test OTP service with soft-deleted user
        print("\n6. Testing OTP service with soft-deleted user...")
        otp_service = OTPService()
        try:
            found_user = otp_service._find_user_by_identifier(test_email)
            if found_user:
                print(f"✅ OTP service found and restored user: {found_user.id}")
                print(f"✅ User is active: {found_user.is_active}")
                print(f"✅ User deleted_at: {found_user.deleted_at}")
            else:
                print("❌ OTP service did not find user")
        except Exception as e:
            print(f"❌ OTP service error: {e}")
        
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        try:
            # Clean up deletion records first
            deletion_records = AccountDeletionRecord.where('email', test_email).get()
            for record in deletion_records:
                record.delete()
            
            # Then clean up users
            test_users = User.where('email', test_email).with_trashed().get()
            for user in test_users:
                user.force_delete()
                
            print("🧹 Test cleanup completed successfully")
        except Exception as e:
            print(f"⚠️ Cleanup error: {e}")

if __name__ == "__main__":
    test_soft_deleted_user_scenarios()
