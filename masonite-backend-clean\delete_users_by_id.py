#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to delete specific users by ID since email search isn't working
"""

import os
import sys

# Add the masonite project to path
sys.path.append(os.path.join(os.path.dirname(__file__)))

# Initialize Masonite application
from wsgi import application

# Now we can import models after app initialization
from app.models.User import User

def delete_users_by_id():
    """Delete specific users by ID"""
    user_ids_to_delete = [255, 265]  # IDs from the search results
    
    print("🧹 Starting cleanup of test users by ID...")
    
    for user_id in user_ids_to_delete:
        try:
            # Find user by ID
            user = User.with_trashed().find(user_id)
            
            if user:
                email = user.email
                username = user.name or 'Unknown'
                
                # Delete the user
                user.delete()
                
                print(f"✅ Deleted user: {email} (ID: {user_id}, Name: {username})")
            else:
                print(f"ℹ️  User not found with ID: {user_id}")
                
        except Exception as e:
            print(f"❌ Error deleting user ID {user_id}: {str(e)}")
    
    print("🎉 Cleanup completed!")

if __name__ == "__main__":
    delete_users_by_id()
