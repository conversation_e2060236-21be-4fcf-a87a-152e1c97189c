#!/usr/bin/env python3
"""
Debug Recovery Code Issue - Check Database State
"""

import requests
import json
import time

# Configuration
BASE_URL = 'http://localhost:3002/api'
EMAIL = '<EMAIL>'
PASSWORD = 'SecurePass123!'

def make_request(method, endpoint, data=None, headers=None):
    """Make HTTP request with error handling"""
    url = f"{BASE_URL}{endpoint}"
    try:
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers, timeout=10)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, headers=headers, timeout=10)
        elif method.upper() == 'PUT':
            response = requests.put(url, json=data, headers=headers, timeout=10)
        else:
            raise ValueError(f"Unsupported method: {method}")
        
        print(f"📊 {method.upper()} {endpoint}")
        print(f"   Status: {response.status_code}")
        try:
            response_data = response.json()
            print(f"   Response: {json.dumps(response_data, indent=2)}")
        except:
            print(f"   Response: {response.text}")
        
        if response.status_code < 300:
            return True, response.json() if response.text else {}
        else:
            return False, response.json() if response.text else response.text
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False, str(e)

def debug_recovery_codes():
    """Debug recovery code functionality step by step"""
    print("🧪 Debugging Recovery Code Functionality")
    print("=" * 60)
    
    # Step 1: Register a new user
    print("\n🔑 Step 1: Register new test user")
    register_data = {
        'name': 'Test User',
        'email': EMAIL,
        'password': PASSWORD,
        'confirmPassword': PASSWORD
    }
    
    status, response = make_request('POST', '/auth/register', register_data)
    if not status:
        print(f"❌ Registration failed: {response}")
        # Try to login with existing user
        print("\n🔑 Trying to login with existing user...")
    
    # Step 2: Login
    print("\n🔑 Step 2: Login to get access token")
    login_data = {
        'email': EMAIL,
        'password': PASSWORD
    }
    
    status, response = make_request('POST', '/auth/login', login_data)
    if not status:
        print(f"❌ Login failed: {response}")
        return
    
    access_token = response.get('access_token') or response.get('token')
    if not access_token:
        print(f"❌ No access token in response: {response}")
        return
    
    print(f"✅ Login successful, got token: {access_token[:20]}...")
    
    # Headers for authenticated requests
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    # Step 3: Setup 2FA to generate recovery codes
    print("\n🔐 Step 3: Setup 2FA (should generate 3 recovery codes)")
    status, response = make_request('POST', '/two-factor/setup', {}, headers)
    if not status:
        print(f"❌ 2FA setup failed: {response}")
        return
    
    backup_codes = response.get('backupCodes', [])
    secret = response.get('secret')
    print(f"📊 2FA Setup Response:")
    print(f"   Secret: {secret[:10] if secret else 'None'}...")
    print(f"   Backup codes count: {len(backup_codes)}")
    for i, code in enumerate(backup_codes[:3]):  # Show first 3
        print(f"   Code {i+1}: {code}")
    
    if not backup_codes:
        print("❌ No backup codes generated!")
        return
    
    # Step 4: Verify 2FA to enable it
    print("\n🔐 Step 4: Enable 2FA (verify with dummy token for now)")
    # For testing, we'll skip this as it requires TOTP app
    print("   Skipping verification for now - testing with codes directly...")
    
    # Step 5: Test login with recovery code
    print("\n🔄 Step 5: Test login with recovery code")
    
    # Logout first
    print("   Logging out...")
    make_request('POST', '/auth/logout', {}, headers)
    
    if backup_codes:
        recovery_code = backup_codes[0]
        print(f"\n🧪 Testing recovery code: {recovery_code}")
        
        # Attempt login with recovery code
        login_with_recovery_data = {
            'email': EMAIL,
            'password': PASSWORD,
            'recoveryCode': recovery_code
        }
        
        status, response = make_request('POST', '/auth/login', login_with_recovery_data)
        if status:
            print(f"✅ Recovery code login successful!")
        else:
            print(f"❌ Recovery code login failed: {response}")
            
            # Check if it's the "No recovery codes remaining" error
            if 'error' in response:
                error_message = response['error'].get('message', '')
                if 'No recovery codes remaining' in error_message:
                    print(f"🎯 FOUND THE ISSUE: {error_message}")
                    print("   This suggests the recovery codes weren't properly stored in the database")
                    
                    # Let's check recovery codes status
                    print("\n📊 Step 6: Check recovery codes status")
                    # We need to login first to check status
                    login_status, login_resp = make_request('POST', '/auth/login', {'email': EMAIL, 'password': PASSWORD})
                    if login_status:
                        new_token = login_resp.get('access_token') or login_resp.get('token')
                        if new_token:
                            new_headers = {'Authorization': f'Bearer {new_token}', 'Content-Type': 'application/json'}
                            status, response = make_request('GET', '/2fa/recovery-codes', headers=new_headers)
                            if status:
                                print("✅ Recovery codes status retrieved:")
                                print(f"   Response: {json.dumps(response, indent=2)}")
                            else:
                                print(f"❌ Failed to get recovery codes status: {response}")
    
    print("\n" + "=" * 60)
    print("🔍 Debug session complete")

if __name__ == '__main__':
    debug_recovery_codes()
