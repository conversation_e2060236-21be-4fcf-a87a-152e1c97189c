#!/usr/bin/env python3
"""
Test script to verify OTP email functionality
Tests the actual email sending with Brevo/SMTP configuration
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django-style settings for Masonite
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

def test_otp_email():
    """Test OTP email sending functionality"""
    try:
        print("🔍 Testing OTP Email Functionality...")
        
        # Test basic email configuration
        from masonite.environment import env
        print(f"📧 MAIL_DRIVER: {env('MAIL_DRIVER')}")
        print(f"📧 MAIL_HOST: {env('MAIL_HOST')}")
        print(f"📧 MAIL_PORT: {env('MAIL_PORT')}")
        print(f"📧 MAIL_FROM: {env('MAIL_FROM')}")
        print(f"📧 BREVO_API_KEY: {'***' + env('BREVO_API_KEY', '')[-4:] if env('BREVO_API_KEY') else 'Not Set'}")
        
        # Test OTP Mailable
        from app.mailables.OTPMailable import OTPMailable
        print("✅ OTPMailable imported successfully")
        
        # Create test OTP
        test_code = "123456"
        test_email = "<EMAIL>"  # Your email from .env
        
        # Test mailable creation
        mailable = OTPMailable(test_code, 'login', test_email)
        print(f"✅ OTPMailable created: {test_code} for {test_email}")
        
        # Test mail sending
        from masonite.facades import Mail
        print("🚀 Attempting to send test OTP email...")
        
        result = Mail.mailable(mailable).send()
        print(f"✅ Email sent successfully! Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Email test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_otp_service():
    """Test OTPService functionality with direct email send"""
    try:
        print("\n🔍 Testing OTP Service Direct Email...")
        
        from app.services.OTPService import OTPService
        otp_service = OTPService()
        
        test_email = "<EMAIL>"
        test_code = "987654"
        
        # Test direct email sending (bypass user lookup)
        print(f"🚀 Sending OTP directly via email to {test_email}...")
        otp_service._send_email_otp(test_email, test_code, "test")
        
        print(f"✅ Direct email send completed!")
        return True
        
    except Exception as e:
        print(f"❌ OTP Service test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Email Functionality Test Suite")
    print("=" * 50)
    
    # Test 1: Email configuration and mailable
    email_test = test_otp_email()
    
    # Test 2: OTP Service
    service_test = test_otp_service()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"  Email Mailable: {'✅ PASS' if email_test else '❌ FAIL'}")
    print(f"  OTP Service: {'✅ PASS' if service_test else '❌ FAIL'}")
    
    if email_test and service_test:
        print("\n🎉 All tests passed! Email functionality is working.")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
