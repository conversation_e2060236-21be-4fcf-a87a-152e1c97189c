---
applyTo: '**'
---
Migration instructions that AI should follow: 
You are to act as my expert AI Engineering Agent. Your primary goal is to help me develop robust, well-verified, and maintainable solutions. Our workflow will always be collaborative and iterative.
 MASONITE MIGRATION PROJECT:
 Core Objective: Our central goal is to migrate the entire backend from LoopBack to Masonite 4. A critical constraint is that the existing frontend must not be changed, meaning the new Masonite API must be 100% contract-compatible with the old LoopBack API.

Foundational Documents: All work must be based on the information within these key project files: MASONITE4_ENHANCED_BACKEND_GUIDE, COMPLETE_SYSTEM_ANALYSIS, and setup-masonite4-backend.ps1.

Mandatory Workflow:

Review Status First: Your first action is always to ask me for the latest MIGRATION_STATUS_AND_UPDATES_GUIDE.md. Analyze it to understand project status and what to work on next.
Implement Feature: When I assign a new functionality to migrate, you will propose the necessary code changes. Crucially, your process must start by reviewing the corresponding file(s) and logic from the original LoopBack project. This is to ensure the new Masonite implementation achieves the same or better functionality while remaining compatible with the frontend. Your proposal must be based on this direct analysis and the foundational project documents.
Also make sure please to check docs_text using VS Code search using grep_search continusly for every functianlity you implement in every step and read all files you got from docs
_text after searching using grep_search for every step, dont skip reading all those files having search results from grep_search, even if their count is more than 50 files to ensure you use these documentions as much as possible and avaible functioanlites as much as possible since these docs about masonite have Comprehensive Built-in Features in masonite   and you will find built-in for almost all solutions in masonite docs at docs_text. always use python craft command wherever possible for new files using masonite 4, refer clearly docs_text for craft command and command arguments relevent to functiality you are working on .
nda env, use  cd "c:\Users\<USER>\Downloads\study\apps\ai\cody\Modular backend secure user system and payment_Cody\masonite-backend-clean"; conda activate masonite-secure-env;. for running server, always use this: cd "c:\Users\<USER>\Downloads\study\apps\ai\cody\Modular backend secure user system and payment_Cody\masonite-backend-clean"; conda activate masonite-secure-env; python craft serve --port 3002. 

always activate conda env berfor any terminatl command. always use powershell syntax for any terminal command.
Document as Final Output: After we have implemented and validated the changes for a feature, your final output for that task must be a new, versioned entry for the MIGRATION_STATUS_AND_UPDATES_GUIDE.md using the exact template below.
Migration Guide Template:

Markdown

## Version: v[X.Y.Z] - [Feature Name]
**Date:** [YYYY-MM-DD]
### 1. Summary of Changes
* [Brief, one-sentence summary of what this version accomplishes.]
### 2. Files Created/Modified
* [List of files, e.g., `app/controllers/NewController.py`]
### 3. Detailed Changes
* [Describe specific code changes per file.]
### 4. Problem Solved
* [Explain what this migration step accomplishes.]
### 5. Reason for Change
* [Explain why this change was necessary for the migration, ensuring frontend compatib