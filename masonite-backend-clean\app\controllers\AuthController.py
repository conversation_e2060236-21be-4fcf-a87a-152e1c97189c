"""
Masonite 4 API Authentication Controller
Maintains exact LoopBack API compatibility for existing frontend
"""

from masonite.controllers import Controller
from masonite.request import Request
from masonite.response import Response
from masonite.authentication import Auth
from masonite.validation import Validator
from masonite.facades import Mail
from masonite.environment import env
from app.models.User import User
from app.mailables.EmailVerification import EmailVerification
from app.mailables.PasswordReset import PasswordReset
from app.mailables.Disable2FARequest import Disable2<PERSON>Request
from app.services.AccountDeletionService import AccountDeletionService
from app.controllers.CorsController import CorsController
from datetime import datetime, timedelta
import jwt


class AuthController(Controller):
    """
    API Authentication Controller 
    Maintains exact compatibility with LoopBack endpoints
    """
    
    def __init__(self):
        """Initialize services"""
        self.account_deletion_service = AccountDeletionService()
    
    def login(self, request: Request, response: Response, auth: Auth, validate: Validator):
        """
        POST /api/auth/login
        User login with email/username and password
        Handles 2FA verification in the same endpoint (LoopBack compatible)
        """
        # Validate request using built-in Masonite validator
        errors = request.validate(
            validate.required(['email', 'password']),
            validate.email('email'),
            validate.string('password')
        )
        
        if errors:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)
        
        email = request.input('email')
        password = request.input('password')
        two_factor_token = request.input('twoFactorToken')  # LoopBack compatible field name
        recovery_code = request.input('recoveryCode')       # LoopBack compatible field name
        
        print(f"🔍 Login attempt for: {email}")
        print(f"🔍 Credentials received: email={email}, hasPassword={bool(password)}, hasTwoFactorToken={bool(two_factor_token)}, hasRecoveryCode={bool(recovery_code)}")
        
        # Use Masonite 4 authentication - attempt returns user model or None
        user = auth.attempt(email, password)
        
        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Invalid email or password'
                }
            }, 401)
        
        # Check if email is verified
        if not user.is_email_verified():
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 403,
                    'name': 'EmailNotVerifiedError',
                    'message': 'Please verify your email address before logging in',
                    'details': {
                        'emailVerified': False,
                        'userId': user.id
                    }
                }
            }, 403)
          # Handle 2FA verification (LoopBack compatible flow)
        if user.two_factor_enabled:
            # If no 2FA token or recovery code provided, request 2FA
            if not two_factor_token and not recovery_code:
                print(f"🔐 2FA required for user: {user.email}")
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'token': '',  # Empty token as per LoopBack
                    'user': {
                        'id': user.id,
                        'firstName': user.first_name or user.name.split(' ')[0] if user.name else '',
                        'lastName': user.last_name or (user.name.split(' ')[1] if user.name and len(user.name.split(' ')) > 1 else ''),
                        'email': user.email,
                        'emailVerified': user.is_email_verified(),
                        'twoFactorEnabled': user.two_factor_enabled or False
                    },
                    'requiresTwoFactor': True
                }, 200)
            
            # Validate 2FA token or recovery code
            is_valid_2fa = False
            
            # Check recovery code first (if provided) - same priority as LoopBack
            if recovery_code:
                print(f"🔄 Attempting recovery code verification for user: {user.email}")
                try:
                    from app.services.RecoveryCodeService import RecoveryCodeService
                    recovery_service = RecoveryCodeService()
                    is_valid_2fa = recovery_service.validate_and_consume_recovery_code(user.id, recovery_code)
                    
                    if is_valid_2fa:
                        print(f"✅ Recovery code validation successful for user: {user.email}")
                        
                        # Check if all recovery codes are now used after this validation
                        try:
                            all_codes_used = recovery_service.are_all_codes_used(user.id)
                            if all_codes_used:
                                print(f"⚠️ All recovery codes have been used for user: {user.email}")
                                # This information could be used by the frontend to show disable 2FA option
                        except Exception as error:
                            print(f"⚠️ Could not check recovery codes status: {error}")
                    else:
                        print(f"❌ Recovery code validation failed for user: {user.email}")
                        
                        # Check if all codes are already used to provide helpful error message
                        try:
                            all_codes_used = recovery_service.are_all_codes_used(user.id)
                            if all_codes_used:
                                print(f"⚠️ All recovery codes are exhausted for user: {user.email}")
                                CorsController.add_cors_headers(response, request.header('Origin'))
                                return response.json({
                                    'error': {
                                        'statusCode': 401,
                                        'name': 'UnauthorizedError',
                                        'message': 'All recovery codes have been used. Please use your authenticator app or request to disable 2FA via email.'
                                    }
                                }, 401)
                        except Exception as check_error:
                            print(f"⚠️ Could not check recovery codes status: {check_error}")
                            
                except Exception as error:
                    print(f"❌ Recovery code validation error: {error}")
                    if 'No recovery codes remaining' in str(error):
                        CorsController.add_cors_headers(response, request.header('Origin'))
                        return response.json({
                            'error': {
                                'statusCode': 401,
                                'name': 'UnauthorizedError',
                                'message': str(error)
                            }
                        }, 401)
                    CorsController.add_cors_headers(response, request.header('Origin'))
                    return response.json({
                        'error': {
                            'statusCode': 401,
                            'name': 'UnauthorizedError',
                            'message': 'Invalid recovery code'
                        }
                    }, 401)
                    
            # Check TOTP token (if provided and recovery code wasn't valid)
            elif two_factor_token:
                print(f"🔄 Attempting TOTP verification for user: {user.email}")
                try:
                    from app.services.SecurityService import SecurityService
                    security_service = SecurityService()
                    is_valid_2fa = security_service.verify_two_factor_token(user.id, two_factor_token)
                    
                    if is_valid_2fa:
                        print(f"✅ TOTP verification successful for user: {user.email}")
                    else:
                        print(f"❌ TOTP verification failed for user: {user.email}")
                        
                except Exception as error:
                    print(f"❌ TOTP verification error: {error}")
                    is_valid_2fa = False
            
            if not is_valid_2fa:
                print(f"❌ 2FA verification failed for user: {user.email}")
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Invalid two-factor authentication token or recovery code'
                    }
                }, 401)
        
        # Generate full JWT token
        token = user.generate_api_token()
        
        print(f"✅ Login successful for user: {user.email}")
        
        # Add CORS headers and return LoopBack-compatible response
        CorsController.add_cors_headers(response, request.header('Origin'))
        return response.json({
            'token': token,
            'user': {
                'id': user.id,
                'firstName': user.first_name or user.name.split(' ')[0] if user.name else '',
                'lastName': user.last_name or (user.name.split(' ')[1] if user.name and len(user.name.split(' ')) > 1 else ''),
                'email': user.email,
                'emailVerified': user.is_email_verified(),
                'twoFactorEnabled': user.two_factor_enabled or False
            }
        })
    
    def register(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/auth/register (mapped to /signup in routes)
        User registration compatible with LoopBack format
        Enhanced with better error handling and frontend compatibility
        """
        try:
            # Log the incoming request for debugging
            print(f"📥 Registration request from {request.header('Origin')}")
            print(f"📝 Request data: {request.all()}")
            
            # Validate registration data with strong password requirements - LoopBack compatible fields
            errors = request.validate(
                validate.required(['firstName', 'lastName', 'email', 'password']),
                validate.email('email'),
                validate.string('firstName'),
                validate.string('lastName'),
                validate.strong('password', length=8, special=1, uppercase=1),  # Built-in strong password validation
            )

            # Manual password confirmation validation to handle frontend field name
            password = request.input('password')
            confirm_password = request.input('confirmPassword') or request.input('password_confirmation')

            # Convert MessageBag to dict if needed
            if errors and hasattr(errors, 'all'):
                error_dict = errors.all()
            else:
                error_dict = errors or {}

            # Enhanced password confirmation validation
            if not confirm_password:
                error_dict['confirmPassword'] = ['Password confirmation is required']
            elif password != confirm_password:
                error_dict['confirmPassword'] = ['Password confirmation does not match']

            # Additional frontend-compatible validations
            if request.input('firstName') and len(request.input('firstName').strip()) < 1:
                error_dict['firstName'] = ['First name is required']
            
            if request.input('lastName') and len(request.input('lastName').strip()) < 1:
                error_dict['lastName'] = ['Last name is required']

            if error_dict:
                print(f"❌ Validation errors: {error_dict}")
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 422,
                        'name': 'ValidationError',
                        'message': 'The request body is invalid',
                        'details': error_dict
                    }
                }, 422)            # Check for preserved data from previous account deletion
            preserved_data_info = None
            try:
                preserved_data_info = self.account_deletion_service.check_preserved_data(request.input('email'))
                if preserved_data_info.get('hasPreservedData'):
                    print(f"🔍 Found preserved data for email: {request.input('email')}")
                    print(f"📊 Preserved data summary: {preserved_data_info.get('preservedDataSummary')}")
            except Exception as error:
                print(f"⚠️ Error checking preserved data: {error}")            # Check if user already exists (including soft-deleted)
            existing_user = User.where('email', request.input('email')).first()
            
            # If no active user found, check for soft-deleted user
            soft_deleted_user = None
            if not existing_user:
                soft_deleted_user = User.with_trashed().where('email', request.input('email')).first()
                if soft_deleted_user and soft_deleted_user.deleted_at is not None:
                    print(f"🔄 Found soft-deleted user for signup: {request.input('email')}")
                    
                    # Check if this user has preserved data
                    if preserved_data_info and preserved_data_info.get('hasPreservedData'):
                        print(f"✅ Restoring user {request.input('email')} via signup with preserved data")
                        
                        # Restore the user by removing soft delete timestamp
                        soft_deleted_user.deleted_at = None
                        soft_deleted_user.is_active = True
                        
                        # Update user information with new signup data
                        soft_deleted_user.first_name = request.input('firstName').strip()
                        soft_deleted_user.last_name = request.input('lastName').strip()
                        soft_deleted_user.name = f"{request.input('firstName').strip()} {request.input('lastName').strip()}"
                        soft_deleted_user.set_password(request.input('password'))
                        soft_deleted_user.email_verified_at = None  # Reset email verification
                        soft_deleted_user.save()
                        
                        # Trigger data restoration
                        try:
                            self.account_deletion_service.restore_user_data(str(soft_deleted_user.id))
                            print(f"✅ User {request.input('email')} data restored successfully")
                        except Exception as restore_error:
                            print(f"⚠️ Error restoring user data: {restore_error}")
                        
                        # Generate email verification token and send verification email
                        verification_token = soft_deleted_user.generate_email_verification_token()
                        
                        email_sent = False
                        email_method = "none"
                        
                        # Try Brevo API first (more reliable)
                        try:
                            from app.services.BrevoEmailService import BrevoEmailService
                            brevo_service = BrevoEmailService()
                            api_result = brevo_service.send_verification_email(soft_deleted_user, verification_token)
                            
                            if api_result['success']:
                                email_sent = True
                                email_method = "brevo_api"
                                print(f"✅ Email sent via Brevo API to {soft_deleted_user.email} (Message ID: {api_result.get('message_id')})")
                            else:
                                print(f"⚠️  Brevo API failed: {api_result['error']}")
                                
                        except Exception as api_error:
                            print(f"⚠️  Brevo API service error: {api_error}")
                        
                        # Fallback to SMTP if API failed
                        if not email_sent:
                            try:
                                # Send email verification email using SMTP
                                Mail.mailable(EmailVerification(soft_deleted_user, verification_token).to(soft_deleted_user.email)).send()
                                email_sent = True
                                email_method = "smtp"
                                print(f"✅ Email sent via SMTP to {soft_deleted_user.email}")
                            except Exception as smtp_error:
                                print(f"❌ SMTP also failed: {smtp_error}")
                                email_method = "failed"
                        
                        # Generate JWT token
                        token = soft_deleted_user.generate_api_token()
                        
                        # Add CORS headers and return LoopBack-compatible response
                        CorsController.add_cors_headers(response, request.header('Origin'))
                        
                        response_data = {
                            'message': 'Account successfully restored with preserved data. Please verify your email.',
                            'userId': str(soft_deleted_user.id),
                            'token': token,
                            'user': {
                                'id': str(soft_deleted_user.id),
                                'email': soft_deleted_user.email,
                                'firstName': soft_deleted_user.first_name,
                                'lastName': soft_deleted_user.last_name,
                                'emailVerified': soft_deleted_user.is_email_verified(),
                                'roles': soft_deleted_user.roles or ['user'],
                                'twoFactorEnabled': soft_deleted_user.two_factor_enabled or False
                            },
                            'hasPreservedData': True,
                            'preservedDataSummary': preserved_data_info.get('preservedDataSummary'),
                            'emailVerificationRequired': True,
                            'emailSent': email_sent,
                            'emailMethod': email_method
                        }
                        
                        return response.json(response_data, 200)
                    else:
                        print(f"⚠️ Soft-deleted user {request.input('email')} found but no preserved data available")
            
            if existing_user:
                print(f"❌ User already exists: {request.input('email')}")
                
                # Special case: Check if this is an inconsistent state where user exists but has preserved data
                if preserved_data_info and preserved_data_info.get('hasPreservedData'):
                    print("🔍 Detected inconsistent state: User exists but has preserved data")
                    print(f"🔍 Found user ID: {existing_user.id}")
                    
                    deletion_record = preserved_data_info.get('deletionRecord')
                    if deletion_record and str(existing_user.id) == str(deletion_record.get('originalUserId')):
                        CorsController.add_cors_headers(response, request.header('Origin'))
                        return response.json({
                            'error': {
                                'statusCode': 422,
                                'name': 'ValidationError',
                                'message': 'Account deletion was incomplete. Your account was not properly deleted during the deletion process. Please contact support to resolve this issue.'
                            }
                        }, 422)
                    else:
                        CorsController.add_cors_headers(response, request.header('Origin'))
                        return response.json({
                            'error': {
                                'statusCode': 422,
                                'name': 'ValidationError',
                                'message': 'A user account exists with this email, but it\'s different from your previously deleted account. This suggests an incomplete account deletion process. Please contact support.'
                            }
                        }, 422)
                
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 422,
                        'name': 'ValidationError',
                        'message': 'User with this email already exists',
                        'details': {'email': ['The email has already been taken.']}
                    }
                }, 422)

            # Create full name from firstName and lastName
            full_name = f"{request.input('firstName').strip()} {request.input('lastName').strip()}"

            # Create user manually for better control
            try:                # Create user instance and set password
                user = User()
                user.name = full_name
                user.first_name = request.input('firstName').strip()
                user.last_name = request.input('lastName').strip()
                user.email = request.input('email').strip().lower()
                user.is_active = True
                user.roles = ['user']  # Fixed: JSON field requires list, not string
                user.set_password(request.input('password'))
                user.save()
                
                print(f"✅ User created successfully: {user.email} (ID: {user.id})")
                
            except Exception as e:
                print(f"❌ Database error creating user: {str(e)}")
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': f'User registration failed: {str(e)}'
                }
            }, 422)
            
            # Generate email verification token and send verification email
            verification_token = user.generate_email_verification_token()
            
            email_sent = False
            email_method = "none"
            
            # Try Brevo API first (more reliable)
            try:
                from app.services.BrevoEmailService import BrevoEmailService
                brevo_service = BrevoEmailService()
                api_result = brevo_service.send_verification_email(user, verification_token)
                
                if api_result['success']:
                    email_sent = True
                    email_method = "brevo_api"
                    print(f"✅ Email sent via Brevo API to {user.email} (Message ID: {api_result.get('message_id')})")
                else:
                    print(f"⚠️  Brevo API failed: {api_result['error']}")
                    
            except Exception as api_error:
                print(f"⚠️  Brevo API service error: {api_error}")
            
            # Fallback to SMTP if API failed
            if not email_sent:
                try:
                    # Send email verification email using SMTP
                    Mail.mailable(EmailVerification(user, verification_token).to(user.email)).send()
                    email_sent = True
                    email_method = "smtp"
                    print(f"✅ Email sent via SMTP to {user.email}")
                except Exception as smtp_error:
                    print(f"❌ SMTP also failed: {smtp_error}")
                    email_method = "failed"
            
            # Log email sending result
            if not email_sent:
                print(f"⚠️  Email verification not sent to {user.email}. User can still login but should verify later.")
            
            # Generate JWT token
            token = user.generate_api_token()            # Add CORS headers and return LoopBack-compatible response
            CorsController.add_cors_headers(response, request.header('Origin'))
            
            response_data = {
                'message': (
                    'User registered successfully. You have preserved data available for restoration.'
                    if preserved_data_info and preserved_data_info.get('hasPreservedData')
                    else 'User registered successfully. You can now log in.'
                ),
                'userId': str(user.id),
                'token': token,
                'user': {
                    'id': user.id,
                    'firstName': request.input('firstName').strip(),
                    'lastName': request.input('lastName').strip(),
                    'email': user.email,
                    'emailVerified': False,
                    'twoFactorEnabled': False
                }
            }
            
            # Add preserved data info if available
            if preserved_data_info and preserved_data_info.get('hasPreservedData'):
                response_data['hasPreservedData'] = True
                response_data['preservedDataSummary'] = preserved_data_info.get('preservedDataSummary')
            else:
                response_data['hasPreservedData'] = False
            
            return response.json(response_data, 201)
            
        except Exception as e:
            print(f"❌ Unexpected error during registration: {str(e)}")
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Registration failed due to server error'
                }
            }, 500)
    
    def logout(self, request: Request, response: Response):
        """
        POST /api/auth/logout
        User logout - invalidate token
        """
        # Get the current user from request (set by middleware)
        user = request.user()
        if user:
            # Invalidate the JWT token by clearing api_token
            user.api_token = None
            user.save()

        CorsController.add_cors_headers(response, request.header('Origin'))
        return response.json({
            'message': 'Logged out successfully'
        })
    
    def refresh(self, request: Request, response: Response):
        """
        POST /api/auth/refresh
        Refresh JWT token
        """
        user = request.user()
        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'No valid token provided'
                }
            }, 401)

        # Generate new JWT token
        token = user.generate_api_token()

        CorsController.add_cors_headers(response, request.header('Origin'))
        return response.json({
            'token': token,
            'user': {
                'id': user.id,
                'firstName': user.first_name or user.name.split(' ')[0] if user.name else '',
                'lastName': user.last_name or (user.name.split(' ')[1] if user.name and len(user.name.split(' ')) > 1 else ''),
                'email': user.email,
                'emailVerified': user.is_email_verified(),
                'twoFactorEnabled': user.two_factor_enabled or False
            }
        })
    
    def verify_email(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/auth/verify-email
        Email verification endpoint - compatible with LoopBack
        """
        # Validate request using built-in Masonite validator
        errors = request.validate(
            validate.required(['token']),
            validate.string('token')
        )
        
        if errors:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)

        token = request.input('token')

        # Find user with valid verification token
        user = User.where('email_verification_token', token)\
                   .where('email_verification_expires', '>', datetime.now())\
                   .first()

        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': 'Invalid or expired verification token'
                }
            }, 400)

        # Mark email as verified
        user.mark_email_as_verified()

        # Return LoopBack-compatible response
        CorsController.add_cors_headers(response, request.header('Origin'))
        return response.json({
            'message': 'Email verified successfully',
            'user': {
                'id': user.id,
                'email': user.email,
                'firstName': user.first_name or user.name.split(' ')[0] if user.name else '',
                'lastName': user.last_name or (user.name.split(' ')[1] if user.name and len(user.name.split(' ')) > 1 else ''),
                'emailVerified': True
            }
        })
    
    def forgot_password(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/auth/forgot-password
        Password reset request - compatible with LoopBack
        """
        # Validate request using built-in Masonite validator
        errors = request.validate(
            validate.required(['email']),
            validate.email('email')
        )
        
        if errors:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)
        email = request.input('email')        # Find user by email
        user = User.where('email', email).first()

        if not user:
            # Don't reveal if email exists or not (security best practice)
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'message': 'If the email exists, a password reset link has been sent'
            })

        # Generate password reset token
        reset_token = user.generate_password_reset_token()
        
        email_sent = False
        email_method = "none"
        
        # Try Brevo API first (more reliable)
        try:
            from app.services.BrevoEmailService import BrevoEmailService
            brevo_service = BrevoEmailService()
            api_result = brevo_service.send_password_reset_email(user, reset_token)
            
            if api_result['success']:
                email_sent = True
                email_method = "brevo_api"
                print(f"✅ Password reset email sent via Brevo API to {user.email} (Message ID: {api_result.get('message_id')})")
            else:
                print(f"⚠️  Brevo API failed for password reset: {api_result['error']}")
                
        except Exception as api_error:
            print(f"⚠️  Brevo API service error for password reset: {api_error}")
        
        # Fallback to SMTP if API failed
        if not email_sent:
            try:
                # Send password reset email using SMTP
                Mail.mailable(PasswordReset(user, reset_token).to(user.email)).send()
                email_sent = True
                email_method = "smtp"
                print(f"✅ Password reset email sent via SMTP to {user.email}")
            except Exception as smtp_error:
                print(f"❌ SMTP also failed for password reset: {smtp_error}")
                email_method = "failed"
        
        # Log email sending result
        if not email_sent:
            print(f"⚠️  Password reset email not sent to {user.email}. User will need to try again.")

        CorsController.add_cors_headers(response, request.header('Origin'))
        return response.json({
            'message': 'If the email exists, a password reset link has been sent'
        })
    
    def reset_password(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/auth/reset-password
        Password reset execution - compatible with LoopBack
        """        # Validate request with strong password requirements
        errors = request.validate(
            validate.required(['token', 'password']),
            validate.string('token'),
            validate.strong('password', length=8, special=1, uppercase=1)  # Built-in strong password validation
        )
        
        if errors:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)

        token = request.input('token')
        new_password = request.input('password')

        # Find user with valid reset token
        user = User.where('password_reset_token', token)\
                   .where('password_reset_expires', '>', datetime.now())\
                   .first()

        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': 'Invalid or expired reset token'
                }
            }, 400)

        # Update password and clear reset token
        user.set_password(new_password)
        user.clear_password_reset_token()

        CorsController.add_cors_headers(response, request.header('Origin'))
        return response.json({
            'message': 'Password reset successfully'
        })
    
    def profile(self, request: Request, response: Response):
        """
        GET /api/auth/profile
        Get current authenticated user profile
        Protected endpoint that requires valid JWT token
        """
        # The user should be set by the JWT middleware
        user = request.user()

        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)

        # Return user profile in LoopBack-compatible format
        CorsController.add_cors_headers(response, request.header('Origin'))
        return response.json({
            'user': {
                'id': user.id,
                'firstName': user.first_name or user.name.split(' ')[0] if user.name else '',
                'lastName': user.last_name or (user.name.split(' ')[1] if user.name and len(user.name.split(' ')) > 1 else ''),
                'email': user.email,
                'emailVerified': user.is_email_verified(),
                'twoFactorEnabled': user.two_factor_enabled or False,
                'createdAt': user.created_at.isoformat() if user.created_at else None,
                'updatedAt': user.updated_at.isoformat() if user.updated_at else None
            }
        })

    def update_profile(self, request: Request, response: Response, validate: Validator):
        """
        PATCH /api/auth/profile
        Update user profile - compatible with LoopBack
        """
        user = request.user()
        if not user:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'Authentication required'
                }
            }, 401)

        # Validate profile update data
        errors = request.validate(
            validate.string('firstName'),
            validate.string('lastName'),
            validate.email('email')
        )

        if errors:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)

        # Update user fields if provided
        if request.input('firstName'):
            user.first_name = request.input('firstName')
        if request.input('lastName'):
            user.last_name = request.input('lastName')
        if request.input('email'):
            # Check if email is already taken by another user
            existing_user = User.where('email', request.input('email')).where('id', '!=', user.id).first()
            if existing_user:
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 422,
                        'name': 'ValidationError',
                        'message': 'Email already taken',
                        'details': {'email': ['The email has already been taken.']}
                    }
                }, 422)
            user.email = request.input('email')
            # Reset email verification if email changed
            user.email_verified_at = None

        # Update full name if first or last name changed
        if request.input('firstName') or request.input('lastName'):
            first_name = request.input('firstName') or user.first_name or ''
            last_name = request.input('lastName') or user.last_name or ''
            user.name = f"{first_name} {last_name}".strip()

        user.save()

        CorsController.add_cors_headers(response, request.header('Origin'))
        return response.json({
            'message': 'Profile updated successfully',
            'user': {
                'id': user.id,
                'firstName': user.first_name,
                'lastName': user.last_name,                'email': user.email,
                'emailVerified': user.is_email_verified(),
                'twoFactorEnabled': user.two_factor_enabled or False,
                'updatedAt': user.updated_at.isoformat() if user.updated_at else None
            }
        })

    def change_password(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/auth/change-password
        Change user password - compatible with LoopBack
        Enhanced with better error handling and frontend compatibility
        """
        try:
            # Log the incoming request for debugging
            print(f"📥 Change password request from {request.header('Origin')}")
            print(f"📝 Request data fields: {list(request.all().keys())}")
            
            user = request.user()
            if not user:
                print("❌ No authenticated user found")
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            # Validate password change data
            errors = request.validate(
                validate.required(['currentPassword', 'newPassword']),
                validate.string('currentPassword'),
                validate.strong('newPassword', length=8, special=1, uppercase=1)
            )

            # Convert MessageBag to dict if needed
            if errors and hasattr(errors, 'all'):
                error_dict = errors.all()
            else:
                error_dict = errors or {}

            # Additional frontend-compatible validations
            current_password = request.input('currentPassword')
            new_password = request.input('newPassword')
            
            if not current_password or len(current_password.strip()) < 1:
                error_dict['currentPassword'] = ['Current password is required']
                
            if not new_password or len(new_password.strip()) < 8:
                error_dict['newPassword'] = ['New password must be at least 8 characters long']

            if error_dict:
                print(f"❌ Validation errors: {error_dict}")
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 422,
                        'name': 'ValidationError',
                        'message': 'The request body is invalid',
                        'details': error_dict
                    }
                }, 422)

            # Verify current password using User model method
            if not user.verify_password(current_password):
                print(f"❌ Current password verification failed for user {user.id}")
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'BadRequestError',
                        'message': 'Current password is incorrect'
                    }
                }, 400)            # Check if new password is the same as current
            if user.verify_password(new_password):
                print(f"❌ New password same as current for user {user.id}")
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'BadRequestError',
                        'message': 'New password must be different from current password'
                    }
                }, 400)

            # Update password
            user.set_password(new_password)
            # Note: password_changed_at column doesn't exist in current schema
            # user.password_changed_at = datetime.now()  # Commented out until migration adds this column
            user.save()
            
            print(f"✅ Password changed successfully for user {user.id}")

            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'message': 'Password changed successfully'
            })
            
        except Exception as e:
            print(f"❌ Unexpected error during password change: {str(e)}")
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Password change failed due to server error'
                }
            }, 500)

    def resend_verification(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/auth/resend-verification
        Resend email verification - compatible with LoopBack
        """
        # Validate request
        errors = request.validate(
            validate.required(['email']),
            validate.email('email')
        )

        if errors:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all()
                }
            }, 422)

        email = request.input('email')
        user = User.where('email', email).first()

        if not user:
            # Don't reveal if email exists or not
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'message': 'If the email exists and is unverified, a verification link has been sent'
            })

        if user.is_email_verified():
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'BadRequestError',
                    'message': 'Email is already verified'
                }
            }, 400)        # Generate new verification token and send email
        verification_token = user.generate_email_verification_token()

        try:
            Mail.mailable(EmailVerification(user, verification_token).to(user.email)).send()
        except Exception as e:
            print(f"Failed to send verification email: {e}")

        CorsController.add_cors_headers(response, request.header('Origin'))
        return response.json({
            'message': 'If the email exists and is unverified, a verification link has been sent'
        })

    def verify_otp(self, request: Request, response: Response, validate: Validator):
        """
        POST /api/auth/verify-otp
        Verify OTP for frontend compatibility (delegates to OTPController)
        """
        # Import here to avoid circular dependency
        from app.services.OTPService import OTPService
        from masonite.facades import Mail
        
        # Validate request - accept both 'otp' and 'code' field names for compatibility
        errors = request.validate(
            validate.required(['identifier', 'type']),
            validate.string('identifier'),
            validate.string('type')
        )
        
        # Check for OTP code in either 'otp' or 'code' field
        otp_code = request.input('otp') or request.input('code')
        if not otp_code:
            errors['otp'] = ['OTP code is required']
        
        if errors:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors
                }
            }, 422)
        
        try:
            identifier = request.input('identifier')
            otp_type = request.input('type')
            
            print(f"🔍 Auth OTP Verification: {identifier} ({otp_type}) with code: {otp_code}")
            
            # Create OTP service instance
            otp_service = OTPService(Mail())
              # Verify OTP using the service
            result = otp_service.verify_otp(identifier, otp_code, otp_type)
            
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json(result, 200)
            
        except Exception as e:
            print(f"❌ Auth OTP Verification Error: {str(e)}")
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'OTP verification failed'
                }
            }, 500)
    
    def verify_token(self, request: Request, response: Response):
        """
        POST /api/auth/verify-token
        Verify JWT token validity and return user data
        Used by frontend to validate authentication state
        """
        # Add CORS headers
        CorsController.add_cors_headers(response, request.header('Origin'))
        
        # Get token from Authorization header or request body
        auth_header = request.header('Authorization') or ''
        token = None
        
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header[7:]  # Remove 'Bearer ' prefix
        elif request.input('token'):
            token = request.input('token')
        
        if not token:
            return response.json({
                'error': {
                    'statusCode': 401,
                    'name': 'UnauthorizedError',
                    'message': 'No authentication token provided'
                }
            }, 401)        # Validate JWT token using the same logic as middleware
        try:
            # Get JWT secret from environment
            jwt_secret = env('JWT_SECRET') or env('APP_KEY') or 'ZAq1N9Mw2BRTOyCQRew0F9pyUafCRNx4IbvDKAUc7LH7JkGYRD/pPOyYuNO1Q5po'

            # Decode JWT token
            payload = jwt.decode(
                token,
                jwt_secret,
                algorithms=[env('JWT_ALGORITHM', 'HS256')],
                options={"verify_aud": False, "verify_iss": False}
            )

            # Get user ID from token payload
            user_id = payload.get('id') or payload.get('securityId')
            if not user_id:
                raise jwt.InvalidTokenError('User ID not found in token')

            # Find user by ID
            user = User.find(user_id)
            if not user:
                raise jwt.InvalidTokenError('User not found')            # Return success with user data and token validity
            return response.json({
                'valid': True,
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'firstName': user.first_name or '',
                    'lastName': user.last_name or '',
                    'emailVerified': bool(user.email_verified_at),
                    'twoFactorEnabled': bool(user.two_factor_enabled),
                    'createdAt': user.created_at.isoformat() if user.created_at else None,
                    'updatedAt': user.updated_at.isoformat() if user.updated_at else None
                },
                'token': token,
                'expiresAt': payload.get('exp')
            }, 200)

        except jwt.ExpiredSignatureError:
            return response.json({
                'valid': False,
                'error': {
                    'statusCode': 401,
                    'name': 'TokenExpired',
                    'message': 'Token has expired'
                }
            }, 401)
        except jwt.InvalidTokenError as e:
            return response.json({
                'valid': False,
                'error': {
                    'statusCode': 401,
                    'name': 'InvalidToken',
                    'message': 'Invalid authentication token'
                }
            }, 401)
        except Exception as e:
            return response.json({
                'valid': False,
                'error': {                    'statusCode': 500,
                    'name': 'InternalError',
                    'message': 'Token validation failed'
                }
            }, 500)

    def request_disable_2fa(self, request: Request, response: Response):
        """
        POST /api/auth/request-disable-2fa
        Request to disable 2FA via email (for when recovery codes are exhausted)
        """
        from app.services.RecoveryCodeService import RecoveryCodeService
          # Validate request
        errors = request.validate(
            {
                'email': 'required|email'
            }
        )
          # Manually validate optional 'reason' field if provided
        reason = request.input('reason', '')
        if reason and reason not in ['recovery_codes_exhausted', 'lost_device', 'other']:
            if not errors:
                errors = {}
            errors['reason'] = ['The reason must be one of: recovery_codes_exhausted, lost_device, other']
        
        if errors:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors.all() if hasattr(errors, 'all') else errors
                }
            }, 422)

        try:
            email = request.input('email').lower().strip()
            reason = request.input('reason', 'not_specified')
            
            print(f"🔐 2FA disable request received for email: {email}, reason: {reason}")
            
            # Check if user exists and has 2FA enabled
            user = User.where('email', email).first()
            if not user:
                # Don't reveal if user exists or not - security measure
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'message': 'If an account exists with 2FA enabled, a disable confirmation email has been sent. The link will expire in 1 hour.',
                    'allCodesUsed': False
                }, 200)
            
            if not user.two_factor_enabled:
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'message': 'If an account exists with 2FA enabled, a disable confirmation email has been sent. The link will expire in 1 hour.',
                    'allCodesUsed': False
                }, 200)
              # Check if all recovery codes are used
            recovery_service = RecoveryCodeService()
            all_codes_used = recovery_service.are_all_codes_used(user.id)            # Generate secure token for 2FA disable confirmation
            import time
            current_time = time.time()
            expiry_time = current_time + (60 * 60)  # 1 hour from now
            
            token_payload = {
                'user_id': user.id,
                'email': user.email,
                'action': 'disable_2fa',
                'reason': reason,                'exp': expiry_time  # 1 hour expiry
            }
            
            disable_2fa_token = jwt.encode(
                token_payload,
                env('JWT_SECRET') or env('APP_KEY') or 'default-secret-key',
                algorithm='HS256'
            )
            
            # Send 2FA disable confirmation email
            email_sent = False
            email_method = "none"
            
            # Try Brevo API first (more reliable)
            try:
                from app.services.BrevoEmailService import BrevoEmailService
                brevo_service = BrevoEmailService()
                api_result = brevo_service.send_disable_2fa_email(user, disable_2fa_token, reason)
                
                if api_result['success']:
                    email_sent = True
                    email_method = "brevo_api"
                    print(f"✅ 2FA disable email sent via Brevo API to {user.email} (Message ID: {api_result.get('message_id')})")
                else:
                    print(f"⚠️  Brevo API failed: {api_result['error']}")
                    
            except Exception as api_error:
                print(f"⚠️  Brevo API service error: {api_error}")
            
            # Fallback to SMTP if API failed
            if not email_sent:
                try:
                    Mail.mailable(Disable2FARequest(user, disable_2fa_token, reason).to(user.email)).send()
                    email_sent = True
                    email_method = "smtp"
                    print(f"✅ 2FA disable email sent via SMTP to {user.email}")
                except Exception as smtp_error:
                    print(f"❌ SMTP also failed: {smtp_error}")
                    email_method = "failed"
            
            # Log email sending result
            if not email_sent:
                print(f"⚠️  2FA disable email not sent to {user.email}. Manual intervention may be required.")
            
            print(f"✅ 2FA disable request processed for user: {user.email} (method: {email_method})")
            
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'message': 'If an account exists with 2FA enabled, a disable confirmation email has been sent. The link will expire in 1 hour.',
                'allCodesUsed': all_codes_used
            }, 200)
            
        except Exception as e:
            print(f"❌ Error processing 2FA disable request: {e}")
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to process 2FA disable request'
                }
            }, 500)

    def confirm_disable_2fa(self, request: Request, response: Response):
        """
        POST /api/auth/confirm-disable-2fa
        Confirm disabling 2FA with the token sent via email
        """
        # Validate request
        errors = request.validate(
            {
                'token': 'required|string'
            }
        )
        
        if errors:
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({                'error': {
                    'statusCode': 422,
                    'name': 'ValidationError',
                    'message': 'The request body is invalid',
                    'details': errors
                }
            }, 422)

        try:
            token = request.input('token')
            print(f"🔍 Received token for validation: {token[:50]}...")
            
            # Validate and decode the token
            try:
                secret_key = env('JWT_SECRET') or env('APP_KEY') or 'default-secret-key'
                print(f"🔑 Using secret key: {secret_key[:20]}...")
                
                payload = jwt.decode(
                    token,
                    secret_key,
                    algorithms=['HS256']
                )
                print(f"✅ Token decoded successfully: {payload}")
                
                # Verify token is for 2FA disable action
                if payload.get('action') != 'disable_2fa':
                    raise jwt.InvalidTokenError("Invalid token action")
                
                # Check if token is expired (double-check)
                import time
                current_time = time.time()
                if current_time > payload['exp']:
                    raise jwt.ExpiredSignatureError("Token has expired")
                
            except jwt.ExpiredSignatureError:
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 410,
                        'name': 'TokenExpired',
                        'message': 'The 2FA disable confirmation link has expired. Please request a new one.'
                    }
                }, 410)
            except jwt.InvalidTokenError:
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'InvalidToken',
                        'message': 'Invalid or malformed token'
                    }
                }, 400)
            
            # Find the user
            user = User.find(payload['user_id'])
            if not user or user.email != payload['email']:
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'error': {
                        'statusCode': 404,
                        'name': 'UserNotFound',
                        'message': 'User not found or token mismatch'
                    }
                }, 404)
            
            # Check if 2FA is still enabled
            if not user.two_factor_enabled:
                CorsController.add_cors_headers(response, request.header('Origin'))
                return response.json({
                    'message': '2FA is already disabled for this account',
                    'success': True
                }, 200)
            
            # Disable 2FA
            user.two_factor_enabled = False
            user.two_factor_secret = None
            user.save()
            
            # Clear all recovery codes
            from app.services.RecoveryCodeService import RecoveryCodeService
            recovery_service = RecoveryCodeService()
            recovery_service.clear_recovery_codes(user.id)
            
            print(f"✅ 2FA successfully disabled for user: {user.email} (reason: {payload.get('reason', 'not_specified')})")
            
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'message': '2FA has been successfully disabled for your account',
                'success': True,
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'twoFactorEnabled': False
                }
            }, 200)
            
        except Exception as e:
            print(f"❌ Error confirming 2FA disable: {e}")
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to confirm 2FA disable'
                }
            }, 500)

    def get_disable_2fa_status(self, request: Request, response: Response):
        """
        GET /api/auth/disable-2fa-status/{email}
        Get the status of 2FA disable request for an email
        """
        try:
            email = request.param('email').lower().strip()
            
            # TODO: Implement status checking
            # For now, return not implemented
            
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 501,
                    'name': 'NotImplemented',
                    'message': '2FA disable status checking is not yet implemented'
                }
            }, 501)
            
        except Exception as e:
            print(f"❌ Error getting 2FA disable status: {e}")
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to get 2FA disable status'
                }
            }, 500)
