#!/usr/bin/env python3
"""
🔍 User Database Check
======================
Quick script to check existing users and their verification status
"""

import requests
import json

BASE_URL = "http://localhost:3002/api"

def test_user_existence():
    print("🔍 Checking User Database Status")
    print("=" * 35)
    
    # List of test emails to check
    test_emails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    for email in test_emails:
        print(f"\n📧 Testing email: {email}")
        
        # Try resend verification to see user status
        resend_data = {"email": email}
        try:
            response = requests.post(f"{BASE_URL}/auth/resend-verification", json=resend_data)
            print(f"   📊 Resend Status: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            
            if response.status_code == 200:
                response_data = response.json()
                message = response_data.get("message", "")
                
                if "already verified" in message.lower():
                    print(f"   ✅ User exists and is VERIFIED")
                elif "sent" in message.lower() or "unverified" in message.lower():
                    print(f"   ⚠️ User exists but is UNVERIFIED")
                else:
                    print(f"   ❓ User status unclear")
            elif response.status_code == 400:
                response_data = response.json()
                if "already verified" in response_data.get("error", {}).get("message", "").lower():
                    print(f"   ✅ User exists and is VERIFIED")
                else:
                    print(f"   ❓ User might not exist or other error")
            else:
                print(f"   ❌ Unexpected response")
                
        except Exception as e:
            print(f"   💥 Error: {str(e)}")
    
    print(f"\n🔑 Testing Login Attempts")
    print("-" * 25)
      # Test login with different password combinations
    test_credentials = [
        {"email": "<EMAIL>", "password": "secret"},  # Seeded user
        {"email": "<EMAIL>", "password": "SecurePass123!"},  # Created user
        {"email": "<EMAIL>", "password": "test123"},
        {"email": "<EMAIL>", "password": "SecurePass123!"},  # Created user
        {"email": "<EMAIL>", "password": "test123"}
    ]
    
    for creds in test_credentials:
        print(f"\n🔐 Trying login: {creds['email']} / {creds['password']}")
        
        try:
            response = requests.post(f"{BASE_URL}/auth/login", json=creds)
            print(f"   📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ LOGIN SUCCESS!")
                response_data = response.json()
                if "access_token" in response_data or "token" in response_data:
                    print(f"   🔑 Auth token received")
            elif response.status_code == 401:
                response_data = response.json()
                message = response_data.get("error", {}).get("message", "")
                print(f"   ❌ Login failed: {message}")
                
                if "email" in message.lower() and "verif" in message.lower():
                    print(f"   📧 Failed due to email verification requirement")
                elif "password" in message.lower() or "invalid" in message.lower():
                    print(f"   🔑 Failed due to invalid credentials")
            else:
                print(f"   ❓ Unexpected status: {response.status_code}")
                
        except Exception as e:
            print(f"   💥 Error: {str(e)}")

def main():
    test_user_existence()

if __name__ == "__main__":
    main()
