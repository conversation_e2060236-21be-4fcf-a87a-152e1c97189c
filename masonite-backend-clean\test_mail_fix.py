#!/usr/bin/env python3
"""
Test script for signup endpoint after Mail fixes
"""

import requests
import json

def test_signup_mail():
    print('🧪 Testing signup with mail sending...')

    signup_data = {
        'firstName': 'Test',
        'lastName': 'Mail',
        'email': '<EMAIL>',
        'phone': '',
        'password': 'TestMail123!',
        'confirmPassword': 'TestMail123!',
        'acceptTerms': True
    }

    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }

    try:
        response = requests.post('http://localhost:3002/api/auth/signup', 
                               json=signup_data, 
                               headers=headers)
        
        print(f'Status Code: {response.status_code}')
        print(f'Response: {response.text}')
        
        if response.status_code == 201:
            print('✅ Signup successful!')
            data = response.json()
            print(f'User ID: {data.get("user", {}).get("id")}')
            print(f'Email: {data.get("user", {}).get("email")}')
            print(f'Token received: {"Yes" if data.get("token") else "No"}')
            return True
        else:
            print(f'❌ Signup failed with status {response.status_code}')
            return False
            
    except Exception as e:
        print(f'Error: {e}')
        return False

if __name__ == "__main__":
    test_signup_mail()
