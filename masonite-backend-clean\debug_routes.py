#!/usr/bin/env python3
"""
Route Debug Script
Test what routes are actually registered in the Masonite application
"""

import os
import sys

# Add the project root to the Python path
project_root = r"c:\Users\<USER>\Downloads\study\apps\ai\cody\Modular backend secure user system and payment_Cody\masonite-backend-clean"
sys.path.insert(0, project_root)
os.chdir(project_root)

try:
    from masonite.foundation import Application
    from masonite.utils.location import base_path
    from masonite.configuration import config
    from Kernel import Kernel as ApplicationKernel
    from masonite.foundation import Kernel

    print("=== Route Debug Script ===")
    print(f"Project root: {project_root}")
    
    # Initialize application
    application = Application(base_path())
    application.register_providers(Kernel, ApplicationKernel)
    
    # Get router
    router = application.make("router")
    routes = router.routes
    
    print(f"\nTotal routes registered: {len(routes)}")
    
    # Find routes that match our login path
    login_routes = []
    api_auth_routes = []
    
    for route in routes:
        route_path = getattr(route, 'route_url', '')
        route_method = getattr(route, 'method_type', [])
        
        # Look for login routes
        if 'login' in route_path.lower():
            login_routes.append({
                'path': route_path,
                'methods': route_method if isinstance(route_method, list) else [route_method],
                'controller': getattr(route, 'controller', 'Unknown'),
                'middleware': getattr(route, 'middleware', [])
            })
        
        # Look for API auth routes
        if '/api/auth' in route_path or route_path.startswith('/auth'):
            api_auth_routes.append({
                'path': route_path,
                'methods': route_method if isinstance(route_method, list) else [route_method],
                'controller': getattr(route, 'controller', 'Unknown'),
                'middleware': getattr(route, 'middleware', [])
            })
    
    print("\n=== Login Routes ===")
    if login_routes:
        for i, route in enumerate(login_routes, 1):
            print(f"{i}. Path: {route['path']}")
            print(f"   Methods: {route['methods']}")
            print(f"   Controller: {route['controller']}")
            print(f"   Middleware: {route['middleware']}")
            print()
    else:
        print("No login routes found!")
    
    print("\n=== API Auth Routes ===")
    if api_auth_routes:
        for i, route in enumerate(api_auth_routes[:10], 1):  # Show first 10
            print(f"{i}. Path: {route['path']}")
            print(f"   Methods: {route['methods']}")
            print(f"   Controller: {route['controller']}")
            print(f"   Middleware: {route['middleware']}")
            print()
        if len(api_auth_routes) > 10:
            print(f"... and {len(api_auth_routes) - 10} more API auth routes")
    else:
        print("No API auth routes found!")
    
    # Specifically look for /api/auth/login
    print("\n=== Searching for /api/auth/login ===")
    found_target = False
    for route in routes:
        route_path = getattr(route, 'route_url', '')
        if route_path == '/api/auth/login':
            found_target = True
            route_method = getattr(route, 'method_type', [])
            print(f"Found exact match: {route_path}")
            print(f"Methods: {route_method if isinstance(route_method, list) else [route_method]}")
            print(f"Controller: {getattr(route, 'controller', 'Unknown')}")
            print(f"Middleware: {getattr(route, 'middleware', [])}")
            break
    
    if not found_target:
        print("❌ /api/auth/login route NOT FOUND in registered routes!")
        print("\nChecking for similar routes:")
        for route in routes:
            route_path = getattr(route, 'route_url', '')
            if 'auth/login' in route_path:
                route_method = getattr(route, 'method_type', [])
                print(f"  Similar: {route_path} [{route_method}]")

except Exception as e:
    print(f"Error during route debug: {e}")
    import traceback
    traceback.print_exc()
