param(
    [string]$TestType = "all"
)

$BaseUrl = "http://localhost:3002/api"

# Function to test OPTIONS request (CORS preflight)
function Test-OptionsRequest {
    param([string]$Endpoint)
    
    try {
        Write-Host "🔍 Testing OPTIONS for $Endpoint..."
        
        $headers = @{
            'Origin' = 'http://localhost:4200'
            'Access-Control-Request-Method' = 'POST'
            'Access-Control-Request-Headers' = 'Content-Type, Authorization, X-Requested-With'
        }
        
        $response = Invoke-WebRequest -Uri "$BaseUrl$Endpoint" -Method OPTIONS -Headers $headers -UseBasicParsing -TimeoutSec 5
        
        Write-Host "✅ OPTIONS $Endpoint - Status: $($response.StatusCode)"
        
        # Check CORS headers
        $corsHeaders = @('Access-Control-Allow-Origin', 'Access-Control-Allow-Methods', 'Access-Control-Allow-Headers')
        foreach ($header in $corsHeaders) {
            if ($response.Headers[$header]) {
                Write-Host "  $header`: $($response.Headers[$header])"
            } else {
                Write-Host "  ❌ Missing: $header"
            }
        }
        
        return $true
    } catch {
        Write-Host "❌ OPTIONS $Endpoint failed: $($_.Exception.Message)"
        return $false
    }
}

# Function to test signup with exact frontend data structure
function Test-SignupLikeFrontend {
    Write-Host "`n🧪 Testing SIGNUP with frontend-like data..."
    
    # Test OPTIONS first
    $optionsWorked = Test-OptionsRequest "/auth/signup"
    
    # Test the actual signup request
    $signupData = @{
        email = "<EMAIL>"
        firstName = "Frontend"
        lastName = "Test"
        password = "FrontendTest123!"
        confirmPassword = "FrontendTest123!"
    } | ConvertTo-Json
    
    $headers = @{
        'Content-Type' = 'application/json'
        'Origin' = 'http://localhost:4200'
        'X-Requested-With' = 'XMLHttpRequest'
    }
    
    try {
        Write-Host "📤 Sending signup request..."
        Write-Host "Data: $signupData"
        
        $response = Invoke-WebRequest -Uri "$BaseUrl/auth/signup" -Method POST -Body $signupData -Headers $headers -UseBasicParsing
        
        Write-Host "✅ Signup Status: $($response.StatusCode)"
        Write-Host "Response: $($response.Content)"
        
        # Parse response to get token
        $responseObj = $response.Content | ConvertFrom-Json
        return $responseObj.token
    } catch {
        Write-Host "❌ Signup failed:"
        Write-Host "  Status: $($_.Exception.Response.StatusCode.Value__)"
        Write-Host "  Error: $($_.Exception.Message)"
        
        if ($_.Exception.Response) {
            $result = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($result)
            $responseBody = $reader.ReadToEnd()
            Write-Host "  Body: $responseBody"
        }
        return $null
    }
}

# Function to test change password with exact frontend data structure
function Test-ChangePasswordLikeFrontend {
    Write-Host "`n🧪 Testing CHANGE PASSWORD with frontend-like data..."
    
    # First login to get a token
    $loginData = @{
        email = "<EMAIL>"
        password = "TestPassword123!"
    } | ConvertTo-Json
    
    $headers = @{
        'Content-Type' = 'application/json'
        'Origin' = 'http://localhost:4200'
        'X-Requested-With' = 'XMLHttpRequest'
    }
    
    try {
        Write-Host "🔐 Logging in first..."
        $loginResponse = Invoke-WebRequest -Uri "$BaseUrl/auth/login" -Method POST -Body $loginData -Headers $headers -UseBasicParsing
        $loginObj = $loginResponse.Content | ConvertFrom-Json
        $token = $loginObj.token
        
        if (-not $token) {
            Write-Host "❌ No token received from login"
            return
        }
        
        Write-Host "✅ Login successful, got token"
        
        # Test OPTIONS for change-password
        $optionsWorked = Test-OptionsRequest "/auth/change-password"
        
        # Test change password with frontend structure (including confirmPassword)
        $changePasswordData = @{
            currentPassword = "TestPassword123!"
            newPassword = "NewFrontendTest123!"
            # Note: Frontend PasswordChange interface has confirmPassword but auth service doesn't send it
        } | ConvertTo-Json
        
        $authHeaders = @{
            'Content-Type' = 'application/json'
            'Authorization' = "Bearer $token"
            'Origin' = 'http://localhost:4200'
            'X-Requested-With' = 'XMLHttpRequest'
        }
        
        Write-Host "📤 Sending change password request..."
        Write-Host "Data: $changePasswordData"
        
        $response = Invoke-WebRequest -Uri "$BaseUrl/auth/change-password" -Method POST -Body $changePasswordData -Headers $authHeaders -UseBasicParsing
        
        Write-Host "✅ Change Password Status: $($response.StatusCode)"
        Write-Host "Response: $($response.Content)"
        
    } catch {
        Write-Host "❌ Change Password failed:"
        Write-Host "  Status: $($_.Exception.Response.StatusCode.Value__)"
        Write-Host "  Error: $($_.Exception.Message)"
        
        if ($_.Exception.Response) {
            $result = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($result)
            $responseBody = $reader.ReadToEnd()
            Write-Host "  Body: $responseBody"
        }
    }
}

# Function to test with malformed data (to trigger 422)
function Test-MalformedRequests {
    Write-Host "`n🧪 Testing MALFORMED requests (should return 422)..."
    
    # Test signup with missing fields
    $malformedSignup = @{
        email = "<EMAIL>"
        # Missing firstName, lastName, password, confirmPassword
    } | ConvertTo-Json
    
    $headers = @{
        'Content-Type' = 'application/json'
        'Origin' = 'http://localhost:4200'
        'X-Requested-With' = 'XMLHttpRequest'
    }
    
    try {
        Write-Host "📤 Testing malformed signup (missing fields)..."
        $response = Invoke-WebRequest -Uri "$BaseUrl/auth/signup" -Method POST -Body $malformedSignup -Headers $headers -UseBasicParsing
        Write-Host "❌ Should have failed but got: $($response.StatusCode)"
    } catch {
        Write-Host "✅ Correctly rejected malformed signup with status: $($_.Exception.Response.StatusCode.Value__)"
        
        if ($_.Exception.Response.StatusCode.Value__ -eq 422) {
            $result = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($result)
            $responseBody = $reader.ReadToEnd()
            Write-Host "  422 Error Body: $responseBody"
        }
    }
}

# Main execution
Write-Host "🚀 Frontend-Backend API Compatibility Test"
Write-Host "=" * 60

switch ($TestType) {
    "signup" { Test-SignupLikeFrontend }
    "change-password" { Test-ChangePasswordLikeFrontend }
    "malformed" { Test-MalformedRequests }
    "all" {
        Test-SignupLikeFrontend
        Test-ChangePasswordLikeFrontend
        Test-MalformedRequests
    }
    default { 
        Write-Host "Usage: .\test_frontend_compatibility.ps1 -TestType [signup|change-password|malformed|all]"
    }
}

Write-Host "`n✅ Test completed!"
