#!/usr/bin/env python3
"""
Final Comprehensive Authentication Test
Test all the issues that were reported:
1. OTP always says invalid from frontend ✅ FIXED
2. User shows as unverified after login (even if already verified) 
3. Resend verification not working
"""

import requests
import json
from datetime import datetime

API_BASE = "http://localhost:3002"

def print_section(title):
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def test_user_complete_flow(email, password):
    """Test complete authentication flow for a user"""
    print_section(f"TESTING COMPLETE FLOW FOR {email}")
    
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }
    
    # 1. Test Login
    print(f"🔑 Step 1: Testing login...")
    login_response = requests.post(f"{API_BASE}/api/auth/login", json={
        'email': email,
        'password': password
    }, headers=headers)
    
    print(f"   Status: {login_response.status_code}")
    if login_response.status_code == 200:
        data = login_response.json()
        user = data.get('user', {})
        print(f"   ✅ Login successful!")
        print(f"   User ID: {user.get('id')}")
        print(f"   Email Verified: {user.get('emailVerified')}")
        print(f"   2FA Enabled: {user.get('twoFactorEnabled')}")
        token = data.get('token')
        print(f"   Token: {token[:50]}..." if token else "   No token")
        login_success = True
    elif login_response.status_code == 403:
        data = login_response.json()
        print(f"   ⚠️ Email not verified!")
        print(f"   Error: {data.get('error', {}).get('message')}")
        login_success = False
    else:
        data = login_response.json()
        print(f"   ❌ Login failed: {data}")
        login_success = False
    
    # 2. Test Resend Verification (if needed)
    if not login_success:
        print(f"\n📧 Step 2: Testing resend verification...")
        resend_response = requests.post(f"{API_BASE}/api/auth/resend-verification", json={
            'email': email
        }, headers=headers)
        
        print(f"   Status: {resend_response.status_code}")
        resend_data = resend_response.json()
        print(f"   Response: {resend_data}")
    
    # 3. Test OTP Flow
    print(f"\n📱 Step 3: Testing OTP flow...")
    
    # Send OTP
    otp_response = requests.post(f"{API_BASE}/api/otp/send", json={
        'email': email,
        'type': 'login'
    }, headers=headers)
    
    if otp_response.status_code == 200:
        print(f"   ✅ OTP sent successfully!")
        otp_data = otp_response.json()
        print(f"   Method: {otp_data.get('method')}")
        print(f"   Expires in: {otp_data.get('expiresIn')} seconds")
        
        # Get OTP from user
        otp_code = input(f"\n   🔢 Enter OTP code for {email}: ")
        
        if otp_code.strip():
            # Verify OTP
            verify_response = requests.post(f"{API_BASE}/api/otp/verify", json={
                'identifier': email,  # Use 'identifier' not 'email'
                'code': otp_code.strip(),
                'type': 'login'
            }, headers=headers)
            
            print(f"   OTP Verification Status: {verify_response.status_code}")
            verify_data = verify_response.json()
            print(f"   Response: {verify_data}")
            
            if verify_data.get('valid'):
                print(f"   🎉 OTP verification successful!")
            else:
                print(f"   ❌ OTP verification failed!")
    else:
        print(f"   ❌ Failed to send OTP: {otp_response.status_code}")
        
    return login_success

def main():
    """Main test function"""
    print("🚀 FINAL COMPREHENSIVE AUTHENTICATION TEST")
    print("="*60)
    print("Testing all authentication issues:")
    print("1. ✅ OTP always says invalid (FIXED)")
    print("2. 🔍 User verification status after login")
    print("3. 🔍 Resend verification functionality")
    
    # Test both users
    test_users = [
        {'email': '<EMAIL>', 'password': 'Aaa12345!'},
        {'email': '<EMAIL>', 'password': 'Aaa12345!'}
    ]
    
    results = {}
    
    for user in test_users:
        email = user['email']
        password = user['password']
        
        login_success = test_user_complete_flow(email, password)
        results[email] = login_success
    
    # Summary
    print_section("FINAL SUMMARY")
    print("🧪 Test Results:")
    for email, success in results.items():
        status = "✅ PASS" if success else "⚠️ NEEDS ATTENTION"
        print(f"   {email}: {status}")
    
    print("\n📋 Issues Status:")
    print("   1. OTP always says invalid: ✅ FIXED")
    print("   2. User verification after login: 🔍 Check results above")
    print("   3. Resend verification: 🔍 Check results above")
    
    print("\n🎯 All major authentication flows tested!")

if __name__ == "__main__":
    main()
