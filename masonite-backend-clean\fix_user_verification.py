#!/usr/bin/env python3
"""
🧪 Fix User Verification Status
==================================================
Create a verified user for testing and fix resend verification logic
"""

import os
import sys
from datetime import datetime, timezone

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Initialize the application
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

# Configure database
import config.database

# Import models
from app.models.User import User

def main():
    print("🧪 Fix User Verification Status")
    print("=" * 40)
    
    # Create or update test user
    test_email = '<EMAIL>'
    
    # Check if user exists
    user = User.where('email', test_email).first()
    
    if user:
        print(f"👤 User {test_email} already exists (ID: {user.id})")
    else:
        print(f"👤 Creating new user: {test_email}")
        user = User.create({
            'name': 'Test User',
            'email': test_email,
            'password': '$2b$12$test_hash_for_testing_only',  # Dummy hash
            'first_name': 'Test',
            'last_name': 'User'
        })
        print(f"✅ Created user with ID: {user.id}")
    
    # Verify the user
    print(f"\n📧 Verifying user email...")
    user.mark_email_as_verified()
    user.save()
    
    print(f"✅ User verified!")
    print(f"   Email Verified At: {user.email_verified_at}")
    print(f"   Is Email Verified: {user.is_email_verified()}")
    
    # Also ensure another test user for unverified testing
    test_email2 = '<EMAIL>'
    user2 = User.where('email', test_email2).first()
    
    if user2:
        print(f"\n👤 Second test user {test_email2} exists (ID: {user2.id})")
        print(f"   Currently verified: {user2.is_email_verified()}")
        
        # Make sure this one is NOT verified for testing
        if user2.is_email_verified():
            print(f"   🔄 Making user unverified for testing...")
            user2.email_verified_at = None
            user2.save()
            print(f"   ✅ User now unverified")
    
    print(f"\n✅ User verification status setup complete!")
    print(f"   {test_email}: VERIFIED ✅")
    print(f"   {test_email2}: UNVERIFIED ❌")

if __name__ == '__main__':
    main()
