#!/usr/bin/env python3
"""Test Raw HTTP Redirect Response"""

import socket
import time

def test_raw_http_redirect():
    """Test raw HTTP redirect to see exact Location header"""
    print("🔍 Testing Raw HTTP Redirect Response")
    print("=" * 50)
    
    # Wait for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    # Create raw HTTP request
    host = "localhost"
    port = 3002
    path = "/api/auth/oauth/callback?error=test_error&state=test_state"
    
    # Build HTTP request
    request = f"GET {path} HTTP/1.1\r\n"
    request += f"Host: {host}:{port}\r\n"
    request += "Connection: close\r\n"
    request += "\r\n"
    
    print(f"📤 Sending raw HTTP request to {host}:{port}{path}")
    
    try:
        # Create socket and connect
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect((host, port))
        
        # Send request
        sock.send(request.encode())
        
        # Receive response
        response = b""
        while True:
            try:
                data = sock.recv(4096)
                if not data:
                    break
                response += data
            except socket.timeout:
                break
        
        sock.close()
        
        # Parse response
        response_str = response.decode('utf-8', errors='ignore')
        lines = response_str.split('\r\n')
        
        print("\n📥 Raw HTTP Response:")
        print("-" * 40)
        
        # Print status line and headers
        for i, line in enumerate(lines):
            if line == "":  # Empty line indicates end of headers
                break
            print(f"   {line}")
            
            # Check for Location header specifically
            if line.lower().startswith('location:'):
                location = line.split(':', 1)[1].strip()
                print(f"\n🔗 Location Header Analysis:")
                print(f"   Raw Location: '{location}'")
                
                if location.startswith('http://localhost:4200'):
                    print("   ✅ CORRECT: Location header contains port 4200")
                elif location.startswith('http://localhost/'):
                    print("   ❌ WRONG: Location header missing port")
                elif location.startswith('http://localhost:3002'):
                    print("   ❌ WRONG: Location header has backend port")
                else:
                    print(f"   ❓ UNEXPECTED: Location header format: {location}")
        
        print("-" * 40)
        
        # Check status code
        if lines and lines[0]:
            status_line = lines[0]
            if "302" in status_line:
                print("✅ Status: 302 redirect detected")
            else:
                print(f"❌ Unexpected status: {status_line}")
        
        return True
        
    except Exception as e:
        print(f"❌ Raw HTTP test failed: {e}")
        return False

if __name__ == "__main__":
    test_raw_http_redirect()
