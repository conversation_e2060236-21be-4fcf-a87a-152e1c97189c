{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/two-factor.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nimport * as i11 from \"@angular/material/checkbox\";\nimport * as i12 from \"@angular/material/tooltip\";\nfunction TwoFactorSetupComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\", 5);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Initializing 2FA setup...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TwoFactorSetupComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"qr_code_scanner\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Step 1: Scan QR Code\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 8)(7, \"p\");\n    i0.ɵɵtext(8, \"Scan this QR code with your authenticator app:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 9);\n    i0.ɵɵelement(10, \"img\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 11)(12, \"p\")(13, \"strong\");\n    i0.ɵɵtext(14, \"Recommended apps:\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"ul\")(16, \"li\");\n    i0.ɵɵtext(17, \"Google Authenticator\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"li\");\n    i0.ɵɵtext(19, \"Microsoft Authenticator\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"li\");\n    i0.ɵɵtext(21, \"Authy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"li\");\n    i0.ɵɵtext(23, \"1Password\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 12)(25, \"p\");\n    i0.ɵɵtext(26, \"Can't scan the QR code? Enter this key manually:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"mat-form-field\", 13)(28, \"mat-label\");\n    i0.ɵɵtext(29, \"Secret Key\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"input\", 14);\n    i0.ɵɵelementStart(31, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function TwoFactorSetupComponent_div_9_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.copySecret());\n    });\n    i0.ɵɵelementStart(32, \"mat-icon\");\n    i0.ɵɵtext(33, \"content_copy\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(34, \"div\", 16)(35, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function TwoFactorSetupComponent_div_9_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancel());\n    });\n    i0.ɵɵtext(36, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function TwoFactorSetupComponent_div_9_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(38, \"Next\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"src\", ctx_r1.setupData.qrCode, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"value\", ctx_r1.setupData.secret);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n  }\n}\nfunction TwoFactorSetupComponent_div_10_mat_error_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Code is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TwoFactorSetupComponent_div_10_mat_error_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Code must be 6 digits \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TwoFactorSetupComponent_div_10_mat_spinner_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 26);\n  }\n}\nfunction TwoFactorSetupComponent_div_10_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Verify & Enable\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TwoFactorSetupComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"verified_user\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Step 2: Verify Setup\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"form\", 19);\n    i0.ɵɵlistener(\"ngSubmit\", function TwoFactorSetupComponent_div_10_Template_form_ngSubmit_6_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onVerifyCode());\n    });\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Enter the 6-digit code from your authenticator app:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-form-field\", 20)(10, \"mat-label\");\n    i0.ɵɵtext(11, \"6-digit code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 21);\n    i0.ɵɵtemplate(13, TwoFactorSetupComponent_div_10_mat_error_13_Template, 2, 0, \"mat-error\", 22)(14, TwoFactorSetupComponent_div_10_mat_error_14_Template, 2, 0, \"mat-error\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 16)(16, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function TwoFactorSetupComponent_div_10_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStep());\n    });\n    i0.ɵɵtext(17, \"Back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 24);\n    i0.ɵɵtemplate(19, TwoFactorSetupComponent_div_10_mat_spinner_19_Template, 1, 0, \"mat-spinner\", 25)(20, TwoFactorSetupComponent_div_10_span_20_Template, 2, 0, \"span\", 22);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.verificationForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r1.verificationForm.get(\"code\")) == null ? null : tmp_2_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.verificationForm.get(\"code\")) == null ? null : tmp_3_0.hasError(\"pattern\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading || ctx_r1.verificationForm.invalid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction TwoFactorSetupComponent_div_11_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const code_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", code_r5, \" \");\n  }\n}\nfunction TwoFactorSetupComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"backup\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Step 3: Save Backup Codes\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 27)(7, \"div\", 28)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\")(11, \"strong\");\n    i0.ɵɵtext(12, \"Important:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Save these backup codes in a secure location. Each code can only be used once if you lose access to your authenticator app.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 29)(15, \"div\", 30);\n    i0.ɵɵtemplate(16, TwoFactorSetupComponent_div_11_div_16_Template, 2, 1, \"div\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 32)(18, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function TwoFactorSetupComponent_div_11_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadBackupCodes());\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Download Codes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function TwoFactorSetupComponent_div_11_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.copyBackupCodes());\n    });\n    i0.ɵɵelementStart(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"content_copy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \" Copy Codes \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 34)(27, \"mat-checkbox\", 35);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TwoFactorSetupComponent_div_11_Template_mat_checkbox_ngModelChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.backupCodesDownloaded, $event) || (ctx_r1.backupCodesDownloaded = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(28, \" I have saved my backup codes in a secure location \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 16)(30, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function TwoFactorSetupComponent_div_11_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.completeSetup());\n    });\n    i0.ɵɵtext(31, \" Complete Setup \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.setupData.backupCodes);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.backupCodesDownloaded);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.backupCodesDownloaded);\n  }\n}\nexport let TwoFactorSetupComponent = /*#__PURE__*/(() => {\n  class TwoFactorSetupComponent {\n    constructor(twoFactorService, formBuilder, snackBar) {\n      this.twoFactorService = twoFactorService;\n      this.formBuilder = formBuilder;\n      this.snackBar = snackBar;\n      this.setupComplete = new EventEmitter();\n      this.cancelled = new EventEmitter();\n      this.currentStep = 1;\n      this.setupData = null;\n      this.backupCodesDownloaded = false;\n      this.loading = false;\n      this.verificationForm = this.formBuilder.group({\n        code: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]],\n        method: ['authenticator']\n      });\n    }\n    ngOnInit() {\n      this.initializeSetup();\n    }\n    initializeSetup() {\n      this.loading = true;\n      this.twoFactorService.setup2FA().subscribe({\n        next: data => {\n          this.setupData = data;\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.error?.message || 'Failed to initialize 2FA setup', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n          this.cancelled.emit();\n        }\n      });\n    }\n    nextStep() {\n      if (this.currentStep < 3) {\n        this.currentStep++;\n      }\n    }\n    previousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--;\n      }\n    }\n    onVerifyCode() {\n      if (this.verificationForm.invalid) {\n        this.markFormGroupTouched(this.verificationForm);\n        return;\n      }\n      this.loading = true;\n      const {\n        code,\n        method\n      } = this.verificationForm.value;\n      // Clean the code to ensure it's exactly 6 digits\n      const cleanCode = code.replace(/\\s/g, '').padStart(6, '0');\n      this.twoFactorService.verify2FA(cleanCode, method).subscribe({\n        next: response => {\n          this.snackBar.open('2FA enabled successfully!', 'Close', {\n            duration: 3000\n          });\n          this.loading = false;\n          this.nextStep();\n        },\n        error: error => {\n          let errorMessage = 'Invalid verification code';\n          if (error.error?.message) {\n            errorMessage = error.error.message;\n          } else if (error.status === 400) {\n            errorMessage = 'Invalid code. Please check your authenticator app and try again.';\n          } else if (error.status === 401) {\n            errorMessage = 'Authentication failed. Please log in again.';\n          }\n          this.snackBar.open(errorMessage, 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n          // Clear the form for retry\n          this.verificationForm.patchValue({\n            code: ''\n          });\n        }\n      });\n    }\n    downloadBackupCodes() {\n      if (!this.setupData?.backupCodes) return;\n      const codes = this.setupData.backupCodes.join('\\n');\n      const blob = new Blob(['2FA Backup Codes\\n', '==================\\n', 'Keep these codes safe! Each can only be used once.\\n\\n', codes, '\\n\\nGenerated: ' + new Date().toISOString()], {\n        type: 'text/plain'\n      });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = '2fa-backup-codes.txt';\n      link.click();\n      window.URL.revokeObjectURL(url);\n      this.backupCodesDownloaded = true;\n    }\n    copyBackupCodes() {\n      if (!this.setupData?.backupCodes) return;\n      const codes = this.setupData.backupCodes.join('\\n');\n      navigator.clipboard.writeText(codes).then(() => {\n        this.snackBar.open('Backup codes copied to clipboard!', 'Close', {\n          duration: 2000\n        });\n      }).catch(() => {\n        this.snackBar.open('Failed to copy backup codes', 'Close', {\n          duration: 3000\n        });\n      });\n    }\n    copySecret() {\n      if (!this.setupData?.secret) return;\n      navigator.clipboard.writeText(this.setupData.secret).then(() => {\n        this.snackBar.open('Secret key copied to clipboard!', 'Close', {\n          duration: 2000\n        });\n      }).catch(() => {\n        this.snackBar.open('Failed to copy secret key', 'Close', {\n          duration: 3000\n        });\n      });\n    }\n    completeSetup() {\n      this.setupComplete.emit();\n    }\n    cancel() {\n      this.cancelled.emit();\n    }\n    markFormGroupTouched(formGroup) {\n      Object.keys(formGroup.controls).forEach(key => {\n        const control = formGroup.get(key);\n        control?.markAsTouched();\n      });\n    }\n    static #_ = this.ɵfac = function TwoFactorSetupComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TwoFactorSetupComponent)(i0.ɵɵdirectiveInject(i1.TwoFactorService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TwoFactorSetupComponent,\n      selectors: [[\"app-two-factor-setup\"]],\n      outputs: {\n        setupComplete: \"setupComplete\",\n        cancelled: \"cancelled\"\n      },\n      standalone: false,\n      decls: 12,\n      vars: 4,\n      consts: [[1, \"two-factor-setup-container\"], [1, \"setup-card\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"step-container\", 4, \"ngIf\"], [1, \"loading-container\"], [\"diameter\", \"40\"], [1, \"step-container\"], [1, \"step-header\"], [1, \"qr-section\"], [1, \"qr-code-container\"], [\"alt\", \"2FA QR Code\", 1, \"qr-code\", 3, \"src\"], [1, \"authenticator-apps\"], [1, \"manual-entry\"], [\"appearance\", \"outline\", 1, \"secret-field\"], [\"matInput\", \"\", \"readonly\", \"\", 3, \"value\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"matTooltip\", \"Copy secret\", 3, \"click\"], [1, \"step-actions\"], [\"mat-button\", \"\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"verification-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"code-field\"], [\"matInput\", \"\", \"formControlName\", \"code\", \"placeholder\", \"123456\", \"maxlength\", \"6\", \"autocomplete\", \"off\"], [4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"diameter\", \"20\"], [1, \"backup-codes-section\"], [1, \"warning-message\"], [1, \"backup-codes\"], [1, \"codes-grid\"], [\"class\", \"backup-code\", 4, \"ngFor\", \"ngForOf\"], [1, \"backup-actions\"], [\"mat-stroked-button\", \"\", 3, \"click\"], [1, \"confirmation\"], [3, \"ngModelChange\", \"ngModel\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [1, \"backup-code\"]],\n      template: function TwoFactorSetupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\");\n          i0.ɵɵtext(4, \"Setup Two-Factor Authentication\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n          i0.ɵɵtext(6, \"Secure your account with an additional layer of protection\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-card-content\");\n          i0.ɵɵtemplate(8, TwoFactorSetupComponent_div_8_Template, 4, 0, \"div\", 2)(9, TwoFactorSetupComponent_div_9_Template, 39, 3, \"div\", 3)(10, TwoFactorSetupComponent_div_10_Template, 21, 7, \"div\", 3)(11, TwoFactorSetupComponent_div_11_Template, 32, 3, \"div\", 3);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading && !ctx.setupData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1 && ctx.setupData && !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2 && ctx.setupData && !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3 && ctx.setupData);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.MaxLengthValidator, i2.FormGroupDirective, i2.FormControlName, i2.NgModel, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardSubtitle, i5.MatCardTitle, i6.MatButton, i6.MatIconButton, i7.MatIcon, i8.MatFormField, i8.MatLabel, i8.MatError, i8.MatSuffix, i9.MatInput, i10.MatProgressSpinner, i11.MatCheckbox, i12.MatTooltip],\n      styles: [\"@charset \\\"UTF-8\\\";.two-factor-setup-container[_ngcontent-%COMP%]{max-width:600px;margin:0 auto;padding:20px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]{min-height:400px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;margin-bottom:24px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:28px;width:28px;height:28px;color:#1976d2}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;color:#1976d2}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]{text-align:center}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .qr-code-container[_ngcontent-%COMP%]{margin:24px 0}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .qr-code-container[_ngcontent-%COMP%]   .qr-code[_ngcontent-%COMP%]{max-width:200px;height:auto;border:2px solid #e0e0e0;border-radius:8px;padding:16px;background:#fff}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .authenticator-apps[_ngcontent-%COMP%]{margin:24px 0;text-align:left}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .authenticator-apps[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style-type:none;padding-left:0}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .authenticator-apps[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{padding:4px 0}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .authenticator-apps[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before{content:\\\"\\\\1f4f1  \\\";margin-right:8px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .manual-entry[_ngcontent-%COMP%]{margin-top:24px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .manual-entry[_ngcontent-%COMP%]   .secret-field[_ngcontent-%COMP%]{width:100%}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .qr-section[_ngcontent-%COMP%]   .manual-entry[_ngcontent-%COMP%]   .secret-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{font-family:Courier New,monospace;font-size:14px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .verification-form[_ngcontent-%COMP%]{text-align:center}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .verification-form[_ngcontent-%COMP%]   .code-field[_ngcontent-%COMP%]{width:200px;margin:24px 0}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .verification-form[_ngcontent-%COMP%]   .code-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{text-align:center;font-size:24px;font-weight:700;letter-spacing:4px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:12px;padding:16px;background-color:#fff3cd;border:1px solid #ffeaa7;border-radius:8px;margin-bottom:24px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#856404;margin-top:2px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#856404}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-codes[_ngcontent-%COMP%]{margin:24px 0}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-codes[_ngcontent-%COMP%]   .codes-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(120px,1fr));gap:12px;padding:16px;background-color:#f8f9fa;border-radius:8px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-codes[_ngcontent-%COMP%]   .codes-grid[_ngcontent-%COMP%]   .backup-code[_ngcontent-%COMP%]{font-family:Courier New,monospace;font-weight:700;font-size:16px;text-align:center;padding:8px;background:#fff;border:1px solid #dee2e6;border-radius:4px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-actions[_ngcontent-%COMP%]{display:flex;gap:12px;justify-content:center;margin:24px 0}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .confirmation[_ngcontent-%COMP%]{margin:24px 0;text-align:center}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-top:32px;padding-top:16px;border-top:1px solid #e0e0e0}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin-right:8px}.two-factor-setup-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{text-align:center;padding:40px}.two-factor-setup-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin:0 auto 16px}@media (max-width: 600px){.two-factor-setup-container[_ngcontent-%COMP%]{padding:12px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .codes-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .codes-grid[_ngcontent-%COMP%]   .backup-code[_ngcontent-%COMP%]{font-size:14px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-actions[_ngcontent-%COMP%]{flex-direction:column}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .backup-codes-section[_ngcontent-%COMP%]   .backup-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]{flex-direction:column;gap:12px}.two-factor-setup-container[_ngcontent-%COMP%]   .setup-card[_ngcontent-%COMP%]   .step-container[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}}\"]\n    });\n  }\n  return TwoFactorSetupComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}