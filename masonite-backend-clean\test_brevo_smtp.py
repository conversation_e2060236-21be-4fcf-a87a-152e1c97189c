#!/usr/bin/env python3
"""
Brevo SMTP Test and Verification Script
Tests actual email sending via Brevo SMTP with detailed debugging
"""

import os
import sys

# Add the masonite project to path
sys.path.append(os.path.join(os.path.dirname(__file__)))

# Initialize Masonite application
from wsgi import application

from masonite.facades import Mail
from masonite.environment import env
from app.mailables.EmailVerification import EmailVerification
from app.models.User import User

def test_brevo_smtp_connection():
    """Test Brevo SMTP connection and configuration"""
    
    print("🔍 Brevo SMTP Configuration Test")
    print("=" * 50)
    
    # Display current configuration
    print("📧 Current SMTP Configuration:")
    print(f"   MAIL_DRIVER: {env('MAIL_DRIVER')}")
    print(f"   MAIL_HOST: {env('MAIL_HOST')}")
    print(f"   MAIL_PORT: {env('MAIL_PORT')}")
    print(f"   MAIL_USERNAME: {env('MAIL_USERNAME')}")
    print(f"   MAIL_FROM: {env('MAIL_FROM')}")
    print(f"   MAIL_ENCRYPTION: {env('MAIL_ENCRYPTION', 'Not Set')}")
    print(f"   Password: {'***' if env('MAIL_PASSWORD') else 'NOT SET'}")
    
    # Validate configuration
    print("\n🔧 Configuration Validation:")
    issues = []
    
    if env('MAIL_DRIVER') != 'smtp':
        issues.append("❌ MAIL_DRIVER should be 'smtp'")
    else:
        print("✅ MAIL_DRIVER is set to 'smtp'")
    
    if not env('MAIL_HOST'):
        issues.append("❌ MAIL_HOST is not set")
    elif env('MAIL_HOST') == 'smtp-relay.brevo.com':
        print("✅ MAIL_HOST is correctly set for Brevo")
    else:
        issues.append(f"⚠️  MAIL_HOST: {env('MAIL_HOST')} (should be smtp-relay.brevo.com)")
    
    if env('MAIL_PORT') != '587':
        issues.append(f"⚠️  MAIL_PORT: {env('MAIL_PORT')} (recommended: 587)")
    else:
        print("✅ MAIL_PORT is set to 587 (TLS)")
    
    if not env('MAIL_USERNAME'):
        issues.append("❌ MAIL_USERNAME is not set")
    else:
        print("✅ MAIL_USERNAME is configured")
    
    if not env('MAIL_PASSWORD'):
        issues.append("❌ MAIL_PASSWORD is not set")
    else:
        print("✅ MAIL_PASSWORD is configured")
    
    if env('MAIL_ENCRYPTION') != 'tls':
        issues.append("⚠️  MAIL_ENCRYPTION should be 'tls' for Brevo")
    else:
        print("✅ MAIL_ENCRYPTION is set to 'tls'")
    
    if issues:
        print("\n🚨 Configuration Issues Found:")
        for issue in issues:
            print(f"   {issue}")
        return False
    else:
        print("\n✅ All configuration checks passed!")
        return True

def test_email_sending():
    """Test actual email sending"""
    
    print("\n📧 Email Sending Test")
    print("=" * 30)
    
    try:
        # Find or create a test user
        test_user = User.where('email', '<EMAIL>').first()
        
        if not test_user:
            print("⚠️  Creating test user for email testing...")
            test_user = User.create({
                'name': 'Test User',
                'email': '<EMAIL>',
                'password': 'temporary_password',
                'email_verified_at': None
            })
        
        print(f"👤 Test user: {test_user.email}")
        
        # Generate test verification token
        verification_token = "test_verification_token_123456"
        
        print("📤 Attempting to send verification email...")
        
        # Create and send the email
        Mail.mailable(EmailVerification(test_user, verification_token).to(test_user.email)).send()
        
        print("✅ Email sent successfully via Brevo SMTP!")
        print("📬 Check the recipient's inbox for the verification email")
        
        return True
        
    except Exception as e:
        print(f"❌ Email sending failed: {e}")
        
        # Provide specific troubleshooting based on error
        error_str = str(e).lower()
        
        print("\n🔧 Troubleshooting Suggestions:")
        
        if "535" in error_str or "authentication" in error_str:
            print("   🔑 Authentication Issue:")
            print("      - Verify Brevo API key is correct and active")
            print("      - Check if the SMTP login matches your Brevo account")
            print("      - Ensure the API key has SMTP permissions")
            
        elif "connection" in error_str or "timeout" in error_str:
            print("   🌐 Connection Issue:")
            print("      - Check internet connection")
            print("      - Verify firewall allows SMTP traffic on port 587")
            print("      - Try using port 25 or 465 if 587 doesn't work")
            
        elif "554" in error_str or "relay" in error_str:
            print("   📧 Relay Issue:")
            print("      - Verify sender email is authorized in Brevo")
            print("      - Check if domain is verified in your Brevo account")
            print("      - Ensure you're not exceeding sending limits")
            
        else:
            print("   🔍 General troubleshooting:")
            print("      - Double-check all SMTP credentials")
            print("      - Verify Brevo account is active and not suspended")
            print("      - Check Brevo dashboard for any restrictions")
        
        return False

def provide_brevo_setup_guide():
    """Provide setup guide for Brevo SMTP"""
    
    print("\n📚 Brevo SMTP Setup Guide")
    print("=" * 30)
    
    print("1. 🔑 Get Brevo SMTP Credentials:")
    print("   - Login to your Brevo (Sendinblue) account")
    print("   - Go to SMTP & API → SMTP")
    print("   - Copy your SMTP server details")
    
    print("\n2. 📧 Verify Sender Address:")
    print("   - Ensure your 'from' email is verified in Brevo")
    print("   - Add sender authentication if required")
    
    print("\n3. ⚙️  Required .env Settings:")
    print("   MAIL_DRIVER=smtp")
    print("   MAIL_HOST=smtp-relay.brevo.com")
    print("   MAIL_PORT=587")
    print("   MAIL_ENCRYPTION=tls")
    print("   MAIL_USERNAME=[your-smtp-login]")
    print("   MAIL_PASSWORD=[your-smtp-password]")
    print("   MAIL_FROM=[verified-sender-email]")
    
    print("\n4. 🧪 Testing:")
    print("   - Restart Masonite server after .env changes")
    print("   - Use this script to test SMTP connection")
    print("   - Check Brevo dashboard for sending statistics")

if __name__ == "__main__":
    print("🚀 Brevo SMTP Configuration and Testing")
    print("=" * 50)
    
    # Step 1: Test configuration
    config_ok = test_brevo_smtp_connection()
    
    if config_ok:
        # Step 2: Test email sending
        email_ok = test_email_sending()
        
        if email_ok:
            print("\n🎉 SUCCESS: Brevo SMTP is working correctly!")
            print("💡 You can now register users and emails will be sent via Brevo")
        else:
            print("\n❌ Email sending failed - see troubleshooting above")
    else:
        print("\n❌ Configuration issues found - please fix them first")
    
    # Always show the setup guide
    provide_brevo_setup_guide()
    
    print("\n" + "=" * 50)
    print("🏁 Test completed. Check results above for next steps.")
