#!/usr/bin/env python3
"""
Comprehensive Authentication Fix Script
Addresses the following issues:
1. OTP always says invalid from frontend
2. User shows as unverified after login (even if already verified)
3. Resend verification sends "sent successfully" but doesn't actually send email for verified users
4. Fix user verification status for test users
"""

import requests
import json
import psycopg2
from datetime import datetime, timedelta
import time

# Configuration
API_BASE = "http://localhost:3002"
DB_CONFIG = {
    'host': 'localhost',
    'database': 'masonite_secure_backend',
    'user': 'postgres',
    'password': 'password'
}

TEST_USERS = [
    {
        'email': '<EMAIL>',
        'password': 'Aaa12345!',
        'should_verify': True
    },
    {
        'email': '<EMAIL>',
        'password': 'Aaa12345!',  # We'll set this password
        'should_verify': True
    }
]

def print_section(title):
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def test_api_call(method, url, data=None, headers=None):
    """Make API call with proper error handling"""
    if headers is None:
        headers = {
            'Content-Type': 'application/json',
            'Origin': 'http://localhost:4200'
        }
    
    try:
        if method.upper() == 'POST':
            response = requests.post(url, json=data, headers=headers, timeout=15)
        elif method.upper() == 'GET':
            response = requests.get(url, headers=headers, timeout=15)
        else:
            raise ValueError(f"Unsupported method: {method}")
        
        return response
    except Exception as e:
        print(f"❌ API Error: {e}")
        return None

def fix_user_verification_in_db():
    """Fix user verification status directly in database"""
    print_section("FIXING USER VERIFICATION STATUS IN DATABASE")
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        for user_data in TEST_USERS:
            email = user_data['email']
            password = user_data['password']
            
            print(f"\n🔧 Fixing user: {email}")
            
            # First, check if user exists
            cursor.execute("SELECT id, email_verified_at FROM users WHERE email = %s", (email,))
            user = cursor.fetchone()
            
            if user:
                user_id, verified_at = user
                print(f"   User ID: {user_id}, Currently verified: {verified_at}")
                
                if user_data['should_verify'] and not verified_at:
                    # Verify the user
                    cursor.execute("""
                        UPDATE users SET 
                            email_verified_at = NOW(),
                            verified_at = NOW(),
                            email_verification_token = NULL,
                            email_verification_expires = NULL,
                            updated_at = NOW()
                        WHERE email = %s
                    """, (email,))
                    
                    conn.commit()
                    print(f"   ✅ Email verified for {email}")
                
                # Update password if needed
                from passlib.context import CryptContext
                pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
                hashed_password = pwd_context.hash(password)
                
                cursor.execute("""
                    UPDATE users SET 
                        password = %s,
                        updated_at = NOW()
                    WHERE email = %s
                """, (hashed_password, email))
                
                conn.commit()
                print(f"   🔑 Password updated for {email}")
                
            else:
                print(f"   ⚠️ User {email} not found")
        
        cursor.close()
        conn.close()
        print("✅ Database fixes completed!")
        
    except Exception as e:
        print(f"❌ Database error: {e}")

def clean_old_otps():
    """Clean old OTP records that might be interfering"""
    print_section("CLEANING OLD OTP RECORDS")
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Delete old/expired OTPs
        cursor.execute("""
            DELETE FROM otps 
            WHERE expires_at < NOW() OR used = true
        """)
        
        deleted_count = cursor.rowcount
        conn.commit()
        
        print(f"🧹 Cleaned {deleted_count} old OTP records")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ OTP cleanup error: {e}")

def test_otp_flow_detailed(email):
    """Test OTP flow with detailed debugging"""
    print_section(f"DETAILED OTP TESTING FOR {email}")
    
    # 1. Send OTP
    print("📱 Step 1: Sending OTP...")
    otp_response = test_api_call('POST', f"{API_BASE}/api/otp/send", {
        'email': email,
        'type': 'login'
    })
    
    if not otp_response or otp_response.status_code != 200:
        print(f"❌ Failed to send OTP: {otp_response.status_code if otp_response else 'No response'}")
        return False
    
    print("✅ OTP sent successfully")
    otp_data = otp_response.json()
    print(f"   Response: {otp_data}")
    
    # 2. Check OTP in database
    print("\n🔍 Step 2: Checking OTP in database...")
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Get the latest OTP for this email
        cursor.execute("""
            SELECT id, identifier, code_hash, otp_type, expires_at, used, attempts, max_attempts
            FROM otps 
            WHERE identifier = %s AND otp_type = 'login'
            ORDER BY created_at DESC 
            LIMIT 1
        """, (email,))
        
        otp_record = cursor.fetchone()
        
        if otp_record:
            otp_id, identifier, code_hash, otp_type, expires_at, used, attempts, max_attempts = otp_record
            print(f"   📋 OTP Record Found:")
            print(f"      ID: {otp_id}")
            print(f"      Identifier: {identifier}")
            print(f"      Hash: {code_hash[:20]}...")
            print(f"      Type: {otp_type}")
            print(f"      Expires: {expires_at}")
            print(f"      Used: {used}")
            print(f"      Attempts: {attempts}/{max_attempts}")
            
            # 3. Test with a known code (for testing purposes)
            test_code = input(f"\n🔢 Enter the OTP code you received for {email}: ")
            
            if test_code:
                print(f"\n🔍 Step 3: Testing OTP verification with code: {test_code}")
                
                verify_response = test_api_call('POST', f"{API_BASE}/api/otp/verify", {
                    'email': email,
                    'code': test_code,
                    'type': 'login'
                })
                
                if verify_response:
                    print(f"   📊 Verification Status: {verify_response.status_code}")
                    verify_data = verify_response.json()
                    print(f"   📋 Response: {verify_data}")
                    
                    # Check the code hash manually
                    from passlib.context import CryptContext
                    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
                    
                    is_code_valid = pwd_context.verify(test_code, code_hash)
                    print(f"   🔐 Manual hash verification: {is_code_valid}")
                    
                    # Check updated OTP record
                    cursor.execute("""
                        SELECT used, attempts FROM otps WHERE id = %s
                    """, (otp_id,))
                    
                    updated_record = cursor.fetchone()
                    if updated_record:
                        used, attempts = updated_record
                        print(f"   📊 Updated OTP record - Used: {used}, Attempts: {attempts}")
                
        else:
            print("   ❌ No OTP record found")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Database OTP check error: {e}")
    
    return True

def test_login_verification_flow(email, password):
    """Test complete login and verification flow"""
    print_section(f"TESTING LOGIN & VERIFICATION FLOW FOR {email}")
    
    # 1. Test login
    print("🔑 Step 1: Testing login...")
    login_response = test_api_call('POST', f"{API_BASE}/api/auth/login", {
        'email': email,
        'password': password
    })
    
    if login_response:
        print(f"   📊 Login Status: {login_response.status_code}")
        login_data = login_response.json()
        print(f"   📋 Response: {login_data}")
        
        if login_response.status_code == 200:
            user = login_data.get('user', {})
            print(f"   ✅ Login successful!")
            print(f"      Email Verified: {user.get('emailVerified')}")
            print(f"      2FA Enabled: {user.get('twoFactorEnabled')}")
            return True
        elif login_response.status_code == 403:
            print(f"   ⚠️ Email not verified, testing resend verification...")
            
            # 2. Test resend verification
            resend_response = test_api_call('POST', f"{API_BASE}/api/auth/resend-verification", {
                'email': email
            })
            
            if resend_response:
                print(f"   📊 Resend Status: {resend_response.status_code}")
                resend_data = resend_response.json()
                print(f"   📋 Response: {resend_data}")
    
    return False

def main():
    """Main execution function"""
    print("🚀 COMPREHENSIVE AUTHENTICATION FIX")
    print("="*60)
    print("This script will:")
    print("1. Fix user verification status in database")
    print("2. Clean old OTP records")
    print("3. Test login and verification flows")
    print("4. Test OTP generation and verification")
    print("5. Identify and report issues")
    
    # Step 1: Fix database issues
    fix_user_verification_in_db()
    
    # Step 2: Clean old OTPs
    clean_old_otps()
    
    # Step 3: Wait a moment for server
    print("\n⏳ Waiting 3 seconds for server...")
    time.sleep(3)
    
    # Step 4: Test each user
    for user_data in TEST_USERS:
        email = user_data['email']
        password = user_data['password']
        
        # Test login/verification flow
        login_success = test_login_verification_flow(email, password)
        
        # Test OTP flow regardless of login status
        test_otp_flow_detailed(email)
    
    print_section("SUMMARY")
    print("✅ Script completed!")
    print("📋 Check the results above for specific issues.")
    print("🔧 If OTP verification still fails, check:")
    print("   - OTP model verify_code method")
    print("   - Hash generation and verification logic")
    print("   - Database timezone issues")
    print("   - API endpoint routing")

if __name__ == "__main__":
    main()
