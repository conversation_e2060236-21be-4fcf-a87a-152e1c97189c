{"ast": null, "code": "export const timeoutProvider = {\n  setTimeout(handler, timeout, ...args) {\n    const {\n      delegate\n    } = timeoutProvider;\n    if (delegate === null || delegate === void 0 ? void 0 : delegate.setTimeout) {\n      return delegate.setTimeout(handler, timeout, ...args);\n    }\n    return setTimeout(handler, timeout, ...args);\n  },\n  clearTimeout(handle) {\n    const {\n      delegate\n    } = timeoutProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearTimeout) || clearTimeout)(handle);\n  },\n  delegate: undefined\n};\n//# sourceMappingURL=timeoutProvider.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}