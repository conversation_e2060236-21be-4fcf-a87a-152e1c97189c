#!/usr/bin/env python3
"""
Debug registration step by step
"""

import requests
import json

BASE_URL = "http://localhost:3002"
TEST_EMAIL = "<EMAIL>"

def test_step_by_step():
    print("=== Step 1: Initial Registration ===")
    data1 = {
        'email': TEST_EMAIL,
        'password': 'TestPassword123!',
        'confirmPassword': 'TestPassword123!',
        'firstName': 'Debug',
        'lastName': 'Test'
    }
    
    response1 = requests.post(f"{BASE_URL}/api/auth/signup", json=data1)
    print(f"Initial registration - Status: {response1.status_code}")
    print(f"Response: {response1.text}")
    
    if response1.status_code not in [200, 201]:
        print("Initial registration failed")
        return
    
    user_data = response1.json()
    token = user_data.get('token')
    
    print("\n=== Step 2: Login ===")
    login_data = {
        'email': TEST_EMAIL,
        'password': 'TestPassword123!'
    }
    
    response2 = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    print(f"Login - Status: {response2.status_code}")
    print(f"Response: {response2.text}")
    
    if response2.status_code == 200:
        token = response2.json().get('token')
    
    if not token:
        print("No token available")
        return
    
    print("\n=== Step 3: Delete Account ===")
    delete_data = {
        'preserve_payment_data': True,
        'preserve_transaction_history': True,
        'preserve_profile_data': True,
        'preserve_security_logs': True,
        'reason': 'Testing preserved data logic'
    }
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    response3 = requests.post(f"{BASE_URL}/api/account/request-deletion", json=delete_data, headers=headers)
    print(f"Delete request - Status: {response3.status_code}")
    print(f"Response: {response3.text}")
    
    if response3.status_code != 200:
        print("Delete request failed")
        return
    
    delete_response = response3.json()
    confirmation_token = delete_response.get('confirmationToken')
    
    if not confirmation_token:
        print("No confirmation token")
        return
    
    print("\n=== Step 4: Confirm Deletion ===")
    confirm_data = {
        'token': confirmation_token
    }
    
    response4 = requests.post(f"{BASE_URL}/api/account/confirm-deletion", json=confirm_data)
    print(f"Confirm deletion - Status: {response4.status_code}")
    print(f"Response: {response4.text}")
    
    print("\n=== Step 5: Re-registration (without restorePreservedData) ===")
    data5 = {
        'email': TEST_EMAIL,
        'password': 'NewPassword456!',
        'confirmPassword': 'NewPassword456!',
        'firstName': 'Debug',
        'lastName': 'ReTest'
    }
    
    response5 = requests.post(f"{BASE_URL}/api/auth/signup", json=data5)
    print(f"Re-registration - Status: {response5.status_code}")
    print(f"Response: {response5.text}")

if __name__ == "__main__":
    test_step_by_step()
