#!/usr/bin/env python3
"""
Advanced Route Debug Script
Check route loading and registration process
"""

import os
import sys
import importlib.util

# Add the project root to the Python path
project_root = r"c:\Users\<USER>\Downloads\study\apps\ai\cody\Modular backend secure user system and payment_Cody\masonite-backend-clean"
sys.path.insert(0, project_root)
os.chdir(project_root)

print("=== Advanced Route Debug ===")
print(f"Project root: {project_root}")

# Check if routes/api.py can be imported
try:
    print("\n1. Testing direct import of routes/api.py...")
    spec = importlib.util.spec_from_file_location("api_routes", "routes/api.py")
    api_routes_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(api_routes_module)
    
    if hasattr(api_routes_module, 'ROUTES'):
        routes = api_routes_module.ROUTES
        print(f"   ✅ Successfully imported {len(routes)} routes from routes/api.py")
        
        # Check for login routes
        login_routes = []
        for route in routes:
            route_path = getattr(route, 'route', '')
            route_method = getattr(route, 'method', '')
            if 'login' in route_path.lower():
                login_routes.append(f"{route_method} {route_path}")
        
        if login_routes:
            print(f"   Login routes found: {login_routes}")
        else:
            print("   ❌ No login routes found in imported routes!")
            
        # Show first few routes for debugging
        print(f"\n   First 5 routes:")
        for i, route in enumerate(routes[:5]):
            route_path = getattr(route, 'route', 'Unknown')
            route_method = getattr(route, 'method', 'Unknown')
            print(f"     {i+1}. {route_method} {route_path}")
    else:
        print("   ❌ No ROUTES variable found in api.py")
        
except Exception as e:
    print(f"   ❌ Error importing routes/api.py: {e}")
    import traceback
    traceback.print_exc()

# Check masonite utilities
try:
    print("\n2. Testing Masonite route loading utilities...")
    from masonite.utils.structures import load
    
    # Try to load using Masonite's load function
    routes_from_load = load("routes/api", "ROUTES")
    print(f"   ✅ Masonite load() returned {len(routes_from_load)} routes")
    
    # Check for login routes using load
    login_routes_load = []
    for route in routes_from_load:
        route_path = getattr(route, 'route', '')
        route_method = getattr(route, 'method', '')
        if 'login' in route_path.lower():
            login_routes_load.append(f"{route_method} {route_path}")
    
    if login_routes_load:
        print(f"   Login routes found via load(): {login_routes_load}")
    else:
        print("   ❌ No login routes found via load()!")
        
except Exception as e:
    print(f"   ❌ Error using Masonite load(): {e}")
    import traceback
    traceback.print_exc()

# Check application initialization
try:
    print("\n3. Testing application route registration...")
    from masonite.foundation import Application
    from masonite.utils.location import base_path
    from Kernel import Kernel as ApplicationKernel
    from masonite.foundation import Kernel
    from masonite.routes import Route
    
    application = Application(base_path())
    application.register_providers(Kernel, ApplicationKernel)
    
    # Check if api routes location is set
    api_location = application.make("api.routes.location")
    print(f"   API routes location: {api_location}")
    
    # Check router
    router = application.make("router")
    all_routes = router.routes
    print(f"   Total routes in router: {len(all_routes)}")
    
    # Check route groups
    route_groups = getattr(router, 'route_groups', [])
    print(f"   Route groups: {len(route_groups)}")
    
    # Show a sample of routes
    print("\n   Sample of registered routes:")
    for i, route in enumerate(all_routes[:10]):
        route_path = getattr(route, 'route_url', 'Unknown')
        route_method = getattr(route, 'method_type', 'Unknown')
        print(f"     {i+1}. {route_method} {route_path}")
        
except Exception as e:
    print(f"   ❌ Error in application initialization: {e}")
    import traceback
    traceback.print_exc()

print("\n=== Route Debug Complete ===")
