#!/usr/bin/env python3
"""
Script to search for users with specific email patterns
"""

import os
import sys

# Add the masonite project to path
sys.path.append(os.path.join(os.path.dirname(__file__)))

# Initialize Masonite application
from wsgi import application

# Now we can import models after app initialization
from app.models.User import User

def search_users():
    """Search for users with ahuja or jitesh in email"""
    print("🔍 Searching for users with 'ahuja' or 'jitesh' in email...")
    
    try:
        # Search for users with ahuja or jitesh in email (case insensitive)
        users = User.where('email', 'like', '%ahuja%').or_where('email', 'like', '%jitesh%').get()
        
        if users.count() == 0:
            print("ℹ️  No users found with 'ahuja' or 'jitesh' in email")
            
            # Let's also search for yahoo.in domain
            yahoo_users = User.where('email', 'like', '%yahoo.in%').get()
            if yahoo_users.count() > 0:
                print(f"Found {yahoo_users.count()} users with yahoo.in domain:")
                for user in yahoo_users:
                    print(f"  ID: {user.id} | Email: {user.email} | Name: {user.name}")
            else:
                print("No users found with yahoo.in domain either")
            return
            
        print(f"Found {users.count()} users:")
        print("-" * 80)
        
        for user in users:
            print(f"ID: {user.id:3d} | Email: {user.email:35s} | Name: {user.name or 'N/A':20s} | Created: {user.created_at}")
            
    except Exception as e:
        print(f"❌ Error searching users: {str(e)}")

if __name__ == "__main__":
    search_users()
