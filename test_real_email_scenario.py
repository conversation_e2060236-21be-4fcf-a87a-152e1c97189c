#!/usr/bin/env python3
"""
Test the real email scenario to verify the fix
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:3002/api"
EMAIL = "<EMAIL>"

def test_real_email_scenario():
    """Test the real email scenario"""
    print("🧪 Testing Real Email Scenario")
    print("=" * 50)
    
    # Check preserved data for the real email
    print(f"📝 Checking preserved data for: {EMAIL}")
    response = requests.post(f"{BASE_URL}/account/check-preserved-data", 
                           json={'email': EMAIL})
    
    if response.status_code != 200:
        print(f"❌ Check preserved data failed: {response.text}")
        return False
    
    result = response.json()
    has_preserved = result.get('hasPreservedData', False)
    preserved_summary = result.get('preservedDataSummary', {})
    
    print(f"📊 Has preserved data: {has_preserved}")
    print(f"📊 Preserved data summary: {preserved_summary}")
    
    # Analyze the results
    print(f"\n📋 Analysis:")
    
    if not has_preserved:
        print("✅ PASS: No preserved data found (user chose no preservation)")
        return True
    
    # If there is preserved data, check what categories
    expected_categories = ['security']  # Based on your logs, only security should be preserved
    unexpected_categories = ['payments', 'profile', 'transactions']
    
    success = True
    
    # Check expected categories
    for category in expected_categories:
        if category in preserved_summary:
            print(f"✅ PASS: {category} is correctly preserved")
        else:
            print(f"⚠️ INFO: {category} was expected but not found (might be cleared)")
    
    # Check unexpected categories
    for category in unexpected_categories:
        if category in preserved_summary:
            print(f"❌ FAIL: {category} should NOT be preserved but it appears in summary")
            print(f"   This indicates the bug is still present!")
            success = False
        else:
            print(f"✅ PASS: {category} is correctly NOT preserved")
    
    return success

def main():
    """Main test function"""
    print("🚀 Testing Real Email Scenario")
    print("Checking if the selective preservation fix works for the real email")
    print("=" * 80)
    
    success = test_real_email_scenario()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 REAL EMAIL SCENARIO TEST PASSED!")
        print("✅ The selective preservation fix is working correctly")
    else:
        print("❌ REAL EMAIL SCENARIO TEST FAILED!")
        print("⚠️ The bug is still present - unwanted data categories are preserved")
    print("🏁 Test completed!")

if __name__ == "__main__":
    main()
