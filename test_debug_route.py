#!/usr/bin/env python3
"""
Debug test to see the actual error
"""

import requests
import urllib.parse

# Configuration
BASE_URL = "http://localhost:3002/api"

def test_debug():
    """Test to see actual error"""
    print("🐛 Debug Test...")

    simple_email = "<EMAIL>"

    try:
        url = f"{BASE_URL}/account/check-preserved-data"
        print(f"📡 Testing URL: {url}")
        response = requests.post(url, json={'email': simple_email})
        print(f"✅ Status: {response.status_code}")

        # Print the response
        print("📄 Response:")
        print(response.text[:1000])  # First 1000 characters

    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_debug()
