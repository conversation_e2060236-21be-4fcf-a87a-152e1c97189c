#!/usr/bin/env python3
"""
Debug test to see the actual error
"""

import requests
import urllib.parse

# Configuration
BASE_URL = "http://localhost:3002/api"

def test_debug():
    """Test to see actual error"""
    print("🐛 Debug Test...")
    
    simple_email = "<EMAIL>"
    
    try:
        url = f"{BASE_URL}/account/check-preserved-data/{simple_email}"
        print(f"📡 Testing URL: {url}")
        response = requests.get(url)
        print(f"✅ Status: {response.status_code}")
        
        # Print the full response to see the error
        if response.status_code == 500:
            print("📄 Full Response:")
            print(response.text[:2000])  # First 2000 characters
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_debug()
