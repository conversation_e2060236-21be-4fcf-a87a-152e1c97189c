"use strict";(self.webpackChunksecure_frontend=self.webpackChunksecure_frontend||[]).push([[461],{4050:(te,Q,me)=>{me(6935)},6935:()=>{const te=globalThis;function Q(e){return(te.__Zone_symbol_prefix||"__zone_symbol__")+e}const Ee=Object.getOwnPropertyDescriptor,Ne=Object.defineProperty,Ze=Object.getPrototypeOf,_t=Object.create,dt=Array.prototype.slice,Le="addEventListener",Ie="removeEventListener",Me=Q(Le),Ae=Q(Ie),ae="true",le="false",ve=Q("");function je(e,r){return Zone.current.wrap(e,r)}function He(e,r,c,t,i){return Zone.current.scheduleMacroTask(e,r,c,t,i)}const j=Q,we=typeof window<"u",be=we?window:void 0,$=we&&be||globalThis;function xe(e,r){for(let c=e.length-1;c>=0;c--)"function"==typeof e[c]&&(e[c]=je(e[c],r+"_"+c));return e}function Ue(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&typeof e.set>"u")}const We=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,Ce=!("nw"in $)&&typeof $.process<"u"&&"[object process]"===$.process.toString(),Ve=!Ce&&!We&&!(!we||!be.HTMLElement),qe=typeof $.process<"u"&&"[object process]"===$.process.toString()&&!We&&!(!we||!be.HTMLElement),De={},gt=j("enable_beforeunload"),Xe=function(e){if(!(e=e||$.event))return;let r=De[e.type];r||(r=De[e.type]=j("ON_PROPERTY"+e.type));const c=this||e.target||$,t=c[r];let i;return Ve&&c===be&&"error"===e.type?(i=t&&t.call(this,e.message,e.filename,e.lineno,e.colno,e.error),!0===i&&e.preventDefault()):(i=t&&t.apply(this,arguments),"beforeunload"===e.type&&$[gt]&&"string"==typeof i?e.returnValue=i:null!=i&&!i&&e.preventDefault()),i};function Ye(e,r,c){let t=Ee(e,r);if(!t&&c&&Ee(c,r)&&(t={enumerable:!0,configurable:!0}),!t||!t.configurable)return;const i=j("on"+r+"patched");if(e.hasOwnProperty(i)&&e[i])return;delete t.writable,delete t.value;const u=t.get,E=t.set,T=r.slice(2);let p=De[T];p||(p=De[T]=j("ON_PROPERTY"+T)),t.set=function(D){let _=this;!_&&e===$&&(_=$),_&&("function"==typeof _[p]&&_.removeEventListener(T,Xe),E?.call(_,null),_[p]=D,"function"==typeof D&&_.addEventListener(T,Xe,!1))},t.get=function(){let D=this;if(!D&&e===$&&(D=$),!D)return null;const _=D[p];if(_)return _;if(u){let R=u.call(this);if(R)return t.set.call(this,R),"function"==typeof D.removeAttribute&&D.removeAttribute(r),R}return null},Ne(e,r,t),e[i]=!0}function $e(e,r,c){if(r)for(let t=0;t<r.length;t++)Ye(e,"on"+r[t],c);else{const t=[];for(const i in e)"on"==i.slice(0,2)&&t.push(i);for(let i=0;i<t.length;i++)Ye(e,t[i],c)}}const re=j("originalInstance");function Pe(e){const r=$[e];if(!r)return;$[j(e)]=r,$[e]=function(){const i=xe(arguments,e);switch(i.length){case 0:this[re]=new r;break;case 1:this[re]=new r(i[0]);break;case 2:this[re]=new r(i[0],i[1]);break;case 3:this[re]=new r(i[0],i[1],i[2]);break;case 4:this[re]=new r(i[0],i[1],i[2],i[3]);break;default:throw new Error("Arg list too long.")}},fe($[e],r);const c=new r(function(){});let t;for(t in c)"XMLHttpRequest"===e&&"responseBlob"===t||function(i){"function"==typeof c[i]?$[e].prototype[i]=function(){return this[re][i].apply(this[re],arguments)}:Ne($[e].prototype,i,{set:function(u){"function"==typeof u?(this[re][i]=je(u,e+"."+i),fe(this[re][i],u)):this[re][i]=u},get:function(){return this[re][i]}})}(t);for(t in r)"prototype"!==t&&r.hasOwnProperty(t)&&($[e][t]=r[t])}function ue(e,r,c){let t=e;for(;t&&!t.hasOwnProperty(r);)t=Ze(t);!t&&e[r]&&(t=e);const i=j(r);let u=null;if(t&&(!(u=t[i])||!t.hasOwnProperty(i))&&(u=t[i]=t[r],Ue(t&&Ee(t,r)))){const T=c(u,i,r);t[r]=function(){return T(this,arguments)},fe(t[r],u)}return u}function kt(e,r,c){let t=null;function i(u){const E=u.data;return E.args[E.cbIdx]=function(){u.invoke.apply(this,arguments)},t.apply(E.target,E.args),u}t=ue(e,r,u=>function(E,T){const p=c(E,T);return p.cbIdx>=0&&"function"==typeof T[p.cbIdx]?He(p.name,T[p.cbIdx],p,i):u.apply(E,T)})}function fe(e,r){e[j("OriginalDelegate")]=r}let Ke=!1,Fe=!1;function pt(){if(Ke)return Fe;Ke=!0;try{const e=be.navigator.userAgent;(-1!==e.indexOf("MSIE ")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/"))&&(Fe=!0)}catch{}return Fe}function Je(e){return"function"==typeof e}function Qe(e){return"number"==typeof e}const mt={useG:!0},ne={},et={},tt=new RegExp("^"+ve+"(\\w+)(true|false)$"),nt=j("propagationStopped");function rt(e,r){const c=(r?r(e):e)+le,t=(r?r(e):e)+ae,i=ve+c,u=ve+t;ne[e]={},ne[e][le]=i,ne[e][ae]=u}function yt(e,r,c,t){const i=t&&t.add||Le,u=t&&t.rm||Ie,E=t&&t.listeners||"eventListeners",T=t&&t.rmAll||"removeAllListeners",p=j(i),D="."+i+":",_="prependListener",R="."+_+":",M=function(y,h,H){if(y.isRemoved)return;const V=y.callback;let Y;"object"==typeof V&&V.handleEvent&&(y.callback=g=>V.handleEvent(g),y.originalDelegate=V);try{y.invoke(y,h,[H])}catch(g){Y=g}const F=y.options;return F&&"object"==typeof F&&F.once&&h[u].call(h,H.type,y.originalDelegate?y.originalDelegate:y.callback,F),Y};function x(y,h,H){if(!(h=h||e.event))return;const V=y||h.target||e,Y=V[ne[h.type][H?ae:le]];if(Y){const F=[];if(1===Y.length){const g=M(Y[0],V,h);g&&F.push(g)}else{const g=Y.slice();for(let U=0;U<g.length&&(!h||!0!==h[nt]);U++){const O=M(g[U],V,h);O&&F.push(O)}}if(1===F.length)throw F[0];for(let g=0;g<F.length;g++){const U=F[g];r.nativeScheduleMicroTask(()=>{throw U})}}}const z=function(y){return x(this,y,!1)},K=function(y){return x(this,y,!0)};function J(y,h){if(!y)return!1;let H=!0;h&&void 0!==h.useG&&(H=h.useG);const V=h&&h.vh;let Y=!0;h&&void 0!==h.chkDup&&(Y=h.chkDup);let F=!1;h&&void 0!==h.rt&&(F=h.rt);let g=y;for(;g&&!g.hasOwnProperty(i);)g=Ze(g);if(!g&&y[i]&&(g=y),!g||g[p])return!1;const U=h&&h.eventNameToString,O={},w=g[p]=g[i],b=g[j(u)]=g[u],S=g[j(E)]=g[E],ee=g[j(T)]=g[T];let W;h&&h.prepend&&(W=g[j(h.prepend)]=g[h.prepend]);const q=H?function(s){if(!O.isExisting)return w.call(O.target,O.eventName,O.capture?K:z,O.options)}:function(s){return w.call(O.target,O.eventName,s.invoke,O.options)},A=H?function(s){if(!s.isRemoved){const l=ne[s.eventName];let v;l&&(v=l[s.capture?ae:le]);const C=v&&s.target[v];if(C)for(let k=0;k<C.length;k++)if(C[k]===s){C.splice(k,1),s.isRemoved=!0,s.removeAbortListener&&(s.removeAbortListener(),s.removeAbortListener=null),0===C.length&&(s.allRemoved=!0,s.target[v]=null);break}}if(s.allRemoved)return b.call(s.target,s.eventName,s.capture?K:z,s.options)}:function(s){return b.call(s.target,s.eventName,s.invoke,s.options)},he=h?.diff||function(s,l){const v=typeof l;return"function"===v&&s.callback===l||"object"===v&&s.originalDelegate===l},_e=Zone[j("UNPATCHED_EVENTS")],oe=e[j("PASSIVE_EVENTS")],a=function(s,l,v,C,k=!1,Z=!1){return function(){const L=this||e;let I=arguments[0];h&&h.transferEventName&&(I=h.transferEventName(I));let G=arguments[1];if(!G)return s.apply(this,arguments);if(Ce&&"uncaughtException"===I)return s.apply(this,arguments);let B=!1;if("function"!=typeof G){if(!G.handleEvent)return s.apply(this,arguments);B=!0}if(V&&!V(s,G,L,arguments))return;const Te=!!oe&&-1!==oe.indexOf(I),ie=function f(s){if("object"==typeof s&&null!==s){const l={...s};return s.signal&&(l.signal=s.signal),l}return s}(function N(s,l){return l?"boolean"==typeof s?{capture:s,passive:!0}:s?"object"==typeof s&&!1!==s.passive?{...s,passive:!0}:s:{passive:!0}:s}(arguments[2],Te)),ke=ie?.signal;if(ke?.aborted)return;if(_e)for(let ce=0;ce<_e.length;ce++)if(I===_e[ce])return Te?s.call(L,I,G,ie):s.apply(this,arguments);const Be=!!ie&&("boolean"==typeof ie||ie.capture),at=!(!ie||"object"!=typeof ie)&&ie.once,It=Zone.current;let ze=ne[I];ze||(rt(I,U),ze=ne[I]);const lt=ze[Be?ae:le];let Oe,pe=L[lt],ut=!1;if(pe){if(ut=!0,Y)for(let ce=0;ce<pe.length;ce++)if(he(pe[ce],G))return}else pe=L[lt]=[];const ft=L.constructor.name,ht=et[ft];ht&&(Oe=ht[I]),Oe||(Oe=ft+l+(U?U(I):I)),O.options=ie,at&&(O.options.once=!1),O.target=L,O.capture=Be,O.eventName=I,O.isExisting=ut;const Re=H?mt:void 0;Re&&(Re.taskData=O),ke&&(O.options.signal=void 0);const se=It.scheduleEventTask(Oe,G,Re,v,C);if(ke){O.options.signal=ke;const ce=()=>se.zone.cancelTask(se);s.call(ke,"abort",ce,{once:!0}),se.removeAbortListener=()=>ke.removeEventListener("abort",ce)}return O.target=null,Re&&(Re.taskData=null),at&&(O.options.once=!0),"boolean"!=typeof se.options&&(se.options=ie),se.target=L,se.capture=Be,se.eventName=I,B&&(se.originalDelegate=G),Z?pe.unshift(se):pe.push(se),k?L:void 0}};return g[i]=a(w,D,q,A,F),W&&(g[_]=a(W,R,function(s){return W.call(O.target,O.eventName,s.invoke,O.options)},A,F,!0)),g[u]=function(){const s=this||e;let l=arguments[0];h&&h.transferEventName&&(l=h.transferEventName(l));const v=arguments[2],C=!!v&&("boolean"==typeof v||v.capture),k=arguments[1];if(!k)return b.apply(this,arguments);if(V&&!V(b,k,s,arguments))return;const Z=ne[l];let L;Z&&(L=Z[C?ae:le]);const I=L&&s[L];if(I)for(let G=0;G<I.length;G++){const B=I[G];if(he(B,k))return I.splice(G,1),B.isRemoved=!0,0!==I.length||(B.allRemoved=!0,s[L]=null,C||"string"!=typeof l)||(s[ve+"ON_PROPERTY"+l]=null),B.zone.cancelTask(B),F?s:void 0}return b.apply(this,arguments)},g[E]=function(){const s=this||e;let l=arguments[0];h&&h.transferEventName&&(l=h.transferEventName(l));const v=[],C=ot(s,U?U(l):l);for(let k=0;k<C.length;k++){const Z=C[k];v.push(Z.originalDelegate?Z.originalDelegate:Z.callback)}return v},g[T]=function(){const s=this||e;let l=arguments[0];if(l){h&&h.transferEventName&&(l=h.transferEventName(l));const v=ne[l];if(v){const Z=s[v[le]],L=s[v[ae]];if(Z){const I=Z.slice();for(let G=0;G<I.length;G++){const B=I[G];this[u].call(this,l,B.originalDelegate?B.originalDelegate:B.callback,B.options)}}if(L){const I=L.slice();for(let G=0;G<I.length;G++){const B=I[G];this[u].call(this,l,B.originalDelegate?B.originalDelegate:B.callback,B.options)}}}}else{const v=Object.keys(s);for(let C=0;C<v.length;C++){const Z=tt.exec(v[C]);let L=Z&&Z[1];L&&"removeListener"!==L&&this[T].call(this,L)}this[T].call(this,"removeListener")}if(F)return this},fe(g[i],w),fe(g[u],b),ee&&fe(g[T],ee),S&&fe(g[E],S),!0}let X=[];for(let y=0;y<c.length;y++)X[y]=J(c[y],t);return X}function ot(e,r){if(!r){const u=[];for(let E in e){const T=tt.exec(E);let p=T&&T[1];if(p&&(!r||p===r)){const D=e[E];if(D)for(let _=0;_<D.length;_++)u.push(D[_])}}return u}let c=ne[r];c||(rt(r),c=ne[r]);const t=e[c[le]],i=e[c[ae]];return t?i?t.concat(i):t.slice():i?i.slice():[]}function vt(e,r){const c=e.Event;c&&c.prototype&&r.patchMethod(c.prototype,"stopImmediatePropagation",t=>function(i,u){i[nt]=!0,t&&t.apply(i,u)})}const Se=j("zoneTask");function ge(e,r,c,t){let i=null,u=null;c+=t;const E={};function T(D){const _=D.data;_.args[0]=function(){return D.invoke.apply(this,arguments)};const R=i.apply(e,_.args);return Qe(R)?_.handleId=R:(_.handle=R,_.isRefreshable=Je(R.refresh)),D}function p(D){const{handle:_,handleId:R}=D.data;return u.call(e,_??R)}i=ue(e,r+=t,D=>function(_,R){if(Je(R[0])){const M={isRefreshable:!1,isPeriodic:"Interval"===t,delay:"Timeout"===t||"Interval"===t?R[1]||0:void 0,args:R},x=R[0];R[0]=function(){try{return x.apply(this,arguments)}finally{const{handle:H,handleId:V,isPeriodic:Y,isRefreshable:F}=M;!Y&&!F&&(V?delete E[V]:H&&(H[Se]=null))}};const z=He(r,R[0],M,T,p);if(!z)return z;const{handleId:K,handle:J,isRefreshable:X,isPeriodic:y}=z.data;if(K)E[K]=z;else if(J&&(J[Se]=z,X&&!y)){const h=J.refresh;J.refresh=function(){const{zone:H,state:V}=z;return"notScheduled"===V?(z._state="scheduled",H._updateTaskCount(z,1)):"running"===V&&(z._state="scheduling"),h.call(this)}}return J??K??z}return D.apply(e,R)}),u=ue(e,c,D=>function(_,R){const M=R[0];let x;Qe(M)?(x=E[M],delete E[M]):(x=M?.[Se],x?M[Se]=null:x=M),x?.type?x.cancelFn&&x.zone.cancelTask(x):D.apply(e,R)})}function st(e,r,c){if(!c||0===c.length)return r;const t=c.filter(u=>u.target===e);if(0===t.length)return r;const i=t[0].ignoreProperties;return r.filter(u=>-1===i.indexOf(u))}function it(e,r,c,t){e&&$e(e,st(e,r,c),t)}function Ge(e){return Object.getOwnPropertyNames(e).filter(r=>r.startsWith("on")&&r.length>2).map(r=>r.substring(2))}function Nt(e,r,c,t,i){const u=Zone.__symbol__(t);if(r[u])return;const E=r[u]=r[t];r[t]=function(T,p,D){return p&&p.prototype&&i.forEach(function(_){const R=`${c}.${t}::`+_,M=p.prototype;try{if(M.hasOwnProperty(_)){const x=e.ObjectGetOwnPropertyDescriptor(M,_);x&&x.value?(x.value=e.wrapWithCurrentZone(x.value,R),e._redefineProperty(p.prototype,_,x)):M[_]&&(M[_]=e.wrapWithCurrentZone(M[_],R))}else M[_]&&(M[_]=e.wrapWithCurrentZone(M[_],R))}catch{}}),E.call(r,T,p,D)},e.attachOriginToPatched(r[t],E)}const ct=function ye(){const e=globalThis,r=!0===e[Q("forceDuplicateZoneCheck")];if(e.Zone&&(r||"function"!=typeof e.Zone.__symbol__))throw new Error("Zone already loaded.");return e.Zone??=function me(){const e=te.performance;function r(N){e&&e.mark&&e.mark(N)}function c(N,d){e&&e.measure&&e.measure(N,d)}r("Zone");let t=(()=>{class N{static __symbol__=Q;static assertZonePatched(){if(te.Promise!==O.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let n=N.current;for(;n.parent;)n=n.parent;return n}static get current(){return b.zone}static get currentTask(){return S}static __load_patch(n,o,m=!1){if(O.hasOwnProperty(n)){const P=!0===te[Q("forceDuplicateZoneCheck")];if(!m&&P)throw Error("Already loaded patch: "+n)}else if(!te["__Zone_disable_"+n]){const P="Zone:"+n;r(P),O[n]=o(te,N,w),c(P,P)}}get parent(){return this._parent}get name(){return this._name}_parent;_name;_properties;_zoneDelegate;constructor(n,o){this._parent=n,this._name=o?o.name||"unnamed":"<root>",this._properties=o&&o.properties||{},this._zoneDelegate=new u(this,this._parent&&this._parent._zoneDelegate,o)}get(n){const o=this.getZoneWith(n);if(o)return o._properties[n]}getZoneWith(n){let o=this;for(;o;){if(o._properties.hasOwnProperty(n))return o;o=o._parent}return null}fork(n){if(!n)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,n)}wrap(n,o){if("function"!=typeof n)throw new Error("Expecting function got: "+n);const m=this._zoneDelegate.intercept(this,n,o),P=this;return function(){return P.runGuarded(m,this,arguments,o)}}run(n,o,m,P){b={parent:b,zone:this};try{return this._zoneDelegate.invoke(this,n,o,m,P)}finally{b=b.parent}}runGuarded(n,o=null,m,P){b={parent:b,zone:this};try{try{return this._zoneDelegate.invoke(this,n,o,m,P)}catch(q){if(this._zoneDelegate.handleError(this,q))throw q}}finally{b=b.parent}}runTask(n,o,m){if(n.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(n.zone||J).name+"; Execution: "+this.name+")");const P=n,{type:q,data:{isPeriodic:A=!1,isRefreshable:de=!1}={}}=n;if(n.state===X&&(q===U||q===g))return;const he=n.state!=H;he&&P._transitionTo(H,h);const _e=S;S=P,b={parent:b,zone:this};try{q==g&&n.data&&!A&&!de&&(n.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,P,o,m)}catch(oe){if(this._zoneDelegate.handleError(this,oe))throw oe}}finally{const oe=n.state;if(oe!==X&&oe!==Y)if(q==U||A||de&&oe===y)he&&P._transitionTo(h,H,y);else{const f=P._zoneDelegates;this._updateTaskCount(P,-1),he&&P._transitionTo(X,H,X),de&&(P._zoneDelegates=f)}b=b.parent,S=_e}}scheduleTask(n){if(n.zone&&n.zone!==this){let m=this;for(;m;){if(m===n.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${n.zone.name}`);m=m.parent}}n._transitionTo(y,X);const o=[];n._zoneDelegates=o,n._zone=this;try{n=this._zoneDelegate.scheduleTask(this,n)}catch(m){throw n._transitionTo(Y,y,X),this._zoneDelegate.handleError(this,m),m}return n._zoneDelegates===o&&this._updateTaskCount(n,1),n.state==y&&n._transitionTo(h,y),n}scheduleMicroTask(n,o,m,P){return this.scheduleTask(new E(F,n,o,m,P,void 0))}scheduleMacroTask(n,o,m,P,q){return this.scheduleTask(new E(g,n,o,m,P,q))}scheduleEventTask(n,o,m,P,q){return this.scheduleTask(new E(U,n,o,m,P,q))}cancelTask(n){if(n.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(n.zone||J).name+"; Execution: "+this.name+")");if(n.state===h||n.state===H){n._transitionTo(V,h,H);try{this._zoneDelegate.cancelTask(this,n)}catch(o){throw n._transitionTo(Y,V),this._zoneDelegate.handleError(this,o),o}return this._updateTaskCount(n,-1),n._transitionTo(X,V),n.runCount=-1,n}}_updateTaskCount(n,o){const m=n._zoneDelegates;-1==o&&(n._zoneDelegates=null);for(let P=0;P<m.length;P++)m[P]._updateTaskCount(n.type,o)}}return N})();const i={name:"",onHasTask:(N,d,n,o)=>N.hasTask(n,o),onScheduleTask:(N,d,n,o)=>N.scheduleTask(n,o),onInvokeTask:(N,d,n,o,m,P)=>N.invokeTask(n,o,m,P),onCancelTask:(N,d,n,o)=>N.cancelTask(n,o)};class u{get zone(){return this._zone}_zone;_taskCounts={microTask:0,macroTask:0,eventTask:0};_parentDelegate;_forkDlgt;_forkZS;_forkCurrZone;_interceptDlgt;_interceptZS;_interceptCurrZone;_invokeDlgt;_invokeZS;_invokeCurrZone;_handleErrorDlgt;_handleErrorZS;_handleErrorCurrZone;_scheduleTaskDlgt;_scheduleTaskZS;_scheduleTaskCurrZone;_invokeTaskDlgt;_invokeTaskZS;_invokeTaskCurrZone;_cancelTaskDlgt;_cancelTaskZS;_cancelTaskCurrZone;_hasTaskDlgt;_hasTaskDlgtOwner;_hasTaskZS;_hasTaskCurrZone;constructor(d,n,o){this._zone=d,this._parentDelegate=n,this._forkZS=o&&(o&&o.onFork?o:n._forkZS),this._forkDlgt=o&&(o.onFork?n:n._forkDlgt),this._forkCurrZone=o&&(o.onFork?this._zone:n._forkCurrZone),this._interceptZS=o&&(o.onIntercept?o:n._interceptZS),this._interceptDlgt=o&&(o.onIntercept?n:n._interceptDlgt),this._interceptCurrZone=o&&(o.onIntercept?this._zone:n._interceptCurrZone),this._invokeZS=o&&(o.onInvoke?o:n._invokeZS),this._invokeDlgt=o&&(o.onInvoke?n:n._invokeDlgt),this._invokeCurrZone=o&&(o.onInvoke?this._zone:n._invokeCurrZone),this._handleErrorZS=o&&(o.onHandleError?o:n._handleErrorZS),this._handleErrorDlgt=o&&(o.onHandleError?n:n._handleErrorDlgt),this._handleErrorCurrZone=o&&(o.onHandleError?this._zone:n._handleErrorCurrZone),this._scheduleTaskZS=o&&(o.onScheduleTask?o:n._scheduleTaskZS),this._scheduleTaskDlgt=o&&(o.onScheduleTask?n:n._scheduleTaskDlgt),this._scheduleTaskCurrZone=o&&(o.onScheduleTask?this._zone:n._scheduleTaskCurrZone),this._invokeTaskZS=o&&(o.onInvokeTask?o:n._invokeTaskZS),this._invokeTaskDlgt=o&&(o.onInvokeTask?n:n._invokeTaskDlgt),this._invokeTaskCurrZone=o&&(o.onInvokeTask?this._zone:n._invokeTaskCurrZone),this._cancelTaskZS=o&&(o.onCancelTask?o:n._cancelTaskZS),this._cancelTaskDlgt=o&&(o.onCancelTask?n:n._cancelTaskDlgt),this._cancelTaskCurrZone=o&&(o.onCancelTask?this._zone:n._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const m=o&&o.onHasTask;(m||n&&n._hasTaskZS)&&(this._hasTaskZS=m?o:i,this._hasTaskDlgt=n,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,o.onScheduleTask||(this._scheduleTaskZS=i,this._scheduleTaskDlgt=n,this._scheduleTaskCurrZone=this._zone),o.onInvokeTask||(this._invokeTaskZS=i,this._invokeTaskDlgt=n,this._invokeTaskCurrZone=this._zone),o.onCancelTask||(this._cancelTaskZS=i,this._cancelTaskDlgt=n,this._cancelTaskCurrZone=this._zone))}fork(d,n){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,d,n):new t(d,n)}intercept(d,n,o){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,d,n,o):n}invoke(d,n,o,m,P){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,d,n,o,m,P):n.apply(o,m)}handleError(d,n){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,d,n)}scheduleTask(d,n){let o=n;if(this._scheduleTaskZS)this._hasTaskZS&&o._zoneDelegates.push(this._hasTaskDlgtOwner),o=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,d,n),o||(o=n);else if(n.scheduleFn)n.scheduleFn(n);else{if(n.type!=F)throw new Error("Task is missing scheduleFn.");z(n)}return o}invokeTask(d,n,o,m){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,d,n,o,m):n.callback.apply(o,m)}cancelTask(d,n){let o;if(this._cancelTaskZS)o=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,d,n);else{if(!n.cancelFn)throw Error("Task is not cancelable");o=n.cancelFn(n)}return o}hasTask(d,n){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,d,n)}catch(o){this.handleError(d,o)}}_updateTaskCount(d,n){const o=this._taskCounts,m=o[d],P=o[d]=m+n;if(P<0)throw new Error("More tasks executed then were scheduled.");0!=m&&0!=P||this.hasTask(this._zone,{microTask:o.microTask>0,macroTask:o.macroTask>0,eventTask:o.eventTask>0,change:d})}}class E{type;source;invoke;callback;data;scheduleFn;cancelFn;_zone=null;runCount=0;_zoneDelegates=null;_state="notScheduled";constructor(d,n,o,m,P,q){if(this.type=d,this.source=n,this.data=m,this.scheduleFn=P,this.cancelFn=q,!o)throw new Error("callback is not defined");this.callback=o;const A=this;this.invoke=d===U&&m&&m.useG?E.invokeTask:function(){return E.invokeTask.call(te,A,this,arguments)}}static invokeTask(d,n,o){d||(d=this),ee++;try{return d.runCount++,d.zone.runTask(d,n,o)}finally{1==ee&&K(),ee--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(X,y)}_transitionTo(d,n,o){if(this._state!==n&&this._state!==o)throw new Error(`${this.type} '${this.source}': can not transition to '${d}', expecting state '${n}'${o?" or '"+o+"'":""}, was '${this._state}'.`);this._state=d,d==X&&(this._zoneDelegates=null)}toString(){return this.data&&typeof this.data.handleId<"u"?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const T=Q("setTimeout"),p=Q("Promise"),D=Q("then");let M,_=[],R=!1;function x(N){if(M||te[p]&&(M=te[p].resolve(0)),M){let d=M[D];d||(d=M.then),d.call(M,N)}else te[T](N,0)}function z(N){0===ee&&0===_.length&&x(K),N&&_.push(N)}function K(){if(!R){for(R=!0;_.length;){const N=_;_=[];for(let d=0;d<N.length;d++){const n=N[d];try{n.zone.runTask(n,null,null)}catch(o){w.onUnhandledError(o)}}}w.microtaskDrainDone(),R=!1}}const J={name:"NO ZONE"},X="notScheduled",y="scheduling",h="scheduled",H="running",V="canceling",Y="unknown",F="microTask",g="macroTask",U="eventTask",O={},w={symbol:Q,currentZoneFrame:()=>b,onUnhandledError:W,microtaskDrainDone:W,scheduleMicroTask:z,showUncaughtError:()=>!t[Q("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:W,patchMethod:()=>W,bindArguments:()=>[],patchThen:()=>W,patchMacroTask:()=>W,patchEventPrototype:()=>W,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>W,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>W,wrapWithCurrentZone:()=>W,filterProperties:()=>[],attachOriginToPatched:()=>W,_redefineProperty:()=>W,patchCallbacks:()=>W,nativeScheduleMicroTask:x};let b={parent:null,zone:new t(null,null)},S=null,ee=0;function W(){}return c("Zone","Zone"),t}(),e.Zone}();(function Lt(e){(function St(e){e.__load_patch("ZoneAwarePromise",(r,c,t)=>{const i=Object.getOwnPropertyDescriptor,u=Object.defineProperty,T=t.symbol,p=[],D=!1!==r[T("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],_=T("Promise"),R=T("then");t.onUnhandledError=f=>{if(t.showUncaughtError()){const a=f&&f.rejection;a?console.error("Unhandled Promise rejection:",a instanceof Error?a.message:a,"; Zone:",f.zone.name,"; Task:",f.task&&f.task.source,"; Value:",a,a instanceof Error?a.stack:void 0):console.error(f)}},t.microtaskDrainDone=()=>{for(;p.length;){const f=p.shift();try{f.zone.runGuarded(()=>{throw f.throwOriginal?f.rejection:f})}catch(a){z(a)}}};const x=T("unhandledPromiseRejectionHandler");function z(f){t.onUnhandledError(f);try{const a=c[x];"function"==typeof a&&a.call(this,f)}catch{}}function K(f){return f&&"function"==typeof f.then}function J(f){return f}function X(f){return A.reject(f)}const y=T("state"),h=T("value"),H=T("finally"),V=T("parentPromiseValue"),Y=T("parentPromiseState"),g=null,U=!0,O=!1;function b(f,a){return s=>{try{N(f,a,s)}catch(l){N(f,!1,l)}}}const S=function(){let f=!1;return function(s){return function(){f||(f=!0,s.apply(null,arguments))}}},ee="Promise resolved with itself",W=T("currentTaskTrace");function N(f,a,s){const l=S();if(f===s)throw new TypeError(ee);if(f[y]===g){let v=null;try{("object"==typeof s||"function"==typeof s)&&(v=s&&s.then)}catch(C){return l(()=>{N(f,!1,C)})(),f}if(a!==O&&s instanceof A&&s.hasOwnProperty(y)&&s.hasOwnProperty(h)&&s[y]!==g)n(s),N(f,s[y],s[h]);else if(a!==O&&"function"==typeof v)try{v.call(s,l(b(f,a)),l(b(f,!1)))}catch(C){l(()=>{N(f,!1,C)})()}else{f[y]=a;const C=f[h];if(f[h]=s,f[H]===H&&a===U&&(f[y]=f[Y],f[h]=f[V]),a===O&&s instanceof Error){const k=c.currentTask&&c.currentTask.data&&c.currentTask.data.__creationTrace__;k&&u(s,W,{configurable:!0,enumerable:!1,writable:!0,value:k})}for(let k=0;k<C.length;)o(f,C[k++],C[k++],C[k++],C[k++]);if(0==C.length&&a==O){f[y]=0;let k=s;try{throw new Error("Uncaught (in promise): "+function E(f){return f&&f.toString===Object.prototype.toString?(f.constructor&&f.constructor.name||"")+": "+JSON.stringify(f):f?f.toString():Object.prototype.toString.call(f)}(s)+(s&&s.stack?"\n"+s.stack:""))}catch(Z){k=Z}D&&(k.throwOriginal=!0),k.rejection=s,k.promise=f,k.zone=c.current,k.task=c.currentTask,p.push(k),t.scheduleMicroTask()}}}return f}const d=T("rejectionHandledHandler");function n(f){if(0===f[y]){try{const a=c[d];a&&"function"==typeof a&&a.call(this,{rejection:f[h],promise:f})}catch{}f[y]=O;for(let a=0;a<p.length;a++)f===p[a].promise&&p.splice(a,1)}}function o(f,a,s,l,v){n(f);const C=f[y],k=C?"function"==typeof l?l:J:"function"==typeof v?v:X;a.scheduleMicroTask("Promise.then",()=>{try{const Z=f[h],L=!!s&&H===s[H];L&&(s[V]=Z,s[Y]=C);const I=a.run(k,void 0,L&&k!==X&&k!==J?[]:[Z]);N(s,!0,I)}catch(Z){N(s,!1,Z)}},s)}const P=function(){},q=r.AggregateError;class A{static toString(){return"function ZoneAwarePromise() { [native code] }"}static resolve(a){return a instanceof A?a:N(new this(null),U,a)}static reject(a){return N(new this(null),O,a)}static withResolvers(){const a={};return a.promise=new A((s,l)=>{a.resolve=s,a.reject=l}),a}static any(a){if(!a||"function"!=typeof a[Symbol.iterator])return Promise.reject(new q([],"All promises were rejected"));const s=[];let l=0;try{for(let k of a)l++,s.push(A.resolve(k))}catch{return Promise.reject(new q([],"All promises were rejected"))}if(0===l)return Promise.reject(new q([],"All promises were rejected"));let v=!1;const C=[];return new A((k,Z)=>{for(let L=0;L<s.length;L++)s[L].then(I=>{v||(v=!0,k(I))},I=>{C.push(I),l--,0===l&&(v=!0,Z(new q(C,"All promises were rejected")))})})}static race(a){let s,l,v=new this((Z,L)=>{s=Z,l=L});function C(Z){s(Z)}function k(Z){l(Z)}for(let Z of a)K(Z)||(Z=this.resolve(Z)),Z.then(C,k);return v}static all(a){return A.allWithCallback(a)}static allSettled(a){return(this&&this.prototype instanceof A?this:A).allWithCallback(a,{thenCallback:l=>({status:"fulfilled",value:l}),errorCallback:l=>({status:"rejected",reason:l})})}static allWithCallback(a,s){let l,v,C=new this((I,G)=>{l=I,v=G}),k=2,Z=0;const L=[];for(let I of a){K(I)||(I=this.resolve(I));const G=Z;try{I.then(B=>{L[G]=s?s.thenCallback(B):B,k--,0===k&&l(L)},B=>{s?(L[G]=s.errorCallback(B),k--,0===k&&l(L)):v(B)})}catch(B){v(B)}k++,Z++}return k-=2,0===k&&l(L),C}constructor(a){const s=this;if(!(s instanceof A))throw new Error("Must be an instanceof Promise.");s[y]=g,s[h]=[];try{const l=S();a&&a(l(b(s,U)),l(b(s,O)))}catch(l){N(s,!1,l)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return A}then(a,s){let l=this.constructor?.[Symbol.species];(!l||"function"!=typeof l)&&(l=this.constructor||A);const v=new l(P),C=c.current;return this[y]==g?this[h].push(C,v,a,s):o(this,C,v,a,s),v}catch(a){return this.then(null,a)}finally(a){let s=this.constructor?.[Symbol.species];(!s||"function"!=typeof s)&&(s=A);const l=new s(P);l[H]=H;const v=c.current;return this[y]==g?this[h].push(v,l,a,a):o(this,v,l,a,a),l}}A.resolve=A.resolve,A.reject=A.reject,A.race=A.race,A.all=A.all;const de=r[_]=r.Promise;r.Promise=A;const he=T("thenPatched");function _e(f){const a=f.prototype,s=i(a,"then");if(s&&(!1===s.writable||!s.configurable))return;const l=a.then;a[R]=l,f.prototype.then=function(v,C){return new A((Z,L)=>{l.call(this,Z,L)}).then(v,C)},f[he]=!0}return t.patchThen=_e,de&&(_e(de),ue(r,"fetch",f=>function oe(f){return function(a,s){let l=f.apply(a,s);if(l instanceof A)return l;let v=l.constructor;return v[he]||_e(v),l}}(f))),Promise[c.__symbol__("uncaughtPromiseErrors")]=p,A})})(e),function Ot(e){e.__load_patch("toString",r=>{const c=Function.prototype.toString,t=j("OriginalDelegate"),i=j("Promise"),u=j("Error"),E=function(){if("function"==typeof this){const _=this[t];if(_)return"function"==typeof _?c.call(_):Object.prototype.toString.call(_);if(this===Promise){const R=r[i];if(R)return c.call(R)}if(this===Error){const R=r[u];if(R)return c.call(R)}}return c.call(this)};E[t]=c,Function.prototype.toString=E;const T=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":T.call(this)}})}(e),function Zt(e){e.__load_patch("util",(r,c,t)=>{const i=Ge(r);t.patchOnProperties=$e,t.patchMethod=ue,t.bindArguments=xe,t.patchMacroTask=kt;const u=c.__symbol__("BLACK_LISTED_EVENTS"),E=c.__symbol__("UNPATCHED_EVENTS");r[E]&&(r[u]=r[E]),r[u]&&(c[u]=c[E]=r[u]),t.patchEventPrototype=vt,t.patchEventTarget=yt,t.isIEOrEdge=pt,t.ObjectDefineProperty=Ne,t.ObjectGetOwnPropertyDescriptor=Ee,t.ObjectCreate=_t,t.ArraySlice=dt,t.patchClass=Pe,t.wrapWithCurrentZone=je,t.filterProperties=st,t.attachOriginToPatched=fe,t._redefineProperty=Object.defineProperty,t.patchCallbacks=Nt,t.getGlobalObjects=()=>({globalSources:et,zoneSymbolEventNames:ne,eventNames:i,isBrowser:Ve,isMix:qe,isNode:Ce,TRUE_STR:ae,FALSE_STR:le,ZONE_SYMBOL_PREFIX:ve,ADD_EVENT_LISTENER_STR:Le,REMOVE_EVENT_LISTENER_STR:Ie})})}(e)})(ct),function Dt(e){e.__load_patch("legacy",r=>{const c=r[e.__symbol__("legacyPatch")];c&&c()}),e.__load_patch("timers",r=>{const c="set",t="clear";ge(r,c,t,"Timeout"),ge(r,c,t,"Interval"),ge(r,c,t,"Immediate")}),e.__load_patch("requestAnimationFrame",r=>{ge(r,"request","cancel","AnimationFrame"),ge(r,"mozRequest","mozCancel","AnimationFrame"),ge(r,"webkitRequest","webkitCancel","AnimationFrame")}),e.__load_patch("blocking",(r,c)=>{const t=["alert","prompt","confirm"];for(let i=0;i<t.length;i++)ue(r,t[i],(E,T,p)=>function(D,_){return c.current.run(E,r,_,p)})}),e.__load_patch("EventTarget",(r,c,t)=>{(function wt(e,r){r.patchEventPrototype(e,r)})(r,t),function Rt(e,r){if(Zone[r.symbol("patchEventTarget")])return;const{eventNames:c,zoneSymbolEventNames:t,TRUE_STR:i,FALSE_STR:u,ZONE_SYMBOL_PREFIX:E}=r.getGlobalObjects();for(let p=0;p<c.length;p++){const D=c[p],M=E+(D+u),x=E+(D+i);t[D]={},t[D][u]=M,t[D][i]=x}const T=e.EventTarget;T&&T.prototype&&r.patchEventTarget(e,r,[T&&T.prototype])}(r,t);const i=r.XMLHttpRequestEventTarget;i&&i.prototype&&t.patchEventTarget(r,t,[i.prototype])}),e.__load_patch("MutationObserver",(r,c,t)=>{Pe("MutationObserver"),Pe("WebKitMutationObserver")}),e.__load_patch("IntersectionObserver",(r,c,t)=>{Pe("IntersectionObserver")}),e.__load_patch("FileReader",(r,c,t)=>{Pe("FileReader")}),e.__load_patch("on_property",(r,c,t)=>{!function Ct(e,r){if(Ce&&!qe||Zone[e.symbol("patchEvents")])return;const c=r.__Zone_ignore_on_properties;let t=[];if(Ve){const i=window;t=t.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);const u=[];it(i,Ge(i),c&&c.concat(u),Ze(i))}t=t.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(let i=0;i<t.length;i++){const u=r[t[i]];u?.prototype&&it(u.prototype,Ge(u.prototype),c)}}(t,r)}),e.__load_patch("customElements",(r,c,t)=>{!function Pt(e,r){const{isBrowser:c,isMix:t}=r.getGlobalObjects();(c||t)&&e.customElements&&"customElements"in e&&r.patchCallbacks(r,e.customElements,"customElements","define",["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback","formAssociatedCallback","formDisabledCallback","formResetCallback","formStateRestoreCallback"])}(r,t)}),e.__load_patch("XHR",(r,c)=>{!function D(_){const R=_.XMLHttpRequest;if(!R)return;const M=R.prototype;let z=M[Me],K=M[Ae];if(!z){const w=_.XMLHttpRequestEventTarget;if(w){const b=w.prototype;z=b[Me],K=b[Ae]}}const J="readystatechange",X="scheduled";function y(w){const b=w.data,S=b.target;S[E]=!1,S[p]=!1;const ee=S[u];z||(z=S[Me],K=S[Ae]),ee&&K.call(S,J,ee);const W=S[u]=()=>{if(S.readyState===S.DONE)if(!b.aborted&&S[E]&&w.state===X){const d=S[c.__symbol__("loadfalse")];if(0!==S.status&&d&&d.length>0){const n=w.invoke;w.invoke=function(){const o=S[c.__symbol__("loadfalse")];for(let m=0;m<o.length;m++)o[m]===w&&o.splice(m,1);!b.aborted&&w.state===X&&n.call(w)},d.push(w)}else w.invoke()}else!b.aborted&&!1===S[E]&&(S[p]=!0)};return z.call(S,J,W),S[t]||(S[t]=w),U.apply(S,b.args),S[E]=!0,w}function h(){}function H(w){const b=w.data;return b.aborted=!0,O.apply(b.target,b.args)}const V=ue(M,"open",()=>function(w,b){return w[i]=0==b[2],w[T]=b[1],V.apply(w,b)}),F=j("fetchTaskAborting"),g=j("fetchTaskScheduling"),U=ue(M,"send",()=>function(w,b){if(!0===c.current[g]||w[i])return U.apply(w,b);{const S={target:w,url:w[T],isPeriodic:!1,args:b,aborted:!1},ee=He("XMLHttpRequest.send",h,S,y,H);w&&!0===w[p]&&!S.aborted&&ee.state===X&&ee.invoke()}}),O=ue(M,"abort",()=>function(w,b){const S=function x(w){return w[t]}(w);if(S&&"string"==typeof S.type){if(null==S.cancelFn||S.data&&S.data.aborted)return;S.zone.cancelTask(S)}else if(!0===c.current[F])return O.apply(w,b)})}(r);const t=j("xhrTask"),i=j("xhrSync"),u=j("xhrListener"),E=j("xhrScheduled"),T=j("xhrURL"),p=j("xhrErrorBeforeScheduled")}),e.__load_patch("geolocation",r=>{r.navigator&&r.navigator.geolocation&&function Et(e,r){const c=e.constructor.name;for(let t=0;t<r.length;t++){const i=r[t],u=e[i];if(u){if(!Ue(Ee(e,i)))continue;e[i]=(T=>{const p=function(){return T.apply(this,xe(arguments,c+"."+i))};return fe(p,T),p})(u)}}}(r.navigator.geolocation,["getCurrentPosition","watchPosition"])}),e.__load_patch("PromiseRejectionEvent",(r,c)=>{function t(i){return function(u){ot(r,i).forEach(T=>{const p=r.PromiseRejectionEvent;if(p){const D=new p(i,{promise:u.promise,reason:u.rejection});T.invoke(D)}})}}r.PromiseRejectionEvent&&(c[j("unhandledPromiseRejectionHandler")]=t("unhandledrejection"),c[j("rejectionHandledHandler")]=t("rejectionhandled"))}),e.__load_patch("queueMicrotask",(r,c,t)=>{!function bt(e,r){r.patchMethod(e,"queueMicrotask",c=>function(t,i){Zone.current.scheduleMicroTask("queueMicrotask",i[0])})}(r,t)})}(ct)}},te=>{te(te.s=4050)}]);