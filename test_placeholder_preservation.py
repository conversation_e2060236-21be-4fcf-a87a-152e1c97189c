#!/usr/bin/env python3
"""
Test that placeholder data is correctly recognized as preserved data
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3002/api"

def test_placeholder_preservation():
    """Test that placeholders are recognized as valid preserved data"""
    email = f"test.placeholder.{int(time.time())}@example.com"
    password = "TestPassword123!"
    
    print("🧪 Testing Placeholder Preservation")
    print("=" * 60)
    
    # Step 1: Register user
    print("📝 Step 1: Registering user...")
    register_data = {
        "email": email,
        "password": password,
        "confirmPassword": password,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+1234567890"
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=register_data)
    if response.status_code not in [200, 201]:
        print(f"❌ Registration failed: {response.text}")
        return False
    
    user_data = response.json()
    user_id = user_data.get('userId') or user_data.get('user', {}).get('id')
    token = user_data.get('token')
    print(f"✅ User registered with ID: {user_id}")
    
    # Step 2: Request deletion with payment and transaction preservation
    print("\n📝 Step 2: Requesting deletion with payment and transaction preservation...")
    deletion_data = {
        "preservePaymentData": True,       # ✅ Preserve (will create placeholder)
        "preserveTransactionHistory": True, # ✅ Preserve (will create placeholder)
        "preserveProfileData": False,      # ❌ Don't preserve
        "preserveSecurityLogs": False,     # ❌ Don't preserve
        "customRetentionPeriod": 30,
        "reason": "Testing placeholder preservation"
    }
    
    response = requests.post(f"{BASE_URL}/account/request-deletion", 
                           json=deletion_data,
                           headers={'Authorization': f'Bearer {token}'})
    
    if response.status_code != 200:
        print(f"❌ Deletion request failed: {response.text}")
        return False
    
    deletion_response = response.json()
    deletion_id = deletion_response['deletionId']
    confirmation_token = deletion_response['confirmationToken']
    print(f"✅ Deletion requested with ID: {deletion_id}")
    
    # Step 3: Confirm deletion
    print("\n📝 Step 3: Confirming deletion...")
    response = requests.post(f"{BASE_URL}/account/confirm-deletion", 
                           json={'token': confirmation_token})
    
    if response.status_code != 200:
        print(f"❌ Deletion confirmation failed: {response.text}")
        return False
    
    confirm_response = response.json()
    print("✅ Deletion confirmed")
    preserved_summary_from_deletion = confirm_response.get('preservedDataSummary', {})
    print(f"📄 Preserved data summary from deletion: {preserved_summary_from_deletion}")
    
    # Step 4: Check preserved data
    print("\n📝 Step 4: Checking preserved data...")
    response = requests.post(f"{BASE_URL}/account/check-preserved-data", 
                           json={'email': email})
    
    if response.status_code != 200:
        print(f"❌ Check preserved data failed: {response.text}")
        return False
    
    check_response = response.json()
    has_preserved = check_response.get('hasPreservedData', False)
    preserved_summary = check_response.get('preservedDataSummary', {})
    
    print(f"📊 Has preserved data: {has_preserved}")
    print(f"📊 Preserved data summary: {preserved_summary}")
    
    # Step 5: Validate expectations
    print("\n📝 Step 5: Validating expectations...")
    
    if not has_preserved:
        print("❌ FAIL: Expected preserved data (payment and transaction placeholders) but found none")
        return False
    
    # Should have payment and transaction data (even if placeholders)
    expected_categories = ['payments', 'transactions']
    unexpected_categories = ['profile', 'security']
    
    # Check that payment and transaction are preserved
    for category in expected_categories:
        if category not in preserved_summary:
            print(f"❌ FAIL: Expected {category} to be preserved but it's missing")
            return False
        print(f"✅ PASS: {category} is correctly preserved (placeholder data)")
    
    # Check that other categories are NOT preserved
    for category in unexpected_categories:
        if category in preserved_summary:
            print(f"❌ FAIL: {category} should NOT be preserved but it appears in summary")
            return False
        print(f"✅ PASS: {category} is correctly NOT preserved")
    
    # Validate summary structure
    if len(preserved_summary) != 2:
        print(f"❌ FAIL: Expected exactly 2 preserved categories but found {len(preserved_summary)}")
        print(f"   Categories found: {list(preserved_summary.keys())}")
        return False
    
    print("✅ PASS: Payment and transaction data are preserved (with placeholders)")
    print("✅ PASS: Profile and security data are correctly NOT preserved")
    
    # Step 6: Test signup - should restore user with preserved data
    print("\n📝 Step 6: Testing signup - should restore user with preserved data...")
    signup_data = {
        "email": email,
        "password": password,
        "confirmPassword": password,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+1234567890"
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=signup_data)
    
    if response.status_code not in [200, 201]:
        print(f"❌ Second registration failed: {response.text}")
        return False
    
    signup_response = response.json()
    signup_user_id = signup_response.get('userId') or signup_response.get('user', {}).get('id')
    signup_has_preserved = signup_response.get('hasPreservedData', False)
    signup_preserved_summary = signup_response.get('preservedDataSummary', {})
    message = signup_response.get('message', '')
    
    print(f"📊 Signup response:")
    print(f"   - User ID: {signup_user_id}")
    print(f"   - Original User ID: {user_id}")
    print(f"   - Has preserved data: {signup_has_preserved}")
    print(f"   - Preserved summary: {signup_preserved_summary}")
    print(f"   - Message: {message}")
    
    # Should restore the same user (not create new one)
    if str(signup_user_id) != str(user_id):
        print(f"❌ FAIL: Different user ID returned - should restore same user")
        return False
    
    # Should have preserved data
    if not signup_has_preserved:
        print(f"❌ FAIL: Should have preserved data available")
        return False
    
    # Should match the preserved data summary
    if signup_preserved_summary != preserved_summary:
        print(f"❌ FAIL: Preserved data summary doesn't match")
        print(f"   Expected: {preserved_summary}")
        print(f"   Got:      {signup_preserved_summary}")
        return False
    
    # Message should indicate restoration
    if "restored" not in message.lower() and "reactivated" not in message.lower():
        print(f"❌ FAIL: Message should indicate account restoration")
        return False
    
    print("✅ PASS: User account correctly restored with preserved data")
    print("✅ PASS: Placeholder data correctly recognized as preserved data")
    print("✅ PASS: All validations successful!")
    
    return True

def main():
    """Main test function"""
    print("🚀 Starting Placeholder Preservation Test")
    print("Testing that placeholder data is correctly recognized as preserved data")
    print("=" * 80)
    
    success = test_placeholder_preservation()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 PLACEHOLDER PRESERVATION TEST PASSED!")
        print("✅ System correctly recognizes placeholders as valid preserved data")
    else:
        print("❌ PLACEHOLDER PRESERVATION TEST FAILED!")
        print("⚠️ System is not recognizing placeholders as preserved data")
    print("🏁 Test completed!")

if __name__ == "__main__":
    main()
