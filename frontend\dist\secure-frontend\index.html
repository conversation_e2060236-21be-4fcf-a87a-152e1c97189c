<!doctype html>
<html lang="en" data-beasties-container>
<head>
  <meta charset="utf-8">
  <title>SecureApp - Secure Authentication & Payments</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <link rel="preconnect" href="https://fonts.gstatic.com">
  <style>@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBGEe.woff2) format('woff2');unicode-range:U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBGEe.woff2) format('woff2');unicode-range:U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBGEe.woff2) format('woff2');unicode-range:U+1F00-1FFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBGEe.woff2) format('woff2');unicode-range:U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBGEe.woff2) format('woff2');unicode-range:U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBGEe.woff2) format('woff2');unicode-range:U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBGEe.woff2) format('woff2');unicode-range:U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBGEe.woff2) format('woff2');unicode-range:U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBA.woff2) format('woff2');unicode-range:U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBGEe.woff2) format('woff2');unicode-range:U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBGEe.woff2) format('woff2');unicode-range:U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBGEe.woff2) format('woff2');unicode-range:U+1F00-1FFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBGEe.woff2) format('woff2');unicode-range:U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBGEe.woff2) format('woff2');unicode-range:U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBGEe.woff2) format('woff2');unicode-range:U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBGEe.woff2) format('woff2');unicode-range:U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBGEe.woff2) format('woff2');unicode-range:U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBA.woff2) format('woff2');unicode-range:U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBGEe.woff2) format('woff2');unicode-range:U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBGEe.woff2) format('woff2');unicode-range:U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBGEe.woff2) format('woff2');unicode-range:U+1F00-1FFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBGEe.woff2) format('woff2');unicode-range:U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBGEe.woff2) format('woff2');unicode-range:U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBGEe.woff2) format('woff2');unicode-range:U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBGEe.woff2) format('woff2');unicode-range:U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBGEe.woff2) format('woff2');unicode-range:U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBA.woff2) format('woff2');unicode-range:U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;}</style>
  <style>@font-face{font-family:'Material Icons';font-style:normal;font-weight:400;src:url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');}.material-icons{font-family:'Material Icons';font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-feature-settings:'liga';-webkit-font-smoothing:antialiased;}</style>
  
  <!-- Security Headers -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' checkout.razorpay.com cdn.razorpay.com; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data: https: checkout.razorpay.com cdn.razorpay.com; connect-src 'self' http://localhost:3002 https://api.razorpay.com https://lumberjack.razorpay.com https://checkout.razorpay.com; frame-src 'self' checkout.razorpay.com api.razorpay.com; child-src 'self' checkout.razorpay.com api.razorpay.com;">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="X-Frame-Options" content="DENY">
  <meta http-equiv="X-XSS-Protection" content="1; mode=block">
  <meta name="referrer" content="strict-origin-when-cross-origin">
<style>html{--mat-sys-on-surface:initial}html{--mat-ripple-color:rgba(0, 0, 0, .1)}html{--mat-option-selected-state-label-text-color:#3f51b5;--mat-option-label-text-color:rgba(0, 0, 0, .87);--mat-option-hover-state-layer-color:rgba(0, 0, 0, .04);--mat-option-focus-state-layer-color:rgba(0, 0, 0, .04);--mat-option-selected-state-layer-color:rgba(0, 0, 0, .04)}html{--mat-optgroup-label-text-color:rgba(0, 0, 0, .87)}html{--mat-pseudo-checkbox-full-selected-icon-color:#ff4081;--mat-pseudo-checkbox-full-selected-checkmark-color:#fafafa;--mat-pseudo-checkbox-full-unselected-icon-color:rgba(0, 0, 0, .54);--mat-pseudo-checkbox-full-disabled-selected-checkmark-color:#fafafa;--mat-pseudo-checkbox-full-disabled-unselected-icon-color:#b0b0b0;--mat-pseudo-checkbox-full-disabled-selected-icon-color:#b0b0b0;--mat-pseudo-checkbox-minimal-selected-checkmark-color:#ff4081;--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color:#b0b0b0}html{--mat-app-background-color:#fafafa;--mat-app-text-color:rgba(0, 0, 0, .87);--mat-app-elevation-shadow-level-0:0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-1:0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-2:0px 3px 1px -2px rgba(0, 0, 0, .2), 0px 2px 2px 0px rgba(0, 0, 0, .14), 0px 1px 5px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-3:0px 3px 3px -2px rgba(0, 0, 0, .2), 0px 3px 4px 0px rgba(0, 0, 0, .14), 0px 1px 8px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-4:0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-5:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 5px 8px 0px rgba(0, 0, 0, .14), 0px 1px 14px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-6:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-7:0px 4px 5px -2px rgba(0, 0, 0, .2), 0px 7px 10px 1px rgba(0, 0, 0, .14), 0px 2px 16px 1px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-8:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-9:0px 5px 6px -3px rgba(0, 0, 0, .2), 0px 9px 12px 1px rgba(0, 0, 0, .14), 0px 3px 16px 2px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-10:0px 6px 6px -3px rgba(0, 0, 0, .2), 0px 10px 14px 1px rgba(0, 0, 0, .14), 0px 4px 18px 3px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-11:0px 6px 7px -4px rgba(0, 0, 0, .2), 0px 11px 15px 1px rgba(0, 0, 0, .14), 0px 4px 20px 3px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-12:0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-13:0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 13px 19px 2px rgba(0, 0, 0, .14), 0px 5px 24px 4px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-14:0px 7px 9px -4px rgba(0, 0, 0, .2), 0px 14px 21px 2px rgba(0, 0, 0, .14), 0px 5px 26px 4px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-15:0px 8px 9px -5px rgba(0, 0, 0, .2), 0px 15px 22px 2px rgba(0, 0, 0, .14), 0px 6px 28px 5px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-16:0px 8px 10px -5px rgba(0, 0, 0, .2), 0px 16px 24px 2px rgba(0, 0, 0, .14), 0px 6px 30px 5px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-17:0px 8px 11px -5px rgba(0, 0, 0, .2), 0px 17px 26px 2px rgba(0, 0, 0, .14), 0px 6px 32px 5px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-18:0px 9px 11px -5px rgba(0, 0, 0, .2), 0px 18px 28px 2px rgba(0, 0, 0, .14), 0px 7px 34px 6px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-19:0px 9px 12px -6px rgba(0, 0, 0, .2), 0px 19px 29px 2px rgba(0, 0, 0, .14), 0px 7px 36px 6px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-20:0px 10px 13px -6px rgba(0, 0, 0, .2), 0px 20px 31px 3px rgba(0, 0, 0, .14), 0px 8px 38px 7px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-21:0px 10px 13px -6px rgba(0, 0, 0, .2), 0px 21px 33px 3px rgba(0, 0, 0, .14), 0px 8px 40px 7px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-22:0px 10px 14px -6px rgba(0, 0, 0, .2), 0px 22px 35px 3px rgba(0, 0, 0, .14), 0px 8px 42px 7px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-23:0px 11px 14px -7px rgba(0, 0, 0, .2), 0px 23px 36px 3px rgba(0, 0, 0, .14), 0px 9px 44px 8px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-24:0px 11px 15px -7px rgba(0, 0, 0, .2), 0px 24px 38px 3px rgba(0, 0, 0, .14), 0px 9px 46px 8px rgba(0, 0, 0, .12)}html{--mat-option-label-text-font:Roboto, sans-serif;--mat-option-label-text-line-height:24px;--mat-option-label-text-size:16px;--mat-option-label-text-tracking:.03125em;--mat-option-label-text-weight:400}html{--mat-optgroup-label-text-font:Roboto, sans-serif;--mat-optgroup-label-text-line-height:24px;--mat-optgroup-label-text-size:16px;--mat-optgroup-label-text-tracking:.03125em;--mat-optgroup-label-text-weight:400}html{--mat-card-elevated-container-shape:4px;--mat-card-outlined-container-shape:4px;--mat-card-filled-container-shape:4px;--mat-card-outlined-outline-width:1px}html{--mat-card-elevated-container-color:white;--mat-card-elevated-container-elevation:0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mat-card-outlined-container-color:white;--mat-card-outlined-container-elevation:0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mat-card-outlined-outline-color:rgba(0, 0, 0, .12);--mat-card-subtitle-text-color:rgba(0, 0, 0, .54);--mat-card-filled-container-color:white;--mat-card-filled-container-elevation:0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12)}html{--mat-card-title-text-font:Roboto, sans-serif;--mat-card-title-text-line-height:32px;--mat-card-title-text-size:20px;--mat-card-title-text-tracking:.0125em;--mat-card-title-text-weight:500;--mat-card-subtitle-text-font:Roboto, sans-serif;--mat-card-subtitle-text-line-height:22px;--mat-card-subtitle-text-size:14px;--mat-card-subtitle-text-tracking:.0071428571em;--mat-card-subtitle-text-weight:500}html{--mat-progress-bar-active-indicator-height:4px;--mat-progress-bar-track-height:4px;--mat-progress-bar-track-shape:0}html{--mat-tooltip-container-shape:4px;--mat-tooltip-supporting-text-line-height:16px}html{--mat-tooltip-container-color:#616161;--mat-tooltip-supporting-text-color:#fff}html{--mat-tooltip-supporting-text-font:Roboto, sans-serif;--mat-tooltip-supporting-text-size:12px;--mat-tooltip-supporting-text-weight:400;--mat-tooltip-supporting-text-tracking:.0333333333em}html{--mat-form-field-filled-active-indicator-height:1px;--mat-form-field-filled-focus-active-indicator-height:2px;--mat-form-field-filled-container-shape:4px;--mat-form-field-outlined-outline-width:1px;--mat-form-field-outlined-focus-outline-width:2px;--mat-form-field-outlined-container-shape:4px}html{--mat-form-field-focus-select-arrow-color:rgba(63, 81, 181, .87);--mat-form-field-filled-caret-color:#3f51b5;--mat-form-field-filled-focus-active-indicator-color:#3f51b5;--mat-form-field-filled-focus-label-text-color:rgba(63, 81, 181, .87);--mat-form-field-outlined-caret-color:#3f51b5;--mat-form-field-outlined-focus-outline-color:#3f51b5;--mat-form-field-outlined-focus-label-text-color:rgba(63, 81, 181, .87);--mat-form-field-disabled-input-text-placeholder-color:rgba(0, 0, 0, .38);--mat-form-field-state-layer-color:rgba(0, 0, 0, .87);--mat-form-field-error-text-color:#f44336;--mat-form-field-select-option-text-color:inherit;--mat-form-field-select-disabled-option-text-color:GrayText;--mat-form-field-leading-icon-color:unset;--mat-form-field-disabled-leading-icon-color:unset;--mat-form-field-trailing-icon-color:unset;--mat-form-field-disabled-trailing-icon-color:unset;--mat-form-field-error-focus-trailing-icon-color:unset;--mat-form-field-error-hover-trailing-icon-color:unset;--mat-form-field-error-trailing-icon-color:unset;--mat-form-field-enabled-select-arrow-color:rgba(0, 0, 0, .54);--mat-form-field-disabled-select-arrow-color:rgba(0, 0, 0, .38);--mat-form-field-hover-state-layer-opacity:.04;--mat-form-field-focus-state-layer-opacity:.08;--mat-form-field-filled-container-color:rgb(244.8, 244.8, 244.8);--mat-form-field-filled-disabled-container-color:rgb(249.9, 249.9, 249.9);--mat-form-field-filled-label-text-color:rgba(0, 0, 0, .6);--mat-form-field-filled-hover-label-text-color:rgba(0, 0, 0, .6);--mat-form-field-filled-disabled-label-text-color:rgba(0, 0, 0, .38);--mat-form-field-filled-input-text-color:rgba(0, 0, 0, .87);--mat-form-field-filled-disabled-input-text-color:rgba(0, 0, 0, .38);--mat-form-field-filled-input-text-placeholder-color:rgba(0, 0, 0, .6);--mat-form-field-filled-error-hover-label-text-color:#f44336;--mat-form-field-filled-error-focus-label-text-color:#f44336;--mat-form-field-filled-error-label-text-color:#f44336;--mat-form-field-filled-error-caret-color:#f44336;--mat-form-field-filled-active-indicator-color:rgba(0, 0, 0, .42);--mat-form-field-filled-disabled-active-indicator-color:rgba(0, 0, 0, .06);--mat-form-field-filled-hover-active-indicator-color:rgba(0, 0, 0, .87);--mat-form-field-filled-error-active-indicator-color:#f44336;--mat-form-field-filled-error-focus-active-indicator-color:#f44336;--mat-form-field-filled-error-hover-active-indicator-color:#f44336;--mat-form-field-outlined-label-text-color:rgba(0, 0, 0, .6);--mat-form-field-outlined-hover-label-text-color:rgba(0, 0, 0, .6);--mat-form-field-outlined-disabled-label-text-color:rgba(0, 0, 0, .38);--mat-form-field-outlined-input-text-color:rgba(0, 0, 0, .87);--mat-form-field-outlined-disabled-input-text-color:rgba(0, 0, 0, .38);--mat-form-field-outlined-input-text-placeholder-color:rgba(0, 0, 0, .6);--mat-form-field-outlined-error-caret-color:#f44336;--mat-form-field-outlined-error-focus-label-text-color:#f44336;--mat-form-field-outlined-error-label-text-color:#f44336;--mat-form-field-outlined-error-hover-label-text-color:#f44336;--mat-form-field-outlined-outline-color:rgba(0, 0, 0, .38);--mat-form-field-outlined-disabled-outline-color:rgba(0, 0, 0, .06);--mat-form-field-outlined-hover-outline-color:rgba(0, 0, 0, .87);--mat-form-field-outlined-error-focus-outline-color:#f44336;--mat-form-field-outlined-error-hover-outline-color:#f44336;--mat-form-field-outlined-error-outline-color:#f44336}html{--mat-form-field-container-height:56px;--mat-form-field-filled-label-display:block;--mat-form-field-container-vertical-padding:16px;--mat-form-field-filled-with-label-container-padding-top:24px;--mat-form-field-filled-with-label-container-padding-bottom:8px}html{--mat-form-field-container-text-font:Roboto, sans-serif;--mat-form-field-container-text-line-height:24px;--mat-form-field-container-text-size:16px;--mat-form-field-container-text-tracking:.03125em;--mat-form-field-container-text-weight:400;--mat-form-field-outlined-label-text-populated-size:16px;--mat-form-field-subscript-text-font:Roboto, sans-serif;--mat-form-field-subscript-text-line-height:20px;--mat-form-field-subscript-text-size:12px;--mat-form-field-subscript-text-tracking:.0333333333em;--mat-form-field-subscript-text-weight:400;--mat-form-field-filled-label-text-font:Roboto, sans-serif;--mat-form-field-filled-label-text-size:16px;--mat-form-field-filled-label-text-tracking:.03125em;--mat-form-field-filled-label-text-weight:400;--mat-form-field-outlined-label-text-font:Roboto, sans-serif;--mat-form-field-outlined-label-text-size:16px;--mat-form-field-outlined-label-text-tracking:.03125em;--mat-form-field-outlined-label-text-weight:400}html{--mat-select-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12)}html{--mat-select-panel-background-color:white;--mat-select-enabled-trigger-text-color:rgba(0, 0, 0, .87);--mat-select-disabled-trigger-text-color:rgba(0, 0, 0, .38);--mat-select-placeholder-text-color:rgba(0, 0, 0, .6);--mat-select-enabled-arrow-color:rgba(0, 0, 0, .54);--mat-select-disabled-arrow-color:rgba(0, 0, 0, .38);--mat-select-focused-arrow-color:rgba(63, 81, 181, .87);--mat-select-invalid-arrow-color:rgba(244, 67, 54, .87)}html{--mat-select-arrow-transform:translateY(-8px)}html{--mat-select-trigger-text-font:Roboto, sans-serif;--mat-select-trigger-text-line-height:24px;--mat-select-trigger-text-size:16px;--mat-select-trigger-text-tracking:.03125em;--mat-select-trigger-text-weight:400}html{--mat-autocomplete-container-shape:4px;--mat-autocomplete-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12)}html{--mat-autocomplete-background-color:white}html{--mat-dialog-container-shape:4px;--mat-dialog-container-elevation-shadow:0px 11px 15px -7px rgba(0, 0, 0, .2), 0px 24px 38px 3px rgba(0, 0, 0, .14), 0px 9px 46px 8px rgba(0, 0, 0, .12);--mat-dialog-container-max-width:80vw;--mat-dialog-container-small-max-width:80vw;--mat-dialog-container-min-width:0;--mat-dialog-actions-alignment:start;--mat-dialog-actions-padding:8px;--mat-dialog-content-padding:20px 24px;--mat-dialog-with-actions-content-padding:20px 24px;--mat-dialog-headline-padding:0 24px 9px}html{--mat-dialog-container-color:white;--mat-dialog-subhead-color:rgba(0, 0, 0, .87);--mat-dialog-supporting-text-color:rgba(0, 0, 0, .6)}html{--mat-dialog-subhead-font:Roboto, sans-serif;--mat-dialog-subhead-line-height:32px;--mat-dialog-subhead-size:20px;--mat-dialog-subhead-weight:500;--mat-dialog-subhead-tracking:.0125em;--mat-dialog-supporting-text-font:Roboto, sans-serif;--mat-dialog-supporting-text-line-height:24px;--mat-dialog-supporting-text-size:16px;--mat-dialog-supporting-text-weight:400;--mat-dialog-supporting-text-tracking:.03125em}html{--mat-slide-toggle-disabled-selected-handle-opacity:.38;--mat-slide-toggle-disabled-selected-icon-opacity:.38;--mat-slide-toggle-disabled-track-opacity:.12;--mat-slide-toggle-disabled-unselected-handle-opacity:.38;--mat-slide-toggle-disabled-unselected-icon-opacity:.38;--mat-slide-toggle-disabled-unselected-track-outline-color:transparent;--mat-slide-toggle-disabled-unselected-track-outline-width:1px;--mat-slide-toggle-handle-height:20px;--mat-slide-toggle-handle-shape:10px;--mat-slide-toggle-handle-width:20px;--mat-slide-toggle-hidden-track-opacity:1;--mat-slide-toggle-hidden-track-transition:transform 75ms 0ms cubic-bezier(.4, 0, .6, 1);--mat-slide-toggle-pressed-handle-size:20px;--mat-slide-toggle-selected-focus-state-layer-opacity:.12;--mat-slide-toggle-selected-handle-horizontal-margin:0;--mat-slide-toggle-selected-handle-size:20px;--mat-slide-toggle-selected-hover-state-layer-opacity:.04;--mat-slide-toggle-selected-icon-size:18px;--mat-slide-toggle-selected-pressed-handle-horizontal-margin:0;--mat-slide-toggle-selected-pressed-state-layer-opacity:.1;--mat-slide-toggle-selected-track-outline-color:transparent;--mat-slide-toggle-selected-track-outline-width:1px;--mat-slide-toggle-selected-with-icon-handle-horizontal-margin:0;--mat-slide-toggle-track-height:14px;--mat-slide-toggle-track-outline-color:transparent;--mat-slide-toggle-track-outline-width:1px;--mat-slide-toggle-track-shape:7px;--mat-slide-toggle-track-width:36px;--mat-slide-toggle-unselected-focus-state-layer-opacity:.12;--mat-slide-toggle-unselected-handle-horizontal-margin:0;--mat-slide-toggle-unselected-handle-size:20px;--mat-slide-toggle-unselected-hover-state-layer-opacity:.04;--mat-slide-toggle-unselected-icon-size:18px;--mat-slide-toggle-unselected-pressed-handle-horizontal-margin:0;--mat-slide-toggle-unselected-pressed-state-layer-opacity:.1;--mat-slide-toggle-unselected-with-icon-handle-horizontal-margin:0;--mat-slide-toggle-visible-track-opacity:1;--mat-slide-toggle-visible-track-transition:transform 75ms 0ms cubic-bezier(0, 0, .2, 1);--mat-slide-toggle-with-icon-handle-size:20px}html{--mat-slide-toggle-selected-focus-state-layer-color:#3949ab;--mat-slide-toggle-selected-handle-color:#3949ab;--mat-slide-toggle-selected-hover-state-layer-color:#3949ab;--mat-slide-toggle-selected-pressed-state-layer-color:#3949ab;--mat-slide-toggle-selected-focus-handle-color:#1a237e;--mat-slide-toggle-selected-hover-handle-color:#1a237e;--mat-slide-toggle-selected-pressed-handle-color:#1a237e;--mat-slide-toggle-selected-focus-track-color:#7986cb;--mat-slide-toggle-selected-hover-track-color:#7986cb;--mat-slide-toggle-selected-pressed-track-color:#7986cb;--mat-slide-toggle-selected-track-color:#7986cb;--mat-slide-toggle-disabled-handle-elevation-shadow:0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mat-slide-toggle-disabled-selected-handle-color:#424242;--mat-slide-toggle-disabled-selected-icon-color:#fff;--mat-slide-toggle-disabled-selected-track-color:#424242;--mat-slide-toggle-disabled-unselected-handle-color:#424242;--mat-slide-toggle-disabled-unselected-icon-color:#fff;--mat-slide-toggle-disabled-unselected-track-color:#424242;--mat-slide-toggle-handle-elevation-shadow:0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mat-slide-toggle-handle-surface-color:#fff;--mat-slide-toggle-label-text-color:rgba(0, 0, 0, .87);--mat-slide-toggle-selected-icon-color:#fff;--mat-slide-toggle-unselected-hover-handle-color:#212121;--mat-slide-toggle-unselected-focus-handle-color:#212121;--mat-slide-toggle-unselected-focus-state-layer-color:#424242;--mat-slide-toggle-unselected-focus-track-color:#e0e0e0;--mat-slide-toggle-unselected-icon-color:#fff;--mat-slide-toggle-unselected-handle-color:#616161;--mat-slide-toggle-unselected-hover-state-layer-color:#424242;--mat-slide-toggle-unselected-hover-track-color:#e0e0e0;--mat-slide-toggle-unselected-pressed-handle-color:#212121;--mat-slide-toggle-unselected-pressed-track-color:#e0e0e0;--mat-slide-toggle-unselected-pressed-state-layer-color:#424242;--mat-slide-toggle-unselected-track-color:#e0e0e0}html{--mdc-slide-toggle-disabled-label-text-color:rgba(0, 0, 0, .38);--mat-slide-toggle-disabled-label-text-color:rgba(0, 0, 0, .38)}html{--mat-slide-toggle-state-layer-size:40px}html{--mat-slide-toggle-label-text-font:Roboto, sans-serif;--mat-slide-toggle-label-text-line-height:20px;--mat-slide-toggle-label-text-size:14px;--mat-slide-toggle-label-text-tracking:.0178571429em;--mat-slide-toggle-label-text-weight:400}html{--mat-radio-disabled-selected-icon-opacity:.38;--mat-radio-disabled-unselected-icon-opacity:.38;--mat-radio-state-layer-size:40px}html{--mat-radio-state-layer-size:40px;--mat-radio-touch-target-display:block}html{--mat-radio-label-text-font:Roboto, sans-serif;--mat-radio-label-text-line-height:20px;--mat-radio-label-text-size:14px;--mat-radio-label-text-tracking:.0178571429em;--mat-radio-label-text-weight:400}html{--mat-slider-active-track-height:6px;--mat-slider-active-track-shape:9999px;--mat-slider-handle-elevation:0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mat-slider-handle-height:20px;--mat-slider-handle-shape:50%;--mat-slider-handle-width:20px;--mat-slider-inactive-track-height:4px;--mat-slider-inactive-track-shape:9999px;--mat-slider-value-indicator-border-radius:4px;--mat-slider-value-indicator-caret-display:block;--mat-slider-value-indicator-container-transform:translateX(-50%);--mat-slider-value-indicator-height:32px;--mat-slider-value-indicator-padding:0 12px;--mat-slider-value-indicator-text-transform:none;--mat-slider-value-indicator-width:auto;--mat-slider-with-overlap-handle-outline-width:1px;--mat-slider-with-tick-marks-active-container-opacity:.6;--mat-slider-with-tick-marks-container-shape:50%;--mat-slider-with-tick-marks-container-size:2px;--mat-slider-with-tick-marks-inactive-container-opacity:.6}html{--mat-slider-active-track-color:#3f51b5;--mat-slider-focus-handle-color:#3f51b5;--mat-slider-focus-state-layer-color:rgba(63, 81, 181, .2);--mat-slider-handle-color:#3f51b5;--mat-slider-hover-handle-color:#3f51b5;--mat-slider-hover-state-layer-color:rgba(63, 81, 181, .05);--mat-slider-inactive-track-color:#3f51b5;--mat-slider-ripple-color:#3f51b5;--mat-slider-with-tick-marks-active-container-color:white;--mat-slider-with-tick-marks-inactive-container-color:#3f51b5;--mat-slider-disabled-active-track-color:#000;--mat-slider-disabled-handle-color:#000;--mat-slider-disabled-inactive-track-color:#000;--mat-slider-label-container-color:#000;--mat-slider-label-label-text-color:#fff;--mat-slider-value-indicator-opacity:.6;--mat-slider-with-overlap-handle-outline-color:#fff;--mat-slider-with-tick-marks-disabled-container-color:#000}html{--mat-slider-label-label-text-font:Roboto, sans-serif;--mat-slider-label-label-text-size:14px;--mat-slider-label-label-text-line-height:22px;--mat-slider-label-label-text-tracking:.0071428571em;--mat-slider-label-label-text-weight:500}html{--mat-menu-container-shape:4px;--mat-menu-divider-bottom-spacing:0;--mat-menu-divider-top-spacing:0;--mat-menu-item-spacing:16px;--mat-menu-item-icon-size:24px;--mat-menu-item-leading-spacing:16px;--mat-menu-item-trailing-spacing:16px;--mat-menu-item-with-icon-leading-spacing:16px;--mat-menu-item-with-icon-trailing-spacing:16px;--mat-menu-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12)}html{--mat-menu-item-label-text-color:rgba(0, 0, 0, .87);--mat-menu-item-icon-color:rgba(0, 0, 0, .87);--mat-menu-item-hover-state-layer-color:rgba(0, 0, 0, .04);--mat-menu-item-focus-state-layer-color:rgba(0, 0, 0, .04);--mat-menu-container-color:white;--mat-menu-divider-color:rgba(0, 0, 0, .12)}html{--mat-menu-item-label-text-font:Roboto, sans-serif;--mat-menu-item-label-text-size:16px;--mat-menu-item-label-text-tracking:.03125em;--mat-menu-item-label-text-line-height:24px;--mat-menu-item-label-text-weight:400}html{--mat-list-active-indicator-color:transparent;--mat-list-active-indicator-shape:4px;--mat-list-list-item-container-shape:0;--mat-list-list-item-leading-avatar-shape:50%;--mat-list-list-item-container-color:transparent;--mat-list-list-item-selected-container-color:transparent;--mat-list-list-item-leading-avatar-color:transparent;--mat-list-list-item-leading-icon-size:24px;--mat-list-list-item-leading-avatar-size:40px;--mat-list-list-item-trailing-icon-size:24px;--mat-list-list-item-disabled-state-layer-color:transparent;--mat-list-list-item-disabled-state-layer-opacity:0;--mat-list-list-item-disabled-label-text-opacity:.38;--mat-list-list-item-disabled-leading-icon-opacity:.38;--mat-list-list-item-disabled-trailing-icon-opacity:.38}html{--mat-list-list-item-label-text-color:rgba(0, 0, 0, .87);--mat-list-list-item-supporting-text-color:rgba(0, 0, 0, .54);--mat-list-list-item-leading-icon-color:rgba(0, 0, 0, .38);--mat-list-list-item-trailing-supporting-text-color:rgba(0, 0, 0, .38);--mat-list-list-item-trailing-icon-color:rgba(0, 0, 0, .38);--mat-list-list-item-selected-trailing-icon-color:rgba(0, 0, 0, .38);--mat-list-list-item-disabled-label-text-color:black;--mat-list-list-item-disabled-leading-icon-color:black;--mat-list-list-item-disabled-trailing-icon-color:black;--mat-list-list-item-hover-label-text-color:rgba(0, 0, 0, .87);--mat-list-list-item-hover-leading-icon-color:rgba(0, 0, 0, .38);--mat-list-list-item-hover-state-layer-color:black;--mat-list-list-item-hover-state-layer-opacity:.04;--mat-list-list-item-hover-trailing-icon-color:rgba(0, 0, 0, .38);--mat-list-list-item-focus-label-text-color:rgba(0, 0, 0, .87);--mat-list-list-item-focus-state-layer-color:black;--mat-list-list-item-focus-state-layer-opacity:.12}html{--mat-list-list-item-leading-icon-start-space:16px;--mat-list-list-item-leading-icon-end-space:32px;--mat-list-list-item-one-line-container-height:48px;--mat-list-list-item-two-line-container-height:64px;--mat-list-list-item-three-line-container-height:88px}html{--mat-list-list-item-label-text-font:Roboto, sans-serif;--mat-list-list-item-label-text-line-height:24px;--mat-list-list-item-label-text-size:16px;--mat-list-list-item-label-text-tracking:.03125em;--mat-list-list-item-label-text-weight:400;--mat-list-list-item-supporting-text-font:Roboto, sans-serif;--mat-list-list-item-supporting-text-line-height:20px;--mat-list-list-item-supporting-text-size:14px;--mat-list-list-item-supporting-text-tracking:.0178571429em;--mat-list-list-item-supporting-text-weight:400;--mat-list-list-item-trailing-supporting-text-font:Roboto, sans-serif;--mat-list-list-item-trailing-supporting-text-line-height:20px;--mat-list-list-item-trailing-supporting-text-size:12px;--mat-list-list-item-trailing-supporting-text-tracking:.0333333333em;--mat-list-list-item-trailing-supporting-text-weight:400}html{--mat-paginator-container-text-color:rgba(0, 0, 0, .87);--mat-paginator-container-background-color:white;--mat-paginator-enabled-icon-color:rgba(0, 0, 0, .54);--mat-paginator-disabled-icon-color:rgba(0, 0, 0, .12)}html{--mat-paginator-container-size:56px;--mat-paginator-form-field-container-height:40px;--mat-paginator-form-field-container-vertical-padding:8px;--mat-paginator-touch-target-display:block}html{--mat-paginator-container-text-font:Roboto, sans-serif;--mat-paginator-container-text-line-height:20px;--mat-paginator-container-text-size:12px;--mat-paginator-container-text-tracking:.0333333333em;--mat-paginator-container-text-weight:400;--mat-paginator-select-trigger-text-size:12px}html{--mat-tab-container-height:48px;--mat-tab-divider-color:transparent;--mat-tab-divider-height:0;--mat-tab-active-indicator-height:2px;--mat-tab-active-indicator-shape:0}html{--mat-checkbox-disabled-selected-checkmark-color:#fff;--mat-checkbox-selected-focus-state-layer-opacity:.16;--mat-checkbox-selected-hover-state-layer-opacity:.04;--mat-checkbox-selected-pressed-state-layer-opacity:.16;--mat-checkbox-unselected-focus-state-layer-opacity:.16;--mat-checkbox-unselected-hover-state-layer-opacity:.04;--mat-checkbox-unselected-pressed-state-layer-opacity:.16}html{--mat-checkbox-disabled-label-color:rgba(0, 0, 0, .38);--mat-checkbox-label-text-color:rgba(0, 0, 0, .87);--mat-checkbox-disabled-selected-icon-color:rgba(0, 0, 0, .38);--mat-checkbox-disabled-unselected-icon-color:rgba(0, 0, 0, .38);--mat-checkbox-selected-checkmark-color:white;--mat-checkbox-selected-focus-icon-color:#ff4081;--mat-checkbox-selected-hover-icon-color:#ff4081;--mat-checkbox-selected-icon-color:#ff4081;--mat-checkbox-selected-pressed-icon-color:#ff4081;--mat-checkbox-unselected-focus-icon-color:#212121;--mat-checkbox-unselected-hover-icon-color:#212121;--mat-checkbox-unselected-icon-color:rgba(0, 0, 0, .54);--mat-checkbox-selected-focus-state-layer-color:#ff4081;--mat-checkbox-selected-hover-state-layer-color:#ff4081;--mat-checkbox-selected-pressed-state-layer-color:#ff4081;--mat-checkbox-unselected-focus-state-layer-color:black;--mat-checkbox-unselected-hover-state-layer-color:black;--mat-checkbox-unselected-pressed-state-layer-color:black}html{--mat-checkbox-touch-target-display:block;--mat-checkbox-state-layer-size:40px}html{--mat-checkbox-label-text-font:Roboto, sans-serif;--mat-checkbox-label-text-line-height:20px;--mat-checkbox-label-text-size:14px;--mat-checkbox-label-text-tracking:.0178571429em;--mat-checkbox-label-text-weight:400}html{--mat-button-filled-container-shape:4px;--mat-button-filled-horizontal-padding:16px;--mat-button-filled-icon-offset:-4px;--mat-button-filled-icon-spacing:8px;--mat-button-outlined-container-shape:4px;--mat-button-outlined-horizontal-padding:15px;--mat-button-outlined-icon-offset:-4px;--mat-button-outlined-icon-spacing:8px;--mat-button-outlined-keep-touch-target:false;--mat-button-outlined-outline-width:1px;--mat-button-protected-container-elevation-shadow:0px 3px 1px -2px rgba(0, 0, 0, .2), 0px 2px 2px 0px rgba(0, 0, 0, .14), 0px 1px 5px 0px rgba(0, 0, 0, .12);--mat-button-protected-container-shape:4px;--mat-button-protected-disabled-container-elevation-shadow:0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mat-button-protected-focus-container-elevation-shadow:0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mat-button-protected-horizontal-padding:16px;--mat-button-protected-hover-container-elevation-shadow:0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mat-button-protected-icon-offset:-4px;--mat-button-protected-icon-spacing:8px;--mat-button-protected-pressed-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-button-text-container-shape:4px;--mat-button-text-horizontal-padding:8px;--mat-button-text-icon-offset:0;--mat-button-text-icon-spacing:8px;--mat-button-text-with-icon-horizontal-padding:8px;--mat-button-tonal-container-shape:4px;--mat-button-tonal-horizontal-padding:16px;--mat-button-tonal-icon-offset:-4px;--mat-button-tonal-icon-spacing:8px}html{--mat-button-filled-container-color:white;--mat-button-filled-disabled-container-color:rgba(0, 0, 0, .12);--mat-button-filled-disabled-label-text-color:rgba(0, 0, 0, .38);--mat-button-filled-disabled-state-layer-color:black;--mat-button-filled-focus-state-layer-opacity:.12;--mat-button-filled-hover-state-layer-opacity:.04;--mat-button-filled-label-text-color:black;--mat-button-filled-pressed-state-layer-opacity:.12;--mat-button-filled-ripple-color:rgba(0, 0, 0, .1);--mat-button-filled-state-layer-color:black;--mat-button-outlined-disabled-label-text-color:rgba(0, 0, 0, .38);--mat-button-outlined-disabled-outline-color:rgba(0, 0, 0, .12);--mat-button-outlined-disabled-state-layer-color:black;--mat-button-outlined-focus-state-layer-opacity:.12;--mat-button-outlined-hover-state-layer-opacity:.04;--mat-button-outlined-label-text-color:black;--mat-button-outlined-outline-color:rgba(0, 0, 0, .12);--mat-button-outlined-pressed-state-layer-opacity:.12;--mat-button-outlined-ripple-color:rgba(0, 0, 0, .1);--mat-button-outlined-state-layer-color:black;--mat-button-protected-container-color:white;--mat-button-protected-disabled-container-color:rgba(0, 0, 0, .12);--mat-button-protected-disabled-label-text-color:rgba(0, 0, 0, .38);--mat-button-protected-disabled-state-layer-color:black;--mat-button-protected-focus-state-layer-opacity:.12;--mat-button-protected-hover-state-layer-opacity:.04;--mat-button-protected-label-text-color:black;--mat-button-protected-pressed-state-layer-opacity:.12;--mat-button-protected-ripple-color:rgba(0, 0, 0, .1);--mat-button-protected-state-layer-color:black;--mat-button-text-disabled-label-text-color:rgba(0, 0, 0, .38);--mat-button-text-disabled-state-layer-color:black;--mat-button-text-focus-state-layer-opacity:.12;--mat-button-text-hover-state-layer-opacity:.04;--mat-button-text-label-text-color:black;--mat-button-text-pressed-state-layer-opacity:.12;--mat-button-text-ripple-color:rgba(0, 0, 0, .1);--mat-button-text-state-layer-color:black;--mat-button-tonal-container-color:white;--mat-button-tonal-disabled-container-color:rgba(0, 0, 0, .12);--mat-button-tonal-disabled-label-text-color:rgba(0, 0, 0, .38);--mat-button-tonal-disabled-state-layer-color:black;--mat-button-tonal-focus-state-layer-opacity:.12;--mat-button-tonal-hover-state-layer-opacity:.04;--mat-button-tonal-label-text-color:black;--mat-button-tonal-pressed-state-layer-opacity:.12;--mat-button-tonal-ripple-color:rgba(0, 0, 0, .1);--mat-button-tonal-state-layer-color:black}html{--mat-button-filled-container-height:36px;--mat-button-filled-touch-target-display:block;--mat-button-outlined-container-height:36px;--mat-button-outlined-touch-target-display:block;--mat-button-protected-container-height:36px;--mat-button-protected-touch-target-display:block;--mat-button-text-container-height:36px;--mat-button-text-touch-target-display:block;--mat-button-tonal-container-height:36px;--mat-button-tonal-touch-target-display:block}html{--mat-button-filled-label-text-font:Roboto, sans-serif;--mat-button-filled-label-text-size:14px;--mat-button-filled-label-text-tracking:.0892857143em;--mat-button-filled-label-text-transform:none;--mat-button-filled-label-text-weight:500;--mat-button-outlined-label-text-font:Roboto, sans-serif;--mat-button-outlined-label-text-size:14px;--mat-button-outlined-label-text-tracking:.0892857143em;--mat-button-outlined-label-text-transform:none;--mat-button-outlined-label-text-weight:500;--mat-button-protected-label-text-font:Roboto, sans-serif;--mat-button-protected-label-text-size:14px;--mat-button-protected-label-text-tracking:.0892857143em;--mat-button-protected-label-text-transform:none;--mat-button-protected-label-text-weight:500;--mat-button-text-label-text-font:Roboto, sans-serif;--mat-button-text-label-text-size:14px;--mat-button-text-label-text-tracking:.0892857143em;--mat-button-text-label-text-transform:none;--mat-button-text-label-text-weight:500;--mat-button-tonal-label-text-font:Roboto, sans-serif;--mat-button-tonal-label-text-size:14px;--mat-button-tonal-label-text-tracking:.0892857143em;--mat-button-tonal-label-text-transform:none;--mat-button-tonal-label-text-weight:500}html{--mat-icon-button-icon-size:24px;--mat-icon-button-container-shape:50%}html{--mat-icon-button-disabled-icon-color:rgba(0, 0, 0, .38);--mat-icon-button-disabled-state-layer-color:black;--mat-icon-button-focus-state-layer-opacity:.12;--mat-icon-button-hover-state-layer-opacity:.04;--mat-icon-button-icon-color:inherit;--mat-icon-button-pressed-state-layer-opacity:.12;--mat-icon-button-ripple-color:rgba(0, 0, 0, .1);--mat-icon-button-state-layer-color:black}html{--mat-icon-button-touch-target-display:block}html{--mat-fab-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mat-fab-container-shape:50%;--mat-fab-extended-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mat-fab-extended-container-height:48px;--mat-fab-extended-container-shape:24px;--mat-fab-extended-focus-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-fab-extended-hover-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-fab-extended-pressed-container-elevation-shadow:0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12);--mat-fab-focus-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-fab-hover-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-fab-pressed-container-elevation-shadow:0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12);--mat-fab-small-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mat-fab-small-container-shape:50%;--mat-fab-small-focus-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-fab-small-hover-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-fab-small-pressed-container-elevation-shadow:0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12)}html{--mat-fab-container-color:white;--mat-fab-disabled-state-container-color:rgba(0, 0, 0, .12);--mat-fab-disabled-state-foreground-color:rgba(0, 0, 0, .38);--mat-fab-disabled-state-layer-color:black;--mat-fab-focus-state-layer-opacity:.12;--mat-fab-foreground-color:black;--mat-fab-hover-state-layer-opacity:.04;--mat-fab-pressed-state-layer-opacity:.12;--mat-fab-ripple-color:rgba(0, 0, 0, .1);--mat-fab-small-container-color:white;--mat-fab-small-disabled-state-container-color:rgba(0, 0, 0, .12);--mat-fab-small-disabled-state-foreground-color:rgba(0, 0, 0, .38);--mat-fab-small-disabled-state-layer-color:black;--mat-fab-small-focus-state-layer-opacity:.12;--mat-fab-small-foreground-color:black;--mat-fab-small-hover-state-layer-opacity:.04;--mat-fab-small-pressed-state-layer-opacity:.12;--mat-fab-small-ripple-color:rgba(0, 0, 0, .1);--mat-fab-small-state-layer-color:black;--mat-fab-state-layer-color:black}html{--mat-fab-small-touch-target-display:block;--mat-fab-touch-target-display:block}html{--mat-fab-extended-label-text-font:Roboto, sans-serif;--mat-fab-extended-label-text-size:14px;--mat-fab-extended-label-text-tracking:.0892857143em;--mat-fab-extended-label-text-weight:500}html{--mat-snack-bar-container-shape:4px}html{--mat-snack-bar-container-color:#333333;--mat-snack-bar-supporting-text-color:rgba(255, 255, 255, .87);--mat-snack-bar-button-color:#c5cae9}html{--mat-snack-bar-supporting-text-font:Roboto, sans-serif;--mat-snack-bar-supporting-text-line-height:20px;--mat-snack-bar-supporting-text-size:14px;--mat-snack-bar-supporting-text-weight:400}html{--mat-table-row-item-outline-width:1px}html{--mat-table-background-color:white;--mat-table-header-headline-color:rgba(0, 0, 0, .87);--mat-table-row-item-label-text-color:rgba(0, 0, 0, .87);--mat-table-row-item-outline-color:rgba(0, 0, 0, .12)}html{--mat-table-header-container-height:56px;--mat-table-footer-container-height:52px;--mat-table-row-item-container-height:52px}html{--mat-table-header-headline-font:Roboto, sans-serif;--mat-table-header-headline-line-height:22px;--mat-table-header-headline-size:14px;--mat-table-header-headline-weight:500;--mat-table-header-headline-tracking:.0071428571em;--mat-table-row-item-label-text-font:Roboto, sans-serif;--mat-table-row-item-label-text-line-height:20px;--mat-table-row-item-label-text-size:14px;--mat-table-row-item-label-text-weight:400;--mat-table-row-item-label-text-tracking:.0178571429em;--mat-table-footer-supporting-text-font:Roboto, sans-serif;--mat-table-footer-supporting-text-line-height:20px;--mat-table-footer-supporting-text-size:14px;--mat-table-footer-supporting-text-weight:400;--mat-table-footer-supporting-text-tracking:.0178571429em}html{--mat-progress-spinner-active-indicator-width:4px;--mat-progress-spinner-size:48px}html{--mat-progress-spinner-active-indicator-color:#3f51b5}html{--mat-badge-container-shape:50%;--mat-badge-container-size:unset;--mat-badge-small-size-container-size:unset;--mat-badge-large-size-container-size:unset;--mat-badge-legacy-container-size:22px;--mat-badge-legacy-small-size-container-size:16px;--mat-badge-legacy-large-size-container-size:28px;--mat-badge-container-offset:-11px 0;--mat-badge-small-size-container-offset:-8px 0;--mat-badge-large-size-container-offset:-14px 0;--mat-badge-container-overlap-offset:-11px;--mat-badge-small-size-container-overlap-offset:-8px;--mat-badge-large-size-container-overlap-offset:-14px;--mat-badge-container-padding:0;--mat-badge-small-size-container-padding:0;--mat-badge-large-size-container-padding:0}html{--mat-badge-background-color:#3f51b5;--mat-badge-text-color:white;--mat-badge-disabled-state-background-color:#b9b9b9;--mat-badge-disabled-state-text-color:rgba(0, 0, 0, .38)}html{--mat-badge-text-font:Roboto, sans-serif;--mat-badge-line-height:22px;--mat-badge-text-size:12px;--mat-badge-text-weight:600;--mat-badge-small-size-text-size:9px;--mat-badge-small-size-line-height:16px;--mat-badge-large-size-text-size:24px;--mat-badge-large-size-line-height:28px}html{--mat-bottom-sheet-container-shape:4px}html{--mat-bottom-sheet-container-text-color:rgba(0, 0, 0, .87);--mat-bottom-sheet-container-background-color:white}html{--mat-bottom-sheet-container-text-font:Roboto, sans-serif;--mat-bottom-sheet-container-text-line-height:20px;--mat-bottom-sheet-container-text-size:14px;--mat-bottom-sheet-container-text-tracking:.0178571429em;--mat-bottom-sheet-container-text-weight:400}html{--mat-button-toggle-focus-state-layer-opacity:.12;--mat-button-toggle-hover-state-layer-opacity:.04;--mat-button-toggle-legacy-focus-state-layer-opacity:1;--mat-button-toggle-legacy-height:36px;--mat-button-toggle-legacy-shape:2px;--mat-button-toggle-shape:4px}html{--mat-button-toggle-background-color:white;--mat-button-toggle-disabled-selected-state-background-color:#bdbdbd;--mat-button-toggle-disabled-selected-state-text-color:rgba(0, 0, 0, .87);--mat-button-toggle-disabled-state-background-color:white;--mat-button-toggle-disabled-state-text-color:rgba(0, 0, 0, .26);--mat-button-toggle-divider-color:rgb(224.4, 224.4, 224.4);--mat-button-toggle-legacy-disabled-selected-state-background-color:#bdbdbd;--mat-button-toggle-legacy-disabled-state-background-color:#eeeeee;--mat-button-toggle-legacy-disabled-state-text-color:rgba(0, 0, 0, .26);--mat-button-toggle-legacy-selected-state-background-color:#e0e0e0;--mat-button-toggle-legacy-selected-state-text-color:rgba(0, 0, 0, .54);--mat-button-toggle-legacy-state-layer-color:rgba(0, 0, 0, .12);--mat-button-toggle-legacy-text-color:rgba(0, 0, 0, .38);--mat-button-toggle-selected-state-background-color:#e0e0e0;--mat-button-toggle-selected-state-text-color:rgba(0, 0, 0, .87);--mat-button-toggle-state-layer-color:black;--mat-button-toggle-text-color:rgba(0, 0, 0, .87)}html{--mat-button-toggle-height:48px}html{--mat-button-toggle-label-text-font:Roboto, sans-serif;--mat-button-toggle-label-text-line-height:24px;--mat-button-toggle-label-text-size:16px;--mat-button-toggle-label-text-tracking:.03125em;--mat-button-toggle-label-text-weight:400;--mat-button-toggle-legacy-label-text-font:Roboto, sans-serif;--mat-button-toggle-legacy-label-text-line-height:24px;--mat-button-toggle-legacy-label-text-size:16px;--mat-button-toggle-legacy-label-text-tracking:.03125em;--mat-button-toggle-legacy-label-text-weight:400}html{--mat-datepicker-calendar-container-shape:4px;--mat-datepicker-calendar-container-touch-shape:4px;--mat-datepicker-calendar-container-elevation-shadow:0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mat-datepicker-calendar-container-touch-elevation-shadow:0px 11px 15px -7px rgba(0, 0, 0, .2), 0px 24px 38px 3px rgba(0, 0, 0, .14), 0px 9px 46px 8px rgba(0, 0, 0, .12)}html{--mat-datepicker-calendar-date-selected-state-text-color:white;--mat-datepicker-calendar-date-selected-state-background-color:#3f51b5;--mat-datepicker-calendar-date-selected-disabled-state-background-color:rgba(63, 81, 181, .4);--mat-datepicker-calendar-date-today-selected-state-outline-color:white;--mat-datepicker-calendar-date-focus-state-background-color:rgba(63, 81, 181, .3);--mat-datepicker-calendar-date-hover-state-background-color:rgba(63, 81, 181, .3);--mat-datepicker-toggle-active-state-icon-color:#3f51b5;--mat-datepicker-calendar-date-in-range-state-background-color:rgba(63, 81, 181, .2);--mat-datepicker-calendar-date-in-comparison-range-state-background-color:rgba(249, 171, 0, .2);--mat-datepicker-calendar-date-in-overlap-range-state-background-color:#a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color:rgb(69.5241935484, 163.4758064516, 93.9516129032);--mat-datepicker-toggle-icon-color:rgba(0, 0, 0, .54);--mat-datepicker-calendar-body-label-text-color:rgba(0, 0, 0, .54);--mat-datepicker-calendar-period-button-text-color:black;--mat-datepicker-calendar-period-button-icon-color:rgba(0, 0, 0, .54);--mat-datepicker-calendar-navigation-button-icon-color:rgba(0, 0, 0, .54);--mat-datepicker-calendar-header-divider-color:rgba(0, 0, 0, .12);--mat-datepicker-calendar-header-text-color:rgba(0, 0, 0, .54);--mat-datepicker-calendar-date-today-outline-color:rgba(0, 0, 0, .38);--mat-datepicker-calendar-date-today-disabled-state-outline-color:rgba(0, 0, 0, .18);--mat-datepicker-calendar-date-text-color:rgba(0, 0, 0, .87);--mat-datepicker-calendar-date-outline-color:transparent;--mat-datepicker-calendar-date-disabled-state-text-color:rgba(0, 0, 0, .38);--mat-datepicker-calendar-date-preview-state-outline-color:rgba(0, 0, 0, .24);--mat-datepicker-range-input-separator-color:rgba(0, 0, 0, .87);--mat-datepicker-range-input-disabled-state-separator-color:rgba(0, 0, 0, .38);--mat-datepicker-range-input-disabled-state-text-color:rgba(0, 0, 0, .38);--mat-datepicker-calendar-container-background-color:white;--mat-datepicker-calendar-container-text-color:rgba(0, 0, 0, .87)}html{--mat-datepicker-calendar-text-font:Roboto, sans-serif;--mat-datepicker-calendar-text-size:13px;--mat-datepicker-calendar-body-label-text-size:14px;--mat-datepicker-calendar-body-label-text-weight:500;--mat-datepicker-calendar-period-button-text-size:14px;--mat-datepicker-calendar-period-button-text-weight:500;--mat-datepicker-calendar-header-text-size:11px;--mat-datepicker-calendar-header-text-weight:400}html{--mat-divider-width:1px}html{--mat-divider-color:rgba(0, 0, 0, .12)}html{--mat-expansion-container-shape:4px;--mat-expansion-legacy-header-indicator-display:inline-block;--mat-expansion-header-indicator-display:none}html{--mat-expansion-container-background-color:white;--mat-expansion-container-text-color:rgba(0, 0, 0, .87);--mat-expansion-actions-divider-color:rgba(0, 0, 0, .12);--mat-expansion-header-hover-state-layer-color:rgba(0, 0, 0, .04);--mat-expansion-header-focus-state-layer-color:rgba(0, 0, 0, .04);--mat-expansion-header-disabled-state-text-color:rgba(0, 0, 0, .26);--mat-expansion-header-text-color:rgba(0, 0, 0, .87);--mat-expansion-header-description-color:rgba(0, 0, 0, .54);--mat-expansion-header-indicator-color:rgba(0, 0, 0, .54)}html{--mat-expansion-header-collapsed-state-height:48px;--mat-expansion-header-expanded-state-height:64px}html{--mat-expansion-header-text-font:Roboto, sans-serif;--mat-expansion-header-text-size:14px;--mat-expansion-header-text-weight:500;--mat-expansion-header-text-line-height:inherit;--mat-expansion-header-text-tracking:inherit;--mat-expansion-container-text-font:Roboto, sans-serif;--mat-expansion-container-text-line-height:20px;--mat-expansion-container-text-size:14px;--mat-expansion-container-text-tracking:.0178571429em;--mat-expansion-container-text-weight:400}html{--mat-grid-list-tile-header-primary-text-size:14px;--mat-grid-list-tile-header-secondary-text-size:12px;--mat-grid-list-tile-footer-primary-text-size:14px;--mat-grid-list-tile-footer-secondary-text-size:12px}html{--mat-icon-color:inherit}html{--mat-sidenav-container-shape:0;--mat-sidenav-container-elevation-shadow:0px 8px 10px -5px rgba(0, 0, 0, .2), 0px 16px 24px 2px rgba(0, 0, 0, .14), 0px 6px 30px 5px rgba(0, 0, 0, .12);--mat-sidenav-container-width:auto}html{--mat-sidenav-container-divider-color:rgba(0, 0, 0, .12);--mat-sidenav-container-background-color:white;--mat-sidenav-container-text-color:rgba(0, 0, 0, .87);--mat-sidenav-content-background-color:#fafafa;--mat-sidenav-content-text-color:rgba(0, 0, 0, .87);--mat-sidenav-scrim-color:rgba(0, 0, 0, .6)}html{--mat-stepper-header-icon-foreground-color:white;--mat-stepper-header-selected-state-icon-background-color:#3f51b5;--mat-stepper-header-selected-state-icon-foreground-color:white;--mat-stepper-header-done-state-icon-background-color:#3f51b5;--mat-stepper-header-done-state-icon-foreground-color:white;--mat-stepper-header-edit-state-icon-background-color:#3f51b5;--mat-stepper-header-edit-state-icon-foreground-color:white;--mat-stepper-container-color:white;--mat-stepper-line-color:rgba(0, 0, 0, .12);--mat-stepper-header-hover-state-layer-color:rgba(0, 0, 0, .04);--mat-stepper-header-focus-state-layer-color:rgba(0, 0, 0, .04);--mat-stepper-header-label-text-color:rgba(0, 0, 0, .54);--mat-stepper-header-optional-label-text-color:rgba(0, 0, 0, .54);--mat-stepper-header-selected-state-label-text-color:rgba(0, 0, 0, .87);--mat-stepper-header-error-state-label-text-color:#f44336;--mat-stepper-header-icon-background-color:rgba(0, 0, 0, .54);--mat-stepper-header-error-state-icon-foreground-color:#f44336;--mat-stepper-header-error-state-icon-background-color:transparent}html{--mat-stepper-header-height:72px}html{--mat-stepper-container-text-font:Roboto, sans-serif;--mat-stepper-header-label-text-font:Roboto, sans-serif;--mat-stepper-header-label-text-size:14px;--mat-stepper-header-label-text-weight:400;--mat-stepper-header-error-state-label-text-size:16px;--mat-stepper-header-selected-state-label-text-size:16px;--mat-stepper-header-selected-state-label-text-weight:400}html{--mat-sort-arrow-color:rgb(117.3, 117.3, 117.3)}html{--mat-toolbar-container-background-color:whitesmoke;--mat-toolbar-container-text-color:rgba(0, 0, 0, .87)}html{--mat-toolbar-standard-height:64px;--mat-toolbar-mobile-height:56px}html{--mat-toolbar-title-text-font:Roboto, sans-serif;--mat-toolbar-title-text-line-height:32px;--mat-toolbar-title-text-size:20px;--mat-toolbar-title-text-tracking:.0125em;--mat-toolbar-title-text-weight:500}html{--mat-tree-container-background-color:white;--mat-tree-node-text-color:rgba(0, 0, 0, .87)}html{--mat-tree-node-min-height:48px}html{--mat-tree-node-text-font:Roboto, sans-serif;--mat-tree-node-text-size:14px;--mat-tree-node-text-weight:400}html{--mat-timepicker-container-shape:4px;--mat-timepicker-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12)}html{--mat-timepicker-container-background-color:white}.mat-typography{font:400 14px/20px Roboto,sans-serif;letter-spacing:.0178571429em}*{margin:0;padding:0;box-sizing:border-box}html,body{height:100%;font-family:Roboto,sans-serif}body{background:linear-gradient(135deg,#667eea,#764ba2);min-height:100vh}:root{--primary-color:#3f51b5;--accent-color:#ff4081;--warn-color:#f44336;--success-color:#4caf50;--info-color:#2196f3;--warning-color:#ff9800}</style><link rel="stylesheet" href="styles.22d14ea5f0b74fd9.css" media="print" onload="this.media='all'"><noscript><link rel="stylesheet" href="styles.22d14ea5f0b74fd9.css"></noscript></head>
<body class="mat-typography">
  <app-root></app-root>
  
  <!-- Razorpay Checkout Script -->
  <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script src="runtime.44b8a703ff97c203.js" type="module"></script><script src="polyfills.3d23f9968fa04de2.js" type="module"></script><script src="main.b42a0ebee2c3a473.js" type="module"></script></body>
</html>
