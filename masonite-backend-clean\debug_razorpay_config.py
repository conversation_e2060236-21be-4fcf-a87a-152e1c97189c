#!/usr/bin/env python3
"""
Debug Razorpay Configuration from within Masonite project
"""

import os
from masonite.configuration import config

def debug_razorpay_config():
    print("🔍 DEBUGGING RAZORPAY CONFIGURATION")
    print("="*50)
    
    # Check environment variables directly
    print("📋 Environment Variables:")
    print(f"   RAZORPAY_KEY_ID: {os.getenv('RAZORPAY_KEY_ID', 'NOT SET')}")
    print(f"   RAZORPAY_KEY_SECRET: {os.getenv('RAZORPAY_KEY_SECRET', 'NOT SET')}")
    
    # Check config function
    try:
        print("\n🔧 Config Function Results:")
        key_id = config('services.razorpay.key_id', 'DEFAULT_KEY_ID')
        key_secret = config('services.razorpay.key_secret', 'DEFAULT_KEY_SECRET')
        print(f"   config('services.razorpay.key_id'): {key_id}")
        print(f"   config('services.razorpay.key_secret'): {key_secret}")
        
        # Check test mode logic
        test_mode = (
            key_secret == 'your_secret_key_here' or 
            key_secret == 'test_secret_key' or 
            key_id == 'your_key_id_here' or
            not key_secret or
            not key_id
        )
        print(f"\n🧪 Test Mode Detection:")
        print(f"   key_secret == 'your_secret_key_here': {key_secret == 'your_secret_key_here'}")
        print(f"   key_secret == 'test_secret_key': {key_secret == 'test_secret_key'}")
        print(f"   key_id == 'your_key_id_here': {key_id == 'your_key_id_here'}")
        print(f"   not key_secret: {not key_secret}")
        print(f"   not key_id: {not key_id}")
        print(f"   FINAL TEST MODE: {test_mode}")
        
        if not test_mode:
            print(f"\n✅ Should be using REAL Razorpay API")
            try:
                import razorpay
                razorpay_client = razorpay.Client(auth=(key_id, key_secret))
                print(f"   Razorpay client created successfully")
            except Exception as e:
                print(f"   ❌ Razorpay client creation failed: {e}")
        else:
            print(f"\n🧪 Will use test/mock mode")
        
    except Exception as e:
        print(f"❌ Config error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_razorpay_config()
