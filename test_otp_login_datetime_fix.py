"""
Test OTP login endpoint on port 3002 to verify datetime serialization fix
"""
import requests
import json

def test_otp_login():
    """Test the OTP login endpoint"""
      # Step 1: Send OTP to email
    send_url = "http://localhost:3002/api/otp/send"
    send_payload = {
        "email": "<EMAIL>",
        "type": "login"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Origin': 'http://localhost:3000'
    }
    
    print("🧪 Testing OTP login flow...")
    print(f"📡 Step 1: Sending OTP to {send_payload['email']}")
    
    try:
        # Send OTP
        send_response = requests.post(send_url, json=send_payload, headers=headers, timeout=30)
        print(f"✅ OTP Send Response Status: {send_response.status_code}")
        
        if send_response.status_code == 200:
            print("📧 OTP sent successfully")
            
            # Step 2: Get OTP code from user
            otp_code = input("🔢 Please enter the OTP code you received: ").strip()
            
            if otp_code:
                # Step 3: Login with OTP
                login_url = "http://localhost:3002/api/otp/login"
                login_payload = {
                    "identifier": "<EMAIL>",
                    "code": otp_code
                }
                
                print(f"📡 Step 2: Logging in with OTP code: {otp_code}")
                
                login_response = requests.post(login_url, json=login_payload, headers=headers, timeout=30)
                print(f"✅ OTP Login Response Status: {login_response.status_code}")
                
                if login_response.headers.get('content-type', '').startswith('application/json'):
                    response_data = login_response.json()
                    print(f"📄 Response Body: {json.dumps(response_data, indent=2)}")
                    
                    if login_response.status_code == 200 and 'token' in response_data:
                        print("🎉 OTP LOGIN SUCCESSFUL!")
                        print(f"🔑 Token received: {response_data['token'][:50]}...")
                        print(f"👤 User data: {response_data.get('user', {})}")
                    else:
                        print("❌ OTP login failed")
                        print(f"Error: {response_data}")
                else:
                    print(f"📄 Response Text: {login_response.text[:500]}...")
            else:
                print("❌ No OTP code entered")
        else:
            print("❌ Failed to send OTP")
            if send_response.headers.get('content-type', '').startswith('application/json'):
                print(f"Error: {send_response.json()}")
                
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the server running on port 3002?")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_otp_login()
