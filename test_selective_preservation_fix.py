#!/usr/bin/env python3
"""
Test the selective preservation fix - only security data should be preserved
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3002/api"

def test_selective_preservation_fix():
    """Test that only selected data categories are preserved"""
    email = f"test.selective.{int(time.time())}@example.com"
    password = "TestPassword123!"
    
    print("🧪 Testing Selective Preservation Fix")
    print("=" * 60)
    
    # Step 1: Register user
    print("📝 Step 1: Registering user...")
    register_data = {
        "email": email,
        "password": password,
        "confirmPassword": password,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+1234567890"
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=register_data)
    if response.status_code not in [200, 201]:
        print(f"❌ Registration failed: {response.text}")
        return False
    
    user_data = response.json()
    user_id = user_data.get('userId') or user_data.get('user', {}).get('id')
    token = user_data.get('token')
    print(f"✅ User registered with ID: {user_id}")
    
    # Step 2: Request deletion with ONLY security logs preservation
    print("\n📝 Step 2: Requesting deletion with ONLY security logs preservation...")
    deletion_data = {
        "preservePaymentData": False,      # ❌ NOT preserved
        "preserveTransactionHistory": False, # ❌ NOT preserved  
        "preserveProfileData": False,      # ❌ NOT preserved
        "preserveSecurityLogs": True,      # ✅ ONLY this preserved
        "customRetentionPeriod": 30,
        "reason": "Testing selective preservation fix"
    }
    
    response = requests.post(f"{BASE_URL}/account/request-deletion", 
                           json=deletion_data,
                           headers={'Authorization': f'Bearer {token}'})
    
    if response.status_code != 200:
        print(f"❌ Deletion request failed: {response.text}")
        return False
    
    deletion_response = response.json()
    deletion_id = deletion_response['deletionId']
    confirmation_token = deletion_response['confirmationToken']
    print(f"✅ Deletion requested with ID: {deletion_id}")
    
    # Step 3: Confirm deletion
    print("\n📝 Step 3: Confirming deletion...")
    response = requests.post(f"{BASE_URL}/account/confirm-deletion", 
                           json={'token': confirmation_token})
    
    if response.status_code != 200:
        print(f"❌ Deletion confirmation failed: {response.text}")
        return False
    
    confirm_response = response.json()
    print("✅ Deletion confirmed")
    preserved_summary_from_deletion = confirm_response.get('preservedDataSummary', {})
    print(f"📄 Preserved data summary from deletion: {preserved_summary_from_deletion}")
    
    # Step 4: Check preserved data
    print("\n📝 Step 4: Checking preserved data...")
    response = requests.post(f"{BASE_URL}/account/check-preserved-data", 
                           json={'email': email})
    
    if response.status_code != 200:
        print(f"❌ Check preserved data failed: {response.text}")
        return False
    
    check_response = response.json()
    has_preserved = check_response.get('hasPreservedData', False)
    preserved_summary = check_response.get('preservedDataSummary', {})
    
    print(f"📊 Has preserved data: {has_preserved}")
    print(f"📊 Preserved data summary: {preserved_summary}")
    
    # Step 5: Validate expectations
    print("\n📝 Step 5: Validating expectations...")
    
    if not has_preserved:
        print("❌ FAIL: Expected preserved data (security logs) but found none")
        return False
    
    # Should ONLY have security data
    expected_categories = ['security']
    unexpected_categories = ['payments', 'profile', 'transactions']
    
    # Check that ONLY security is preserved
    for category in expected_categories:
        if category not in preserved_summary:
            print(f"❌ FAIL: Expected {category} to be preserved but it's missing")
            return False
        print(f"✅ PASS: {category} is correctly preserved")
    
    # Check that other categories are NOT preserved
    for category in unexpected_categories:
        if category in preserved_summary:
            print(f"❌ FAIL: {category} should NOT be preserved but it appears in summary")
            print(f"   This is the bug we're trying to fix!")
            return False
        print(f"✅ PASS: {category} is correctly NOT preserved")
    
    # Validate summary structure
    if len(preserved_summary) != 1:
        print(f"❌ FAIL: Expected exactly 1 preserved category but found {len(preserved_summary)}")
        print(f"   Categories found: {list(preserved_summary.keys())}")
        return False
    
    print("✅ PASS: Only security data is preserved as requested")
    print("✅ PASS: No unwanted data categories are preserved")
    print("✅ PASS: All validations successful!")
    
    return True

def main():
    """Main test function"""
    print("🚀 Starting Selective Preservation Fix Test")
    print("Testing that only selected data categories are preserved")
    print("=" * 80)
    
    success = test_selective_preservation_fix()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 SELECTIVE PRESERVATION FIX TEST PASSED!")
        print("✅ System correctly preserves only selected data categories")
    else:
        print("❌ SELECTIVE PRESERVATION FIX TEST FAILED!")
        print("⚠️ System is still preserving unwanted data categories")
    print("🏁 Test completed!")

if __name__ == "__main__":
    main()
