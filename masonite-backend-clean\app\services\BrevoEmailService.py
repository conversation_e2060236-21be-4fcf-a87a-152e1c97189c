"""
Brevo API Email Service
Alternative to SMTP - uses Brevo's REST API directly like LoopBack backend
"""

import requests
import json
from masonite.environment import env


class BrevoEmailService:
    """
    Brevo Email Service using REST API
    More reliable than SMTP and matches LoopBack implementation approach
    """
    
    def __init__(self):
        self.api_key = env('BREVO_API_KEY')
        self.api_url = env('BREVO_API_URL', 'https://api.brevo.com/v3/smtp/email')
        self.from_email = env('MAIL_FROM', '<EMAIL>')
    
    def send_verification_email(self, user, verification_token):
        """
        Send email verification email using Brevo API
        
        Args:
            user: User model instance
            verification_token: Email verification token
            
        Returns:
            dict: API response or error information
        """
        
        if not self.api_key or self.api_key == 'your-brevo-api-key-here':
            return {
                'success': False,
                'error': 'Brevo API key not configured',
                'fallback': 'terminal'
            }
        
        verification_url = f"{env('FRONTEND_URL', 'http://localhost:4200')}/auth/verify-email?token={verification_token}"
        
        # Prepare email data in Brevo API format
        email_data = {
            "sender": {
                "name": "Secure Backend",
                "email": self.from_email
            },
            "to": [
                {
                    "email": user.email,
                    "name": user.name
                }
            ],
            "subject": "Verify Your Email Address",
            "htmlContent": f"""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #333;">Verify Your Email Address</h2>
                <p>Hello {user.name},</p>
                <p>Thank you for registering with our secure application. Please click the button below to verify your email address:</p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{verification_url}"
                       style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                        Verify Email
                    </a>
                </div>
                <p>If the button doesn't work, copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #666;">{verification_url}</p>
                <p style="color: #666; font-size: 12px;">This link will expire in 24 hours.</p>
                <p style="color: #666; font-size: 12px;">If you didn't create an account, please ignore this email.</p>
            </div>
            """,
            "textContent": f"""
            Verify Your Email Address
            
            Hello {user.name},
            
            Thank you for registering with our secure application. Please click the link below to verify your email address:
            
            {verification_url}
            
            This link will expire in 24 hours.
            
            If you didn't create an account, please ignore this email.
            """
        }
        
        # Set up headers
        headers = {
            'accept': 'application/json',
            'api-key': self.api_key,
            'content-type': 'application/json'
        }
        
        try:
            # Send request to Brevo API
            response = requests.post(
                self.api_url,
                headers=headers,
                data=json.dumps(email_data),
                timeout=10
            )
            
            if response.status_code == 201:
                response_data = response.json()
                return {
                    'success': True,
                    'message_id': response_data.get('messageId'),
                    'method': 'brevo_api',
                    'to': user.email
                }
            else:
                error_data = response.json() if response.text else {'error': 'Unknown error'}
                return {
                    'success': False,
                    'error': f"Brevo API error {response.status_code}: {error_data}",
                    'method': 'brevo_api',
                    'status_code': response.status_code
                }
                
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': f"Request error: {str(e)}",
                'method': 'brevo_api'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"Unexpected error: {str(e)}",
                'method': 'brevo_api'
            }
    
    def send_password_reset_email(self, user, reset_token):
        """
        Send password reset email using Brevo API
        
        Args:
            user: User model instance
            reset_token: Password reset token
            
        Returns:
            dict: API response or error information
        """
        
        if not self.api_key or self.api_key == 'your-brevo-api-key-here':
            return {
                'success': False,
                'error': 'Brevo API key not configured',
                'fallback': 'terminal'
            }
        
        reset_url = f"{env('FRONTEND_URL', 'http://localhost:4200')}/auth/reset-password?token={reset_token}"
        
        email_data = {
            "sender": {
                "name": "Secure Backend",
                "email": self.from_email
            },
            "to": [
                {
                    "email": user.email,
                    "name": user.name
                }
            ],
            "subject": "Reset Your Password",
            "htmlContent": f"""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #333;">Reset Your Password</h2>
                <p>Hello {user.name},</p>
                <p>You requested to reset your password. Click the button below to set a new password:</p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{reset_url}"
                       style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                        Reset Password
                    </a>
                </div>
                <p>If the button doesn't work, copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #666;">{reset_url}</p>
                <p style="color: #666; font-size: 12px;">This link will expire in 1 hour.</p>
                <p style="color: #666; font-size: 12px;">If you didn't request this, please ignore this email.</p>
            </div>
            """,
            "textContent": f"""
            Reset Your Password
            
            Hello {user.name},
            
            You requested to reset your password. Click the link below to set a new password:
            
            {reset_url}
            
            This link will expire in 1 hour.
            
            If you didn't request this, please ignore this email.
            """
        }
        
        headers = {
            'accept': 'application/json',
            'api-key': self.api_key,
            'content-type': 'application/json'
        }
        
        try:
            response = requests.post(
                self.api_url,
                headers=headers,
                data=json.dumps(email_data),
                timeout=10
            )
            
            if response.status_code == 201:
                response_data = response.json()
                return {
                    'success': True,
                    'message_id': response_data.get('messageId'),
                    'method': 'brevo_api',
                    'to': user.email
                }
            else:
                error_data = response.json() if response.text else {'error': 'Unknown error'}
                return {
                    'success': False,
                    'error': f"Brevo API error {response.status_code}: {error_data}",
                    'method': 'brevo_api',
                    'status_code': response.status_code
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"Error sending password reset email: {str(e)}",
                'method': 'brevo_api'
            }
    
    def send_otp_email(self, email, otp_code, otp_type='login'):
        """
        Send OTP email using Brevo API
        
        Args:
            email: Email address to send to
            otp_code: OTP code to send
            otp_type: Type of OTP (login, 2fa, verification)
            
        Returns:
            dict: API response or error information
        """
        
        if not self.api_key or self.api_key == 'your-brevo-api-key-here':
            return {
                'success': False,
                'error': 'Brevo API key not configured',
                'fallback': 'smtp'
            }
        
        # Determine email content based on OTP type
        if otp_type == 'login':
            subject = "Your Login Code"
            greeting = "Your secure login code"
        elif otp_type == '2fa':
            subject = "Two-Factor Authentication Code"
            greeting = "Your two-factor authentication code"
        elif otp_type == 'verification':
            subject = "Account Verification Code"
            greeting = "Your account verification code"
        else:
            subject = "Your Security Code"
            greeting = "Your security code"
        
        email_data = {
            "sender": {
                "name": "Secure Backend",
                "email": self.from_email
            },
            "to": [
                {
                    "email": email,
                    "name": email.split('@')[0]  # Use part before @ as name
                }
            ],
            "subject": subject,
            "htmlContent": f"""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                    <h1 style="margin: 0; font-size: 28px;">🔐 Secure Access</h1>
                </div>
                <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
                    <h2 style="color: #333; margin-top: 0;">{greeting}</h2>
                    <p style="color: #666; font-size: 16px; line-height: 1.6;">
                        Use the following code to complete your authentication:
                    </p>
                    <div style="background: #fff; border: 2px solid #667eea; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0;">
                        <div style="font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px; font-family: 'Courier New', monospace;">
                            {otp_code}
                        </div>
                    </div>
                    <p style="color: #666; font-size: 14px; margin-bottom: 0;">
                        This code will expire in 10 minutes. If you didn't request this code, please ignore this email.
                    </p>
                    <hr style="border: none; border-top: 1px solid #e9ecef; margin: 20px 0;">
                    <p style="color: #999; font-size: 12px; margin-bottom: 0;">
                        This is an automated message from SecureApp. Please do not reply to this email.
                    </p>
                </div>
            </div>
            """,
            "textContent": f"""
            {greeting.upper()}

            Use the following code to complete your authentication:

            {otp_code}

            This code will expire in 10 minutes.
            
            If you didn't request this code, please ignore this email.

            ---
            This is an automated message from SecureApp.
            """
        }
        
        return self._send_via_brevo_api(email_data, f"OTP to {email}")
    
    def _send_via_brevo_api(self, email_data, fallback_info):
        """
        Send email using Brevo API, with fallback to terminal in case of error
        
        Args:
            email_data: Prepared email data
            fallback_info: Information for fallback action
            
        Returns:
            dict: API response or error information
        """
        
        # Set up headers
        headers = {
            'accept': 'application/json',
            'api-key': self.api_key,
            'content-type': 'application/json'
        }
        
        try:
            # Send request to Brevo API
            response = requests.post(
                self.api_url,
                headers=headers,
                data=json.dumps(email_data),
                timeout=10
            )
            
            if response.status_code == 201:
                response_data = response.json()
                return {
                    'success': True,
                    'message_id': response_data.get('messageId'),
                    'method': 'brevo_api',
                    'to': email_data['to'][0]['email']
                }
            else:
                error_data = response.json() if response.text else {'error': 'Unknown error'}
                return {
                    'success': False,
                    'error': f"Brevo API error {response.status_code}: {error_data}",
                    'method': 'brevo_api',
                    'status_code': response.status_code
                }
                
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': f"Request error: {str(e)}",
                'method': 'brevo_api'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"Unexpected error: {str(e)}",
                'method': 'brevo_api'
            }

    def send_disable_2fa_email(self, user, disable_token, reason='not_specified'):
        """
        Send 2FA disable confirmation email using Brevo API
        
        Args:
            user: User model instance
            disable_token: 2FA disable confirmation token
            reason: Reason for disabling 2FA
            
        Returns:
            dict: API response or error information
        """
        
        if not self.api_key or self.api_key == 'your-brevo-api-key-here':
            return {
                'success': False,
                'error': 'Brevo API key not configured',
                'fallback': 'terminal'
            }
        
        confirm_url = f"{env('FRONTEND_URL', 'http://localhost:4200')}/auth/disable-2fa?token={disable_token}"
        
        # Format reason for display
        reason_display = {
            'recovery_codes_exhausted': 'Recovery codes exhausted',
            'lost_device': 'Lost device',
            'other': 'Other reason',
            'not_specified': 'Not specified'
        }.get(reason, 'Not specified')
        
        from datetime import datetime
        
        email_data = {
            "sender": {
                "name": "Secure Backend",
                "email": self.from_email
            },
            "to": [
                {
                    "email": user.email,
                    "name": user.name or user.email
                }
            ],
            "subject": "🔐 Confirm 2FA Disable Request",
            "htmlContent": f"""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #dc3545;">🔐 Disable Two-Factor Authentication</h2>
                <p>Hello {user.name or user.email},</p>
                <p>You requested to disable two-factor authentication on your account.</p>
                
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <p><strong>Request Details:</strong></p>
                    <p>• Email: {user.email}</p>
                    <p>• Reason: {reason_display}</p>
                    <p>• Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
                </div>
                
                <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <p style="margin: 0; color: #856404;"><strong>⚠️ Security Notice:</strong></p>
                    <p style="margin: 5px 0 0 0; color: #856404;">Disabling 2FA will reduce your account security. Only proceed if you initiated this request.</p>
                </div>
                
                <p>To confirm and disable 2FA, click the button below:</p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{confirm_url}"
                       style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                        Confirm Disable 2FA
                    </a>
                </div>
                <p>If the button doesn't work, copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #666;">{confirm_url}</p>
                
                <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <p style="margin: 0; color: #721c24;"><strong>⏰ Important:</strong></p>
                    <p style="margin: 5px 0 0 0; color: #721c24;">This confirmation link will expire in 1 hour for security reasons.</p>
                </div>
                
                <p style="color: #666; font-size: 12px;">If you didn't request to disable 2FA, please ignore this email and ensure your account is secure.</p>
            </div>
            """,
            "textContent": f"""
            Disable Two-Factor Authentication
            
            Hello {user.name or user.email},
            
            You requested to disable two-factor authentication on your account.
            
            Request Details:
            • Email: {user.email}
            • Reason: {reason_display}
            • Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}
            
            ⚠️  Security Notice: Disabling 2FA will reduce your account security. Only proceed if you initiated this request.
            
            To confirm and disable 2FA, please click the link below:
            
            {confirm_url}
            
            ⏰ Important: This confirmation link will expire in 1 hour for security reasons.
            
            If you didn't request to disable 2FA, please ignore this email and ensure your account is secure.
            """
        }
        
        headers = {
            'accept': 'application/json',
            'api-key': self.api_key,
            'content-type': 'application/json'
        }
        
        try:
            response = requests.post(
                self.api_url,
                headers=headers,
                data=json.dumps(email_data),
                timeout=10
            )
            
            if response.status_code == 201:
                response_data = response.json()
                return {
                    'success': True,
                    'message_id': response_data.get('messageId'),
                    'method': 'brevo_api',
                    'to': user.email
                }
            else:
                error_data = response.json() if response.text else {'error': 'Unknown error'}
                return {
                    'success': False,
                    'error': f"Brevo API error {response.status_code}: {error_data}",
                    'method': 'brevo_api',
                    'status_code': response.status_code
                }
                
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': f"Request error: {str(e)}",
                'method': 'brevo_api'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"Unexpected error: {str(e)}",
                'method': 'brevo_api'
            }

    def send_account_deletion_confirmation_email(self, user, deletion_record, preferences):
        """
        Send account deletion confirmation email using Brevo API
        
        Args:
            user: User model instance
            deletion_record: AccountDeletionRecord instance
            preferences: Dictionary of deletion preferences
            
        Returns:
            dict: API response or error information
        """
        
        if not self.api_key or self.api_key == 'your-brevo-api-key-here':
            return {
                'success': False,
                'error': 'Brevo API key not configured',
                'fallback': 'terminal'
            }
        
        confirmation_url = f"{env('FRONTEND_URL', 'http://localhost:4200')}/account/confirm-deletion?token={deletion_record.confirmation_token}"
        
        # Build preserved data text
        preserved_items = []
        if preferences.get('preservePaymentData', False):
            preserved_items.append('Payment data')
        if preferences.get('preserveTransactionHistory', False):
            preserved_items.append('Transaction history')
        if preferences.get('preserveProfileData', False):
            preserved_items.append('Profile data')
        if preferences.get('preserveSecurityLogs', False):
            preserved_items.append('Security logs')
        
        preserved_text = ', '.join(preserved_items) if preserved_items else 'None'
        
        # Prepare email data in Brevo API format
        email_data = {
            "sender": {
                "name": "Secure Backend",
                "email": self.from_email
            },
            "to": [
                {
                    "email": user.email,
                    "name": getattr(user, 'name', 'User')
                }
            ],
            "subject": "⚠️ Confirm Account Deletion Request",
            "htmlContent": f"""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
                <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <h2 style="color: #dc3545; text-align: center; margin-bottom: 20px;">🗑️ Account Deletion Confirmation Required</h2>
                    
                    <p style="color: #333; font-size: 16px;">Hello <strong>{getattr(user, 'name', 'User')}</strong>,</p>
                    
                    <p style="color: #333; font-size: 14px; line-height: 1.6;">
                        You have requested to delete your account. Please review the details below and confirm your decision:
                    </p>
                    
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #dc3545;">
                        <h3 style="color: #333; margin-top: 0;">⚙️ Deletion Preferences</h3>
                        <ul style="color: #555; line-height: 1.8;">
                            <li><strong>Payment data:</strong> {'✅ Preserve' if preferences.get('preservePaymentData', False) else '❌ Delete'}</li>
                            <li><strong>Transaction history:</strong> {'✅ Preserve' if preferences.get('preserveTransactionHistory', False) else '❌ Delete'}</li>
                            <li><strong>Profile data:</strong> {'✅ Preserve' if preferences.get('preserveProfileData', False) else '❌ Delete'}</li>
                            <li><strong>Security logs:</strong> {'✅ Preserve' if preferences.get('preserveSecurityLogs', False) else '❌ Delete'}</li>
                        </ul>
                        <p style="color: #555; margin-bottom: 0;"><strong>Data preserved:</strong> {preserved_text}</p>
                        <p style="color: #555; margin-bottom: 0;"><strong>Retention period:</strong> {preferences.get('customRetentionPeriod', 30)} days</p>
                        {f"<p style='color: #555; margin-bottom: 0;'><strong>Reason:</strong> {preferences.get('reason', 'Not specified')}</p>" if preferences.get('reason') else ''}
                    </div>

                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{confirmation_url}" 
                           style="background: linear-gradient(135deg, #dc3545, #c82333); color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; font-size: 16px; box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);">
                            🗑️ Confirm Account Deletion
                        </a>
                    </div>
                    
                    <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 6px; margin: 20px 0;">
                        <p style="color: #856404; margin: 0; font-size: 14px;">
                            ⚠️ <strong>Warning:</strong> This action cannot be undone. Once confirmed, your account will be permanently deleted according to your preferences above.
                        </p>
                    </div>
                    
                    <p style="color: #666; font-size: 13px; line-height: 1.6;">
                        ⏰ <strong>Important:</strong> This confirmation link expires in 24 hours.<br>
                        If you didn't request this deletion, please ignore this email or contact our support team immediately.
                    </p>
                    
                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                    
                    <p style="color: #999; font-size: 12px; text-align: center;">
                        This email was sent regarding your account deletion request.<br>
                        For security reasons, please do not share this confirmation link with anyone.
                    </p>
                </div>
            </div>
            """,
            "textContent": f"""
            Account Deletion Confirmation Required
            
            Hello {getattr(user, 'name', 'User')},
            
            You have requested to delete your account. Please review the details below:
            
            Deletion Preferences:
            - Payment data: {'Preserve' if preferences.get('preservePaymentData', False) else 'Delete'}
            - Transaction history: {'Preserve' if preferences.get('preserveTransactionHistory', False) else 'Delete'}
            - Profile data: {'Preserve' if preferences.get('preserveProfileData', False) else 'Delete'}
            - Security logs: {'Preserve' if preferences.get('preserveSecurityLogs', False) else 'Delete'}
            
            Data preserved: {preserved_text}
            Retention period: {preferences.get('customRetentionPeriod', 30)} days
            {f"Reason: {preferences.get('reason')}" if preferences.get('reason') else ''}
            
            To confirm your account deletion, click the link below:
            {confirmation_url}
            
            WARNING: This action cannot be undone. Once confirmed, your account will be permanently deleted according to your preferences above.
            
            This confirmation link expires in 24 hours.
            
            If you didn't request this deletion, please ignore this email or contact support immediately.
            """
        }
        
        return self._send_via_brevo_api(email_data, {
            'type': 'account_deletion_confirmation',
            'user_email': user.email,
            'user_name': getattr(user, 'name', 'User'),
            'deletion_id': deletion_record.deletion_id
        })

    def send_account_deletion_completed_email(self, user, preserved_data_summary):
        """
        Send account deletion completed email using Brevo API
        
        Args:
            user: User model instance
            preserved_data_summary: Dictionary of preserved data summary
            
        Returns:
            dict: API response or error information
        """
        
        if not self.api_key or self.api_key == 'your-brevo-api-key-here':
            return {
                'success': False,
                'error': 'Brevo API key not configured',
                'fallback': 'terminal'
            }
        
        # Prepare email data in Brevo API format
        email_data = {
            "sender": {
                "name": "Secure Backend",
                "email": self.from_email
            },
            "to": [
                {
                    "email": user.email,
                    "name": getattr(user, 'name', 'User')
                }
            ],
            "subject": "✅ Account Deletion Completed",
            "htmlContent": f"""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
                <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <h2 style="color: #28a745; text-align: center; margin-bottom: 20px;">✅ Account Deletion Completed</h2>
                    
                    <p style="color: #333; font-size: 16px;">Hello <strong>{getattr(user, 'name', 'User')}</strong>,</p>
                    
                    <p style="color: #333; font-size: 14px; line-height: 1.6;">
                        Your account has been successfully deleted as requested. Thank you for using our service.
                    </p>
                    
                    <div style="background: #d4edda; padding: 20px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #28a745;">
                        <h3 style="color: #333; margin-top: 0;">📊 Data Summary</h3>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; color: #495057; white-space: pre-wrap; overflow-x: auto;">
{str(preserved_data_summary) if preserved_data_summary else 'No data preserved'}
                        </div>
                    </div>
                    
                    <div style="background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 6px; margin: 20px 0;">
                        <p style="color: #0c5460; margin: 0; font-size: 14px;">
                            💡 <strong>Note:</strong> If you have preserved data, you can restore it when signing up again with this email address.
                        </p>
                    </div>
                    
                    <p style="color: #666; font-size: 13px; line-height: 1.6; text-align: center;">
                        If you have any questions or need assistance, please contact our support team.
                    </p>
                    
                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                    
                    <p style="color: #999; font-size: 12px; text-align: center;">
                        This email confirms that your account deletion has been completed.<br>
                        For privacy and security, this information will not be retained beyond the required legal period.
                    </p>
                </div>
            </div>
            """,
            "textContent": f"""
            Account Deletion Completed
            
            Hello {getattr(user, 'name', 'User')},
            
            Your account has been successfully deleted as requested. Thank you for using our service.
            
            Data Summary:
            {str(preserved_data_summary) if preserved_data_summary else 'No data preserved'}
            
            Note: If you have preserved data, you can restore it when signing up again with this email address.
            
            If you have any questions or need assistance, please contact our support team.
            """
        }
        
        return self._send_via_brevo_api(email_data, {
            'type': 'account_deletion_completed',
            'user_email': user.email,
            'user_name': getattr(user, 'name', 'User')
        })
