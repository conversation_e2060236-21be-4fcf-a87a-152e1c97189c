#!/usr/bin/env python3
"""
Test script to debug 422 errors on signup and change-password endpoints
"""

import requests
import json
import sys

# API base URL
BASE_URL = "http://localhost:3002/api"

def test_signup():
    """Test signup endpoint with sample data"""
    print("🧪 Testing /auth/signup endpoint...")
    
    signup_data = {
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "confirmPassword": "TestPassword123!"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/signup", 
                               json=signup_data, 
                               headers=headers,
                               timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        try:
            response_json = response.json()
            print(f"Response Body: {json.dumps(response_json, indent=2)}")
        except:
            print(f"Response Text: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    
    print("\n" + "="*50 + "\n")

def test_change_password():
    """Test change-password endpoint"""
    print("🧪 Testing /auth/change-password endpoint...")
      # First we need to login to get a token
    login_data = {
        "email": "<EMAIL>",
        "password": "TestPassword123!"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }
    
    try:
        # Login first
        print("🔐 Attempting to login first...")
        login_response = requests.post(f"{BASE_URL}/auth/login", 
                                     json=login_data, 
                                     headers=headers,
                                     timeout=30)
        
        print(f"Login Status Code: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get('token')
            
            if token:
                print(f"✅ Login successful, got token")
                  # Now test change password
                change_password_data = {
                    "currentPassword": "TestPassword123!",
                    "newPassword": "NewPassword123!"
                }
                
                auth_headers = {
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {token}',
                    'Origin': 'http://localhost:4200'
                }
                
                response = requests.post(f"{BASE_URL}/auth/change-password", 
                                       json=change_password_data, 
                                       headers=auth_headers,
                                       timeout=30)
                
                print(f"Change Password Status Code: {response.status_code}")
                print(f"Response Headers: {dict(response.headers)}")
                
                try:
                    response_json = response.json()
                    print(f"Response Body: {json.dumps(response_json, indent=2)}")
                except:
                    print(f"Response Text: {response.text}")
            else:
                print("❌ No token received from login")
        else:
            print(f"❌ Login failed: {login_response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    
    print("\n" + "="*50 + "\n")

def test_options_requests():
    """Test OPTIONS requests for CORS"""
    print("🧪 Testing OPTIONS requests for CORS...")
    
    endpoints = ['/auth/signup', '/auth/change-password']
    
    headers = {
        'Origin': 'http://localhost:4200',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type, Authorization'
    }
    
    for endpoint in endpoints:
        try:
            response = requests.options(f"{BASE_URL}{endpoint}", 
                                      headers=headers,
                                      timeout=30)
            
            print(f"OPTIONS {endpoint}:")
            print(f"  Status Code: {response.status_code}")
            print(f"  CORS Headers: {dict(response.headers)}")
            
        except requests.exceptions.RequestException as e:
            print(f"❌ OPTIONS {endpoint} failed: {e}")
    
    print("\n" + "="*50 + "\n")

if __name__ == "__main__":
    print("🚀 Starting API endpoint tests...")
    print("="*60)
    
    # Test CORS first
    test_options_requests()
    
    # Test signup
    test_signup()
    
    # Test change password
    test_change_password()
    
    print("✅ Test completed!")
