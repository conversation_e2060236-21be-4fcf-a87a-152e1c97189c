#!/usr/bin/env python3
"""
Brevo SMTP Diagnostic and Solution Guide
"""

def print_brevo_troubleshooting():
    """Print comprehensive Brevo troubleshooting guide"""
    
    print("🚨 Brevo SMTP Authentication Failed (535 Error)")
    print("=" * 50)
    
    print("📋 Possible Causes & Solutions:")
    print()
    
    print("1. 🔑 INCORRECT CREDENTIALS")
    print("   Problem: API key or username is wrong")
    print("   Solution:")
    print("   - Login to your Brevo account")
    print("   - Go to 'SMTP & API' → 'SMTP'")
    print("   - Copy the exact login and password")
    print("   - Make sure you're using SMTP credentials, not API key")
    print()
    
    print("2. 📧 SENDER EMAIL NOT VERIFIED")
    print("   Problem: The 'from' email isn't verified in Brevo")
    print("   Solution:")
    print("   - In Brevo, go to 'Senders & IP'")
    print("   - Add and verify your sender email")
    print("   - Wait for verification to complete")
    print()
    
    print("3. 🏢 ACCOUNT LIMITATIONS")
    print("   Problem: Free account limits or restrictions")
    print("   Solution:")
    print("   - Check your Brevo dashboard for account status")
    print("   - Verify you haven't exceeded sending limits")
    print("   - Upgrade account if needed")
    print()
    
    print("4. 🌐 SMTP SETTINGS MISMATCH")
    print("   Problem: Wrong host/port/encryption")
    print("   Solution:")
    print("   Current settings:")
    print("   - Host: smtp-relay.brevo.com")
    print("   - Port: 587")
    print("   - Encryption: TLS")
    print("   Try alternative:")
    print("   - Port: 25 (no encryption)")
    print("   - Port: 465 (SSL)")
    print()
    
    print("5. 💡 QUICK WORKAROUNDS")
    print("   A) Use Gmail SMTP instead:")
    print("      MAIL_HOST=smtp.gmail.com")
    print("      MAIL_PORT=587")
    print("      MAIL_USERNAME=<EMAIL>")
    print("      MAIL_PASSWORD=your-app-password")
    print()
    print("   B) Use Mailgun service:")
    print("      MAIL_DRIVER=mailgun")
    print("      MAILGUN_DOMAIN=your-domain")
    print("      MAILGUN_SECRET=your-key")
    print()
    print("   C) Return to terminal driver (development):")
    print("      MAIL_DRIVER=terminal")
    print()
    
    print("🔧 IMMEDIATE ACTIONS TO TRY:")
    print("=" * 30)
    print("1. Double-check Brevo username/password in .env")
    print("2. Verify sender email in Brevo dashboard")
    print("3. Try port 25 instead of 587")
    print("4. Consider switching to Gmail for testing")
    print()
    
    print("📋 CURRENT .ENV SHOULD LOOK LIKE:")
    print("MAIL_DRIVER=smtp")
    print("MAIL_HOST=smtp-relay.brevo.com")
    print("MAIL_PORT=587")
    print("MAIL_ENCRYPTION=tls")
    print("MAIL_USERNAME=[exact-brevo-smtp-login]")
    print("MAIL_PASSWORD=[exact-brevo-smtp-password]")
    print("MAIL_FROM=[verified-sender-email]")

def create_gmail_alternative():
    """Create Gmail SMTP alternative configuration"""
    
    print("\n🔄 Gmail SMTP Alternative Setup")
    print("=" * 35)
    
    gmail_config = """
# Gmail SMTP Configuration (Alternative to Brevo)
MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_ENCRYPTION=tls
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password  # NOT your regular Gmail password!
MAIL_FROM=<EMAIL>

# Steps to get Gmail App Password:
# 1. Enable 2FA on your Gmail account
# 2. Go to Google Account settings
# 3. Security → App passwords
# 4. Generate app password for 'Mail'
# 5. Use that 16-character password above
"""
    
    with open("gmail_smtp_config.txt", "w") as f:
        f.write(gmail_config)
    
    print("✅ Created 'gmail_smtp_config.txt' with Gmail setup instructions")
    print("📝 Follow the steps in the file to set up Gmail SMTP")

if __name__ == "__main__":
    print_brevo_troubleshooting()
    create_gmail_alternative()
    
    print("\n🎯 NEXT STEPS:")
    print("1. Check your Brevo account and credentials")
    print("2. Try the Gmail alternative if Brevo continues to fail")
    print("3. Test registration again after updating .env")
    print("4. Restart Masonite server after any .env changes")
