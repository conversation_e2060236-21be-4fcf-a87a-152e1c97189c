"use strict";(self.webpackChunksecure_frontend=self.webpackChunksecure_frontend||[]).push([[937],{3890:(h,_,r)=>{r.d(_,{z:()=>p});var m=r(2306),u=r(5312),s=r(8564);let p=(()=>{class l{constructor(t){this.http=t,this.apiUrl=`${u.c.apiUrl}/account`}requestAccountDeletion(t){const o=this.getAuthHeaders();return this.http.post(`${this.apiUrl}/request-deletion`,t,{headers:o})}getDeletionStatus(){const t=this.getAuthHeaders();return this.http.get(`${this.apiUrl}/deletion-status`,{headers:t})}cancelDeletion(){const t=this.getAuthHeaders();return this.http.post(`${this.apiUrl}/cancel-deletion`,{},{headers:t})}confirmDeletion(t){return this.http.post(`${this.apiUrl}/confirm-deletion`,{token:t})}checkPreservedData(t){return this.http.get(`${this.apiUrl}/check-preserved-data/${encodeURIComponent(t)}`)}restoreData(t,o,d){return this.http.post(`${this.apiUrl}/restore-data`,{userId:t,email:o,...d})}deletePreservedData(t){return this.http.request("delete",`${this.apiUrl}/delete-preserved-data`,{body:{email:t}})}exportUserData(){const t=this.getAuthHeaders();return this.http.get(`${this.apiUrl}/export-data`,{headers:t,responseType:"blob"})}requestDataExport(){const t=this.getAuthHeaders();return this.http.post(`${this.apiUrl}/request-export`,{},{headers:t})}getAuthHeaders(){const t=localStorage.getItem("authToken");return new m.Lr({Authorization:`Bearer ${t}`,"Content-Type":"application/json"})}validateDeletionPreferences(t){const o=[];return void 0!==t.customRetentionPeriod&&(t.customRetentionPeriod<1||t.customRetentionPeriod>365)&&o.push("Retention period must be between 1 and 365 days"),t.preservePaymentData||t.preserveTransactionHistory||t.preserveProfileData||t.preserveSecurityLogs||console.warn("No data will be preserved - account deletion will be permanent"),{isValid:0===o.length,errors:o}}calculateExpiryDate(t=30){const o=new Date;return o.setDate(o.getDate()+t),o}formatPreservedDataSummary(t){const o=[];return t.paymentRecords>0&&o.push(`${t.paymentRecords} payment record${1===t.paymentRecords?"":"s"}`),t.transactionHistory>0&&o.push(`${t.transactionHistory} transaction${1===t.transactionHistory?"":"s"}`),t.profileBackup&&o.push("Profile backup"),t.securityEvents>0&&o.push(`${t.securityEvents} security event${1===t.securityEvents?"":"s"}`),t.loginHistory>0&&o.push(`${t.loginHistory} login record${1===t.loginHistory?"":"s"}`),o}getRecommendedPreferences(t){return{preservePaymentData:!0,preserveTransactionHistory:!0,preserveProfileData:!1,preserveSecurityLogs:!1,customRetentionPeriod:90,reason:""}}static#t=this.\u0275fac=function(o){return new(o||l)(s.KVO(m.Qq))};static#e=this.\u0275prov=s.jDH({token:l,factory:l.\u0275fac,providedIn:"root"})}return l})()},9937:(h,_,r)=>{r.r(_),r.d(_,{DeletionConfirmationComponent:()=>R});var m=r(467),u=r(6396),s=r(5596),p=r(8834),l=r(1074),g=r(9183),t=r(7241),o=r(8564),d=r(5333),v=r(8822),D=r(3890),E=r(4796);function C(n,c){1&n&&(t.j41(0,"div",10),t.nrm(1,"mat-spinner",11),t.j41(2,"p"),t.EFF(3,"Processing your account deletion confirmation..."),t.k0s()())}function P(n,c){if(1&n&&(t.j41(0,"li"),t.EFF(1),t.k0s()),2&n){const e=c.$implicit;t.R7$(),t.JRh(e)}}function F(n,c){if(1&n&&(t.j41(0,"div",15)(1,"h4"),t.EFF(2,"Preserved Data Summary:"),t.k0s(),t.j41(3,"ul"),t.DNE(4,P,2,1,"li",16),t.k0s(),t.j41(5,"p",17)(6,"mat-icon"),t.EFF(7,"info"),t.k0s(),t.EFF(8," Your preserved data will be automatically deleted after the retention period expires. "),t.k0s()()),2&n){const e=t.XpG(2);t.R7$(4),t.Y8G("ngForOf",e.preservedDataItems)}}function y(n,c){if(1&n&&(t.j41(0,"div",12)(1,"p")(2,"strong"),t.EFF(3,"Your account has been successfully scheduled for deletion."),t.k0s()(),t.j41(4,"p"),t.EFF(5,"Deletion ID: "),t.j41(6,"code"),t.EFF(7),t.k0s()(),t.DNE(8,F,9,1,"div",13),t.j41(9,"div",14)(10,"h4"),t.EFF(11,"What happens next:"),t.k0s(),t.j41(12,"ul")(13,"li"),t.EFF(14,"Your account is now permanently deleted"),t.k0s(),t.j41(15,"li"),t.EFF(16,"You will no longer be able to log in"),t.k0s(),t.j41(17,"li"),t.EFF(18,"If you preserved any data, you'll receive an email before it's permanently deleted"),t.k0s(),t.j41(19,"li"),t.EFF(20,"You can create a new account at any time using the same email address"),t.k0s()()()()),2&n){const e=t.XpG();t.R7$(7),t.JRh(e.deletionId),t.R7$(),t.Y8G("ngIf",e.preservedDataSummary)}}function M(n,c){if(1&n&&(t.j41(0,"p"),t.EFF(1),t.k0s()),2&n){const e=t.XpG(2);t.R7$(),t.JRh(e.errorMessage)}}function k(n,c){if(1&n&&(t.j41(0,"div",18)(1,"p")(2,"strong"),t.EFF(3,"We couldn't process your deletion confirmation."),t.k0s()(),t.DNE(4,M,2,1,"p",19),t.j41(5,"p"),t.EFF(6,"This could happen if:"),t.k0s(),t.j41(7,"ul")(8,"li"),t.EFF(9,"The confirmation link has expired"),t.k0s(),t.j41(10,"li"),t.EFF(11,"The confirmation link has already been used"),t.k0s(),t.j41(12,"li"),t.EFF(13,"There was a technical error"),t.k0s()()()),2&n){const e=t.XpG();t.R7$(4),t.Y8G("ngIf",e.errorMessage)}}function O(n,c){1&n&&(t.j41(0,"div",18)(1,"p")(2,"strong"),t.EFF(3,"Invalid confirmation link."),t.k0s()(),t.j41(4,"p"),t.EFF(5,"The confirmation token is missing or invalid. Please check your email and try again with the correct confirmation link."),t.k0s()())}function I(n,c){if(1&n){const e=t.RV6();t.j41(0,"button",20),t.bIt("click",function(){o.eBV(e);const i=t.XpG();return o.Njj(i.goToLogin())}),t.j41(1,"mat-icon"),t.EFF(2,"login"),t.k0s(),t.EFF(3," Create New Account "),t.k0s()}}function T(n,c){if(1&n){const e=t.RV6();t.j41(0,"button",9),t.bIt("click",function(){o.eBV(e);const i=t.XpG();return o.Njj(i.goToSupport())}),t.j41(1,"mat-icon"),t.EFF(2,"help"),t.k0s(),t.EFF(3," Contact Support "),t.k0s()}}let R=(()=>{class n{constructor(e,a,i,f,j){this.route=e,this.router=a,this.snackBar=i,this.accountDeletionService=f,this.authService=j,this.status="loading",this.token=null,this.deletionId="",this.preservedDataSummary=null,this.preservedDataItems=[],this.errorMessage=""}ngOnInit(){this.token=this.route.snapshot.queryParamMap.get("token"),this.token?this.confirmDeletion():this.status="invalid"}confirmDeletion(){var e=this;return(0,m.A)(function*(){if(e.token)try{const a=yield e.accountDeletionService.confirmDeletion(e.token).toPromise();e.status="success",e.deletionId=a?.deletionId||"",e.preservedDataSummary=a?.preservedDataSummary,e.preservedDataSummary&&(e.preservedDataItems=e.accountDeletionService.formatPreservedDataSummary(e.preservedDataSummary)),e.authService.logout()}catch(a){console.error("Error confirming deletion:",a),e.status="error",e.errorMessage=a.error?.message||a.message||"An unexpected error occurred"}})()}getStatusIcon(){switch(this.status){case"loading":return"hourglass_empty";case"success":return"check_circle";case"error":return"error";case"invalid":return"warning";default:return"help"}}getStatusTitle(){switch(this.status){case"loading":return"Confirming Account Deletion";case"success":return"Account Deletion Confirmed";case"error":return"Deletion Confirmation Failed";case"invalid":return"Invalid Confirmation Link";default:return"Account Deletion"}}goToLogin(){this.router.navigate(["/auth/login"])}goToHome(){this.router.navigate(["/"])}goToSupport(){window.open("mailto:<EMAIL>?subject=Account Deletion Issue","_blank")}static#t=this.\u0275fac=function(a){return new(a||n)(t.rXU(d.nX),t.rXU(d.Ix),t.rXU(v.UG),t.rXU(D.z),t.rXU(E.u))};static#e=this.\u0275cmp=t.VBU({type:n,selectors:[["app-deletion-confirmation"]],decls:19,vars:9,consts:[[1,"confirmation-container"],[1,"confirmation-card"],[3,"color"],["class","loading-section",4,"ngIf"],["class","success-section",4,"ngIf"],["class","error-section",4,"ngIf"],["align","end"],["mat-raised-button","","color","primary",3,"click",4,"ngIf"],["mat-button","",3,"click",4,"ngIf"],["mat-button","",3,"click"],[1,"loading-section"],["diameter","40"],[1,"success-section"],["class","preserved-data-info",4,"ngIf"],[1,"next-steps"],[1,"preserved-data-info"],[4,"ngFor","ngForOf"],[1,"info-text"],[1,"error-section"],[4,"ngIf"],["mat-raised-button","","color","primary",3,"click"]],template:function(a,i){1&a&&(t.j41(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"mat-card-title")(4,"mat-icon",2),t.EFF(5),t.k0s(),t.EFF(6),t.k0s()(),t.j41(7,"mat-card-content"),t.DNE(8,C,4,0,"div",3)(9,y,21,2,"div",4)(10,k,14,1,"div",5)(11,O,6,0,"div",5),t.k0s(),t.j41(12,"mat-card-actions",6),t.DNE(13,I,4,0,"button",7)(14,T,4,0,"button",8),t.j41(15,"button",9),t.bIt("click",function(){return i.goToHome()}),t.j41(16,"mat-icon"),t.EFF(17,"home"),t.k0s(),t.EFF(18," Go to Home "),t.k0s()()()()),2&a&&(t.R7$(4),t.Y8G("color","loading"===i.status||"success"===i.status?"primary":"warn"),t.R7$(),t.SpI(" ",i.getStatusIcon()," "),t.R7$(),t.SpI(" ",i.getStatusTitle()," "),t.R7$(2),t.Y8G("ngIf","loading"===i.status),t.R7$(),t.Y8G("ngIf","success"===i.status),t.R7$(),t.Y8G("ngIf","error"===i.status),t.R7$(),t.Y8G("ngIf","invalid"===i.status),t.R7$(2),t.Y8G("ngIf","success"===i.status),t.R7$(),t.Y8G("ngIf","error"===i.status||"invalid"===i.status))},dependencies:[u.MD,u.Sq,u.bT,s.Hu,s.RN,s.YY,s.m2,s.MM,s.dh,p.Hl,p.$z,l.m_,l.An,g.D6,g.LG],styles:[".confirmation-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;padding:20px;background:linear-gradient(135deg,#f5f7fa,#c3cfe2)}.confirmation-card[_ngcontent-%COMP%]{max-width:600px;width:100%}.loading-section[_ngcontent-%COMP%]{text-align:center;padding:2rem 0}.loading-section[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin:0 auto 1rem}.success-section[_ngcontent-%COMP%], .error-section[_ngcontent-%COMP%]{padding:1rem 0}.preserved-data-info[_ngcontent-%COMP%]{background-color:#f8f9fa;padding:1rem;border-radius:8px;margin:1rem 0}.preserved-data-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin-top:0;color:#495057}.preserved-data-info[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:.5rem 0}.info-text[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-size:.9rem;color:#6c757d;margin-top:1rem}.next-steps[_ngcontent-%COMP%]{margin-top:1.5rem}.next-steps[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#495057;margin-bottom:.5rem}.next-steps[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{line-height:1.6}code[_ngcontent-%COMP%]{background-color:#f8f9fa;padding:.2rem .4rem;border-radius:4px;font-family:Courier New,monospace;font-size:.9rem}mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}mat-card-actions[_ngcontent-%COMP%]{gap:.5rem}"]})}return n})()}}]);