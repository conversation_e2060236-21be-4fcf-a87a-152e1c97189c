from masonite.mail import Mailable
from masonite.environment import env


class AccountDeletionCompleted(Mailable):
    """
    Account Deletion Completed Mailable
    Sent when account deletion is successfully completed
    """
    
    def __init__(self, user, preserved_data_summary):
        """Initialize with user and preserved data summary"""
        super().__init__()
        self.user = user
        self.preserved_data_summary = preserved_data_summary
    
    def build(self):
        """Build the account deletion completed email"""
        
        html_content = f"""
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
            <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h2 style="color: #28a745; text-align: center; margin-bottom: 20px;">✅ Account Deletion Completed</h2>
                
                <p style="color: #333; font-size: 16px;">Hello <strong>{getattr(self.user, 'name', 'User')}</strong>,</p>
                
                <p style="color: #333; font-size: 14px; line-height: 1.6;">
                    Your account has been successfully deleted as requested. Thank you for using our service.
                </p>
                
                <div style="background: #d4edda; padding: 20px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #28a745;">
                    <h3 style="color: #333; margin-top: 0;">📊 Data Summary</h3>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; color: #495057; white-space: pre-wrap; overflow-x: auto;">
{str(self.preserved_data_summary) if self.preserved_data_summary else 'No data preserved'}
                    </div>
                </div>
                
                <div style="background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 6px; margin: 20px 0;">
                    <p style="color: #0c5460; margin: 0; font-size: 14px;">
                        💡 <strong>Note:</strong> If you have preserved data, you can restore it when signing up again with this email address.
                    </p>
                </div>
                
                <p style="color: #666; font-size: 13px; line-height: 1.6; text-align: center;">
                    If you have any questions or need assistance, please contact our support team.
                </p>
                
                <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                
                <p style="color: #999; font-size: 12px; text-align: center;">
                    This email confirms that your account deletion has been completed.<br>
                    For privacy and security, this information will not be retained beyond the required legal period.
                </p>
            </div>
        </div>
        """
        
        text_content = f"""
        Account Deletion Completed
        
        Hello {getattr(self.user, 'name', 'User')},
        
        Your account has been successfully deleted as requested. Thank you for using our service.
        
        Data Summary:
        {str(self.preserved_data_summary) if self.preserved_data_summary else 'No data preserved'}
        
        Note: If you have preserved data, you can restore it when signing up again with this email address.
        
        If you have any questions or need assistance, please contact our support team.
        """
        
        return self.to(self.user.email).subject("✅ Account Deletion Completed").view_data({
            'html_content': html_content,
            'text_content': text_content
        }).text(text_content).html(html_content)
