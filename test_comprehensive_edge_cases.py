#!/usr/bin/env python3
"""
Comprehensive test for all edge cases in data preservation and restoration
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3002/api"

def test_scenario(scenario_name, preserve_payment, preserve_transactions, preserve_profile, preserve_security, restore_preference=None):
    """Test a specific scenario"""
    email = f"test.edge.{int(time.time())}.{scenario_name.lower().replace(' ', '')}@example.com"
    password = "TestPassword123!"
    
    print(f"\n🧪 Testing: {scenario_name}")
    print("=" * 60)
    
    # Step 1: Register user
    print("📝 Step 1: Registering user...")
    register_data = {
        "email": email,
        "password": password,
        "confirmPassword": password,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+**********"
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=register_data)
    if response.status_code not in [200, 201]:
        print(f"❌ Registration failed: {response.text}")
        return False
    
    user_data = response.json()
    user_id = user_data.get('userId') or user_data.get('user', {}).get('id')
    token = user_data.get('token')
    print(f"✅ User registered with ID: {user_id}")
    
    # Step 2: Request deletion with specific preferences
    print(f"\n📝 Step 2: Requesting deletion...")
    print(f"   - Preserve Payment Data: {preserve_payment}")
    print(f"   - Preserve Transactions: {preserve_transactions}")
    print(f"   - Preserve Profile Data: {preserve_profile}")
    print(f"   - Preserve Security Logs: {preserve_security}")
    
    deletion_data = {
        "preservePaymentData": preserve_payment,
        "preserveTransactionHistory": preserve_transactions,
        "preserveProfileData": preserve_profile,
        "preserveSecurityLogs": preserve_security,
        "customRetentionPeriod": 30,
        "reason": f"Testing {scenario_name}"
    }
    
    response = requests.post(f"{BASE_URL}/account/request-deletion", 
                           json=deletion_data,
                           headers={'Authorization': f'Bearer {token}'})
    
    if response.status_code != 200:
        print(f"❌ Deletion request failed: {response.text}")
        return False
    
    deletion_response = response.json()
    deletion_id = deletion_response['deletionId']
    confirmation_token = deletion_response['confirmationToken']
    print(f"✅ Deletion requested with ID: {deletion_id}")
    
    # Step 3: Confirm deletion
    print("\n📝 Step 3: Confirming deletion...")
    response = requests.post(f"{BASE_URL}/account/confirm-deletion", 
                           json={'token': confirmation_token})
    
    if response.status_code != 200:
        print(f"❌ Deletion confirmation failed: {response.text}")
        return False
    
    confirm_response = response.json()
    print("✅ Deletion confirmed")
    preserved_summary_from_deletion = confirm_response.get('preservedDataSummary', {})
    print(f"📄 Preserved data summary from deletion: {preserved_summary_from_deletion}")
    
    # Step 4: Check preserved data
    print("\n📝 Step 4: Checking preserved data...")
    response = requests.post(f"{BASE_URL}/account/check-preserved-data", 
                           json={'email': email})
    
    if response.status_code != 200:
        print(f"❌ Check preserved data failed: {response.text}")
        return False
    
    check_response = response.json()
    has_preserved = check_response.get('hasPreservedData', False)
    preserved_summary = check_response.get('preservedDataSummary', {})
    
    print(f"📊 Has preserved data: {has_preserved}")
    print(f"📊 Preserved data summary: {preserved_summary}")
    
    # Step 5: Test signup with specific restore preference
    print(f"\n📝 Step 5: Testing signup with restore preference: {restore_preference}")
    signup_data = {
        "email": email,
        "password": password,
        "confirmPassword": password,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+**********"
    }
    
    if restore_preference is not None:
        signup_data["restorePreservedData"] = restore_preference
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=signup_data)
    
    if response.status_code not in [200, 201]:
        print(f"❌ Second registration failed: {response.text}")
        return False
    
    signup_response = response.json()
    signup_has_preserved = signup_response.get('hasPreservedData', False)
    signup_preserved_summary = signup_response.get('preservedDataSummary', {})
    data_cleaned_up = signup_response.get('dataCleanedUp', False)
    preserved_data_restored = signup_response.get('preservedDataRestored', False)
    message = signup_response.get('message', '')
    
    print(f"📊 Signup response:")
    print(f"   - Has preserved data: {signup_has_preserved}")
    print(f"   - Preserved summary: {signup_preserved_summary}")
    print(f"   - Data cleaned up: {data_cleaned_up}")
    print(f"   - Data restored: {preserved_data_restored}")
    print(f"   - Message: {message}")
    
    # Step 6: Validate behavior
    print(f"\n📝 Step 6: Validating behavior...")
    
    # Calculate expected behavior
    any_data_preserved = preserve_payment or preserve_transactions or preserve_profile or preserve_security
    
    if not any_data_preserved:
        # No data should be preserved
        if has_preserved:
            print(f"❌ FAIL: No data should be preserved but found: {preserved_summary}")
            return False
        print("✅ PASS: No data preserved as expected")
        return True
    
    # Some data was preserved
    if not has_preserved:
        print(f"❌ FAIL: Expected preserved data but found none")
        return False
    
    # Check specific categories
    expected_categories = []
    if preserve_payment:
        expected_categories.append('payments')
    if preserve_transactions:
        expected_categories.append('transactions')
    if preserve_profile:
        expected_categories.append('profile')
    if preserve_security:
        expected_categories.append('security')
    
    for category in expected_categories:
        if category not in preserved_summary:
            print(f"❌ FAIL: Expected {category} to be preserved but it's not in summary")
            return False
    
    for category in preserved_summary:
        if category not in expected_categories:
            print(f"❌ FAIL: Unexpected category {category} found in preserved summary")
            return False
    
    print(f"✅ PASS: Correctly preserved only selected categories: {list(preserved_summary.keys())}")
    
    # Check restoration behavior
    if restore_preference is True:
        if not preserved_data_restored:
            print(f"❌ FAIL: Data should have been restored when explicitly requested")
            return False
        print("✅ PASS: Data was restored when explicitly requested")
    elif restore_preference is False:
        if preserved_data_restored:
            print(f"❌ FAIL: Data should NOT have been restored when explicitly declined")
            return False
        if not data_cleaned_up:
            print(f"❌ FAIL: Data should have been cleaned up when explicitly declined")
            return False
        print("✅ PASS: Data was not restored and was cleaned up when explicitly declined")
    else:  # restore_preference is None
        if preserved_data_restored:
            print(f"❌ FAIL: Data should NOT be automatically restored when no preference specified")
            return False
        if data_cleaned_up:
            print(f"❌ FAIL: Data should NOT be cleaned up when no preference specified")
            return False
        print("✅ PASS: Data was not automatically restored when no preference specified")
    
    return True

def main():
    """Run comprehensive edge case tests"""
    print("🚀 Starting Comprehensive Edge Case Tests")
    print("Testing all possible scenarios for data preservation and restoration")
    print("=" * 80)
    
    test_cases = [
        # Scenario name, preserve_payment, preserve_transactions, preserve_profile, preserve_security, restore_preference
        ("No Data Preservation", False, False, False, False, None),
        ("Only Payment Data", True, False, False, False, None),
        ("Only Profile Data", False, False, True, False, None),
        ("Payment + Profile", True, False, True, False, None),
        ("All Data Preserved", True, True, True, True, None),
        ("All Data + Explicit Restore", True, True, True, True, True),
        ("All Data + Explicit No Restore", True, True, True, True, False),
        ("Profile Only + Explicit Restore", False, False, True, False, True),
        ("Profile Only + Explicit No Restore", False, False, True, False, False),
    ]
    
    results = []
    
    for i, (name, payment, transactions, profile, security, restore) in enumerate(test_cases):
        print(f"\n{'='*20} Test {i+1}/{len(test_cases)} {'='*20}")
        success = test_scenario(name, payment, transactions, profile, security, restore)
        results.append({'scenario': name, 'success': success})
        
        if not success:
            print(f"❌ STOPPING: Test failed for scenario: {name}")
            break
        
        # Small delay to avoid rate limiting
        time.sleep(1)
    
    # Summary
    print("\n" + "=" * 80)
    print("🏁 COMPREHENSIVE TEST RESULTS SUMMARY")
    print("=" * 80)
    
    passed = 0
    failed = 0
    
    for result in results:
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        print(f"{status}: {result['scenario']}")
        if result['success']:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📊 Total: {len(results)} scenarios tested")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! Data preservation and restoration logic is working correctly!")
    else:
        print(f"\n⚠️ {failed} test(s) failed. Issues found in data preservation logic.")

if __name__ == "__main__":
    main()
