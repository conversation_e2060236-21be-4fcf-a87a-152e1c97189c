"use strict";(self.webpackChunksecure_frontend=self.webpackChunksecure_frontend||[]).push([[28],{357:(Q,k,l)=>{l.d(k,{M:()=>at,b:()=>et,c:()=>it,e:()=>q,h:()=>B,k:()=>S,l:()=>pt});var s=l(7241),o=l(8564),i=l(7705),m=l(6939),b=l(1497),I=l(5751),A=l(4466),O=l(9629),C=l(1413),D=l(9030),w=l(7035),c=l(7336),h=l(9172),f=l(282),E=l(8511),x=l(372);function _(a,r){}class y{viewContainerRef;injector;id;role="dialog";panelClass="";hasBackdrop=!0;backdropClass="";disableClose=!1;closePredicate;width="";height="";minWidth;minHeight;maxWidth;maxHeight;positionStrategy;data=null;direction;ariaDescribedBy=null;ariaLabelledBy=null;ariaLabel=null;ariaModal=!1;autoFocus="first-tabbable";restoreFocus=!0;scrollStrategy;closeOnNavigation=!0;closeOnDestroy=!0;closeOnOverlayDetachments=!0;disableAnimations=!1;providers;container;templateContext}let v=(()=>{class a extends m.lb{_elementRef=(0,o.WQX)(s.aKT);_focusTrapFactory=(0,o.WQX)(b.F);_config;_interactivityChecker=(0,o.WQX)(b.I);_ngZone=(0,o.WQX)(s.SKi);_focusMonitor=(0,o.WQX)(I.F);_renderer=(0,o.WQX)(s.sFG);_platform=(0,o.WQX)(A.P);_document=(0,o.WQX)(o.qQL,{optional:!0});_portalOutlet;_focusTrap=null;_elementFocusedBeforeDialogWasOpened=null;_closeInteractionType=null;_ariaLabelledByQueue=[];_changeDetectorRef=(0,o.WQX)(i.gRc);_injector=(0,o.WQX)(o.zZn);_isDestroyed=!1;constructor(){super(),this._config=(0,o.WQX)(y,{optional:!0})||new y,this._config.ariaLabelledBy&&this._ariaLabelledByQueue.push(this._config.ariaLabelledBy)}_addAriaLabelledBy(t){this._ariaLabelledByQueue.push(t),this._changeDetectorRef.markForCheck()}_removeAriaLabelledBy(t){const e=this._ariaLabelledByQueue.indexOf(t);e>-1&&(this._ariaLabelledByQueue.splice(e,1),this._changeDetectorRef.markForCheck())}_contentAttached(){this._initializeFocusTrap(),this._captureInitialFocus()}_captureInitialFocus(){this._trapFocus()}ngOnDestroy(){this._isDestroyed=!0,this._restoreFocus()}attachComponentPortal(t){this._portalOutlet.hasAttached();const e=this._portalOutlet.attachComponentPortal(t);return this._contentAttached(),e}attachTemplatePortal(t){this._portalOutlet.hasAttached();const e=this._portalOutlet.attachTemplatePortal(t);return this._contentAttached(),e}attachDomPortal=t=>{this._portalOutlet.hasAttached();const e=this._portalOutlet.attachDomPortal(t);return this._contentAttached(),e};_recaptureFocus(){this._containsFocus()||this._trapFocus()}_forceFocus(t,e){this._interactivityChecker.isFocusable(t)||(t.tabIndex=-1,this._ngZone.runOutsideAngular(()=>{const n=()=>{d(),g(),t.removeAttribute("tabindex")},d=this._renderer.listen(t,"blur",n),g=this._renderer.listen(t,"mousedown",n)})),t.focus(e)}_focusByCssSelector(t,e){let n=this._elementRef.nativeElement.querySelector(t);n&&this._forceFocus(n,e)}_trapFocus(t){this._isDestroyed||(0,s.mal)(()=>{const e=this._elementRef.nativeElement;switch(this._config.autoFocus){case!1:case"dialog":this._containsFocus()||e.focus(t);break;case!0:case"first-tabbable":this._focusTrap?.focusInitialElement(t)||this._focusDialogContainer(t);break;case"first-heading":this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role="heading"]',t);break;default:this._focusByCssSelector(this._config.autoFocus,t)}},{injector:this._injector})}_restoreFocus(){const t=this._config.restoreFocus;let e=null;if("string"==typeof t?e=this._document.querySelector(t):"boolean"==typeof t?e=t?this._elementFocusedBeforeDialogWasOpened:null:t&&(e=t),this._config.restoreFocus&&e&&"function"==typeof e.focus){const n=(0,O.c)(),d=this._elementRef.nativeElement;(!n||n===this._document.body||n===d||d.contains(n))&&(this._focusMonitor?(this._focusMonitor.focusVia(e,this._closeInteractionType),this._closeInteractionType=null):e.focus())}this._focusTrap&&this._focusTrap.destroy()}_focusDialogContainer(t){this._elementRef.nativeElement.focus?.(t)}_containsFocus(){const t=this._elementRef.nativeElement,e=(0,O.c)();return t===e||t.contains(e)}_initializeFocusTrap(){this._platform.isBrowser&&(this._focusTrap=this._focusTrapFactory.create(this._elementRef.nativeElement),this._document&&(this._elementFocusedBeforeDialogWasOpened=(0,O.c)()))}static \u0275fac=function(e){return new(e||a)};static \u0275cmp=s.VBU({type:a,selectors:[["cdk-dialog-container"]],viewQuery:function(e,n){if(1&e&&s.GBs(m.I3,7),2&e){let d;s.mGM(d=s.lsd())&&(n._portalOutlet=d.first)}},hostAttrs:["tabindex","-1",1,"cdk-dialog-container"],hostVars:6,hostBindings:function(e,n){2&e&&s.BMQ("id",n._config.id||null)("role",n._config.role)("aria-modal",n._config.ariaModal)("aria-labelledby",n._config.ariaLabel?null:n._ariaLabelledByQueue[0])("aria-label",n._config.ariaLabel)("aria-describedby",n._config.ariaDescribedBy||null)},features:[s.Vt3],decls:1,vars:0,consts:[["cdkPortalOutlet",""]],template:function(e,n){1&e&&s.DNE(0,_,0,0,"ng-template",0)},dependencies:[m.I3],styles:[".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\n"],encapsulation:2})}return a})();class p{overlayRef;config;componentInstance;componentRef;containerInstance;disableClose;closed=new C.B;backdropClick;keydownEvents;outsidePointerEvents;id;_detachSubscription;constructor(r,t){this.overlayRef=r,this.config=t,this.disableClose=t.disableClose,this.backdropClick=r.backdropClick(),this.keydownEvents=r.keydownEvents(),this.outsidePointerEvents=r.outsidePointerEvents(),this.id=t.id,this.keydownEvents.subscribe(e=>{e.keyCode===w.g&&!this.disableClose&&!(0,c.rp)(e)&&(e.preventDefault(),this.close(void 0,{focusOrigin:"keyboard"}))}),this.backdropClick.subscribe(()=>{!this.disableClose&&this._canClose()?this.close(void 0,{focusOrigin:"mouse"}):this.containerInstance._recaptureFocus?.()}),this._detachSubscription=r.detachments().subscribe(()=>{!1!==t.closeOnOverlayDetachments&&this.close()})}close(r,t){if(this._canClose(r)){const e=this.closed;this.containerInstance._closeInteractionType=t?.focusOrigin||"program",this._detachSubscription.unsubscribe(),this.overlayRef.dispose(),e.next(r),e.complete(),this.componentInstance=this.containerInstance=null}}updatePosition(){return this.overlayRef.updatePosition(),this}updateSize(r="",t=""){return this.overlayRef.updateSize({width:r,height:t}),this}addPanelClass(r){return this.overlayRef.addPanelClass(r),this}removePanelClass(r){return this.overlayRef.removePanelClass(r),this}_canClose(r){const t=this.config;return!!this.containerInstance&&(!t.closePredicate||t.closePredicate(r,t,this.componentInstance))}}const M=new o.nKC("DialogScrollStrategy",{providedIn:"root",factory:()=>{const a=(0,o.WQX)(o.zZn);return()=>(0,f.s)(a)}}),P=new o.nKC("DialogData"),ot=new o.nKC("DefaultDialogConfig");function st(a){const r=(0,o.vPA)(a),t=new s.bkB;return{valueSignal:r,get value(){return r()},change:t,ngOnDestroy(){t.complete()}}}let z=(()=>{class a{_injector=(0,o.WQX)(o.zZn);_defaultOptions=(0,o.WQX)(ot,{optional:!0});_parentDialog=(0,o.WQX)(a,{optional:!0,skipSelf:!0});_overlayContainer=(0,o.WQX)(f.O);_idGenerator=(0,o.WQX)(E._);_openDialogsAtThisLevel=[];_afterAllClosedAtThisLevel=new C.B;_afterOpenedAtThisLevel=new C.B;_ariaHiddenElements=new Map;_scrollStrategy=(0,o.WQX)(M);get openDialogs(){return this._parentDialog?this._parentDialog.openDialogs:this._openDialogsAtThisLevel}get afterOpened(){return this._parentDialog?this._parentDialog.afterOpened:this._afterOpenedAtThisLevel}afterAllClosed=(0,D.v)(()=>this.openDialogs.length?this._getAfterAllClosed():this._getAfterAllClosed().pipe((0,h.Z)(void 0)));constructor(){}open(t,e){(e={...this._defaultOptions||new y,...e}).id=e.id||this._idGenerator.getId("cdk-dialog-"),e.id&&this.getDialogById(e.id);const d=this._getOverlayConfig(e),g=(0,f.c)(this._injector,d),u=new p(g,e),T=this._attachContainer(g,u,e);return u.containerInstance=T,this._attachDialogContent(t,u,T,e),this.openDialogs.length||this._hideNonDialogContentFromAssistiveTechnology(),this.openDialogs.push(u),u.closed.subscribe(()=>this._removeOpenDialog(u,!0)),this.afterOpened.next(u),u}closeAll(){j(this.openDialogs,t=>t.close())}getDialogById(t){return this.openDialogs.find(e=>e.id===t)}ngOnDestroy(){j(this._openDialogsAtThisLevel,t=>{!1===t.config.closeOnDestroy&&this._removeOpenDialog(t,!1)}),j(this._openDialogsAtThisLevel,t=>t.close()),this._afterAllClosedAtThisLevel.complete(),this._afterOpenedAtThisLevel.complete(),this._openDialogsAtThisLevel=[]}_getOverlayConfig(t){const e=new f.i({positionStrategy:t.positionStrategy||(0,f.f)().centerHorizontally().centerVertically(),scrollStrategy:t.scrollStrategy||this._scrollStrategy(),panelClass:t.panelClass,hasBackdrop:t.hasBackdrop,direction:t.direction,minWidth:t.minWidth,minHeight:t.minHeight,maxWidth:t.maxWidth,maxHeight:t.maxHeight,width:t.width,height:t.height,disposeOnNavigation:t.closeOnNavigation,disableAnimations:t.disableAnimations});return t.backdropClass&&(e.backdropClass=t.backdropClass),e}_attachContainer(t,e,n){const d=n.injector||n.viewContainerRef?.injector,g=[{provide:y,useValue:n},{provide:p,useValue:e},{provide:f.d,useValue:t}];let u;n.container?"function"==typeof n.container?u=n.container:(u=n.container.type,g.push(...n.container.providers(n))):u=v;const T=new m.A8(u,n.viewContainerRef,o.zZn.create({parent:d||this._injector,providers:g}));return t.attach(T).instance}_attachDialogContent(t,e,n,d){if(t instanceof s.C4Q){const g=this._createInjector(d,e,n,void 0);let u={$implicit:d.data,dialogRef:e};d.templateContext&&(u={...u,..."function"==typeof d.templateContext?d.templateContext():d.templateContext}),n.attachTemplatePortal(new m.VA(t,null,u,g))}else{const g=this._createInjector(d,e,n,this._injector),u=n.attachComponentPortal(new m.A8(t,d.viewContainerRef,g));e.componentRef=u,e.componentInstance=u.instance}}_createInjector(t,e,n,d){const g=t.injector||t.viewContainerRef?.injector,u=[{provide:P,useValue:t.data},{provide:p,useValue:e}];return t.providers&&("function"==typeof t.providers?u.push(...t.providers(e,t,n)):u.push(...t.providers)),t.direction&&(!g||!g.get(x.D,null,{optional:!0}))&&u.push({provide:x.D,useValue:st(t.direction)}),o.zZn.create({parent:g||d,providers:u})}_removeOpenDialog(t,e){const n=this.openDialogs.indexOf(t);n>-1&&(this.openDialogs.splice(n,1),this.openDialogs.length||(this._ariaHiddenElements.forEach((d,g)=>{d?g.setAttribute("aria-hidden",d):g.removeAttribute("aria-hidden")}),this._ariaHiddenElements.clear(),e&&this._getAfterAllClosed().next()))}_hideNonDialogContentFromAssistiveTechnology(){const t=this._overlayContainer.getContainerElement();if(t.parentElement){const e=t.parentElement.children;for(let n=e.length-1;n>-1;n--){const d=e[n];d!==t&&"SCRIPT"!==d.nodeName&&"STYLE"!==d.nodeName&&!d.hasAttribute("aria-live")&&(this._ariaHiddenElements.set(d,d.getAttribute("aria-hidden")),d.setAttribute("aria-hidden","true"))}}}_getAfterAllClosed(){const t=this._parentDialog;return t?t._getAfterAllClosed():this._afterAllClosedAtThisLevel}static \u0275fac=function(e){return new(e||a)};static \u0275prov=o.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})}return a})();function j(a,r){let t=a.length;for(;t--;)r(a[t])}let rt=(()=>{class a{static \u0275fac=function(e){return new(e||a)};static \u0275mod=s.$C({type:a});static \u0275inj=o.G2t({providers:[z],imports:[f.t,m.jc,b.A,m.jc]})}return a})();var G=l(9355),N=l(6609),lt=l(7786),R=l(5964),W=l(6697),ct=l(3980),V=l(1715);function dt(a,r){}class X{viewContainerRef;injector;id;role="dialog";panelClass="";hasBackdrop=!0;backdropClass="";disableClose=!1;closePredicate;width="";height="";minWidth;minHeight;maxWidth;maxHeight;position;data=null;direction;ariaDescribedBy=null;ariaLabelledBy=null;ariaLabel=null;ariaModal=!1;autoFocus="first-tabbable";restoreFocus=!0;delayFocusTrap=!0;scrollStrategy;closeOnNavigation=!0;enterAnimationDuration;exitAnimationDuration}const U="mdc-dialog--open",K="mdc-dialog--opening",H="mdc-dialog--closing";let $=(()=>{class a extends v{_animationStateChanged=new s.bkB;_animationsEnabled=!(0,N._)();_actionSectionCount=0;_hostElement=this._elementRef.nativeElement;_enterAnimationDuration=this._animationsEnabled?Z(this._config.enterAnimationDuration)??150:0;_exitAnimationDuration=this._animationsEnabled?Z(this._config.exitAnimationDuration)??75:0;_animationTimer=null;_contentAttached(){super._contentAttached(),this._startOpenAnimation()}_startOpenAnimation(){this._animationStateChanged.emit({state:"opening",totalTime:this._enterAnimationDuration}),this._animationsEnabled?(this._hostElement.style.setProperty(Y,`${this._enterAnimationDuration}ms`),this._requestAnimationFrame(()=>this._hostElement.classList.add(K,U)),this._waitForAnimationToComplete(this._enterAnimationDuration,this._finishDialogOpen)):(this._hostElement.classList.add(U),Promise.resolve().then(()=>this._finishDialogOpen()))}_startExitAnimation(){this._animationStateChanged.emit({state:"closing",totalTime:this._exitAnimationDuration}),this._hostElement.classList.remove(U),this._animationsEnabled?(this._hostElement.style.setProperty(Y,`${this._exitAnimationDuration}ms`),this._requestAnimationFrame(()=>this._hostElement.classList.add(H)),this._waitForAnimationToComplete(this._exitAnimationDuration,this._finishDialogClose)):Promise.resolve().then(()=>this._finishDialogClose())}_updateActionSectionCount(t){this._actionSectionCount+=t,this._changeDetectorRef.markForCheck()}_finishDialogOpen=()=>{this._clearAnimationClasses(),this._openAnimationDone(this._enterAnimationDuration)};_finishDialogClose=()=>{this._clearAnimationClasses(),this._animationStateChanged.emit({state:"closed",totalTime:this._exitAnimationDuration})};_clearAnimationClasses(){this._hostElement.classList.remove(K,H)}_waitForAnimationToComplete(t,e){null!==this._animationTimer&&clearTimeout(this._animationTimer),this._animationTimer=setTimeout(e,t)}_requestAnimationFrame(t){this._ngZone.runOutsideAngular(()=>{"function"==typeof requestAnimationFrame?requestAnimationFrame(t):t()})}_captureInitialFocus(){this._config.delayFocusTrap||this._trapFocus()}_openAnimationDone(t){this._config.delayFocusTrap&&this._trapFocus(),this._animationStateChanged.next({state:"opened",totalTime:t})}ngOnDestroy(){super.ngOnDestroy(),null!==this._animationTimer&&clearTimeout(this._animationTimer)}attachComponentPortal(t){const e=super.attachComponentPortal(t);return e.location.nativeElement.classList.add("mat-mdc-dialog-component-host"),e}static \u0275fac=(()=>{let t;return function(n){return(t||(t=s.xGo(a)))(n||a)}})();static \u0275cmp=s.VBU({type:a,selectors:[["mat-dialog-container"]],hostAttrs:["tabindex","-1",1,"mat-mdc-dialog-container","mdc-dialog"],hostVars:10,hostBindings:function(e,n){2&e&&(s.Avn("id",n._config.id),s.BMQ("aria-modal",n._config.ariaModal)("role",n._config.role)("aria-labelledby",n._config.ariaLabel?null:n._ariaLabelledByQueue[0])("aria-label",n._config.ariaLabel)("aria-describedby",n._config.ariaDescribedBy||null),s.AVh("_mat-animation-noopable",!n._animationsEnabled)("mat-mdc-dialog-container-with-actions",n._actionSectionCount>0))},features:[s.Vt3],decls:3,vars:0,consts:[[1,"mat-mdc-dialog-inner-container","mdc-dialog__container"],[1,"mat-mdc-dialog-surface","mdc-dialog__surface"],["cdkPortalOutlet",""]],template:function(e,n){1&e&&(s.j41(0,"div",0)(1,"div",1),s.DNE(2,dt,0,0,"ng-template",2),s.k0s()())},dependencies:[m.I3],styles:['.mat-mdc-dialog-container{width:100%;height:100%;display:block;box-sizing:border-box;max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;outline:0}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 560px);min-width:var(--mat-dialog-container-min-width, 280px)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, calc(100vw - 32px))}}.mat-mdc-dialog-inner-container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;transition:opacity linear var(--mat-dialog-transition-duration, 0ms);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mdc-dialog--closing .mat-mdc-dialog-inner-container{transition:opacity 75ms linear;transform:none}.mdc-dialog--open .mat-mdc-dialog-inner-container{opacity:1}._mat-animation-noopable .mat-mdc-dialog-inner-container{transition:none}.mat-mdc-dialog-surface{display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;width:100%;height:100%;position:relative;overflow-y:auto;outline:0;transform:scale(0.8);transition:transform var(--mat-dialog-transition-duration, 0ms) cubic-bezier(0, 0, 0.2, 1);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;box-shadow:var(--mat-dialog-container-elevation-shadow, none);border-radius:var(--mat-dialog-container-shape, var(--mat-sys-corner-extra-large, 4px));background-color:var(--mat-dialog-container-color, var(--mat-sys-surface, white))}[dir=rtl] .mat-mdc-dialog-surface{text-align:right}.mdc-dialog--open .mat-mdc-dialog-surface,.mdc-dialog--closing .mat-mdc-dialog-surface{transform:none}._mat-animation-noopable .mat-mdc-dialog-surface{transition:none}.mat-mdc-dialog-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:"";pointer-events:none}.mat-mdc-dialog-title{display:block;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:var(--mat-dialog-headline-padding, 6px 24px 13px)}.mat-mdc-dialog-title::before{display:inline-block;width:0;height:40px;content:"";vertical-align:0}[dir=rtl] .mat-mdc-dialog-title{text-align:right}.mat-mdc-dialog-container .mat-mdc-dialog-title{color:var(--mat-dialog-subhead-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-dialog-subhead-font, var(--mat-sys-headline-small-font, inherit));line-height:var(--mat-dialog-subhead-line-height, var(--mat-sys-headline-small-line-height, 1.5rem));font-size:var(--mat-dialog-subhead-size, var(--mat-sys-headline-small-size, 1rem));font-weight:var(--mat-dialog-subhead-weight, var(--mat-sys-headline-small-weight, 400));letter-spacing:var(--mat-dialog-subhead-tracking, var(--mat-sys-headline-small-tracking, 0.03125em))}.mat-mdc-dialog-content{display:block;flex-grow:1;box-sizing:border-box;margin:0;overflow:auto;max-height:65vh}.mat-mdc-dialog-content>:first-child{margin-top:0}.mat-mdc-dialog-content>:last-child{margin-bottom:0}.mat-mdc-dialog-container .mat-mdc-dialog-content{color:var(--mat-dialog-supporting-text-color, var(--mat-sys-on-surface-variant, rgba(0, 0, 0, 0.6)));font-family:var(--mat-dialog-supporting-text-font, var(--mat-sys-body-medium-font, inherit));line-height:var(--mat-dialog-supporting-text-line-height, var(--mat-sys-body-medium-line-height, 1.5rem));font-size:var(--mat-dialog-supporting-text-size, var(--mat-sys-body-medium-size, 1rem));font-weight:var(--mat-dialog-supporting-text-weight, var(--mat-sys-body-medium-weight, 400));letter-spacing:var(--mat-dialog-supporting-text-tracking, var(--mat-sys-body-medium-tracking, 0.03125em))}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px 0)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;box-sizing:border-box;min-height:52px;margin:0;border-top:1px solid rgba(0,0,0,0);padding:var(--mat-dialog-actions-padding, 16px 24px);justify-content:var(--mat-dialog-actions-alignment, flex-end)}@media(forced-colors: active){.mat-mdc-dialog-actions{border-top-color:CanvasText}}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}.mat-mdc-dialog-component-host{display:contents}\n'],encapsulation:2})}return a})();const Y="--mat-dialog-transition-duration";function Z(a){return null==a?null:"number"==typeof a?a:a.endsWith("ms")?(0,G.c)(a.substring(0,a.length-2)):a.endsWith("s")?1e3*(0,G.c)(a.substring(0,a.length-1)):"0"===a?0:null}var L=function(a){return a[a.OPEN=0]="OPEN",a[a.CLOSING=1]="CLOSING",a[a.CLOSED=2]="CLOSED",a}(L||{});class S{_ref;_config;_containerInstance;componentInstance;componentRef;disableClose;id;_afterOpened=new C.B;_beforeClosed=new C.B;_result;_closeFallbackTimeout;_state=L.OPEN;_closeInteractionType;constructor(r,t,e){this._ref=r,this._config=t,this._containerInstance=e,this.disableClose=t.disableClose,this.id=r.id,r.addPanelClass("mat-mdc-dialog-panel"),e._animationStateChanged.pipe((0,R.p)(n=>"opened"===n.state),(0,W.s)(1)).subscribe(()=>{this._afterOpened.next(),this._afterOpened.complete()}),e._animationStateChanged.pipe((0,R.p)(n=>"closed"===n.state),(0,W.s)(1)).subscribe(()=>{clearTimeout(this._closeFallbackTimeout),this._finishDialogClose()}),r.overlayRef.detachments().subscribe(()=>{this._beforeClosed.next(this._result),this._beforeClosed.complete(),this._finishDialogClose()}),(0,lt.h)(this.backdropClick(),this.keydownEvents().pipe((0,R.p)(n=>n.keyCode===w.g&&!this.disableClose&&!(0,c.rp)(n)))).subscribe(n=>{this.disableClose||(n.preventDefault(),function J(a,r,t){return a._closeInteractionType=r,a.close(t)}(this,"keydown"===n.type?"keyboard":"mouse"))})}close(r){const t=this._config.closePredicate;t&&!t(r,this._config,this.componentInstance)||(this._result=r,this._containerInstance._animationStateChanged.pipe((0,R.p)(e=>"closing"===e.state),(0,W.s)(1)).subscribe(e=>{this._beforeClosed.next(r),this._beforeClosed.complete(),this._ref.overlayRef.detachBackdrop(),this._closeFallbackTimeout=setTimeout(()=>this._finishDialogClose(),e.totalTime+100)}),this._state=L.CLOSING,this._containerInstance._startExitAnimation())}afterOpened(){return this._afterOpened}afterClosed(){return this._ref.closed}beforeClosed(){return this._beforeClosed}backdropClick(){return this._ref.backdropClick}keydownEvents(){return this._ref.keydownEvents}updatePosition(r){let t=this._ref.config.positionStrategy;return r&&(r.left||r.right)?r.left?t.left(r.left):t.right(r.right):t.centerHorizontally(),r&&(r.top||r.bottom)?r.top?t.top(r.top):t.bottom(r.bottom):t.centerVertically(),this._ref.updatePosition(),this}updateSize(r="",t=""){return this._ref.updateSize(r,t),this}addPanelClass(r){return this._ref.addPanelClass(r),this}removePanelClass(r){return this._ref.removePanelClass(r),this}getState(){return this._state}_finishDialogClose(){this._state=L.CLOSED,this._ref.close(this._result,{focusOrigin:this._closeInteractionType}),this.componentInstance=null}}const q=new o.nKC("MatMdcDialogData"),gt=new o.nKC("mat-mdc-dialog-default-options"),mt=new o.nKC("mat-mdc-dialog-scroll-strategy",{providedIn:"root",factory:()=>{const a=(0,o.WQX)(o.zZn);return()=>(0,f.s)(a)}});let B=(()=>{class a{_defaultOptions=(0,o.WQX)(gt,{optional:!0});_scrollStrategy=(0,o.WQX)(mt);_parentDialog=(0,o.WQX)(a,{optional:!0,skipSelf:!0});_idGenerator=(0,o.WQX)(E._);_injector=(0,o.WQX)(o.zZn);_dialog=(0,o.WQX)(z);_animationsDisabled=(0,N._)();_openDialogsAtThisLevel=[];_afterAllClosedAtThisLevel=new C.B;_afterOpenedAtThisLevel=new C.B;dialogConfigClass=X;_dialogRefConstructor;_dialogContainerType;_dialogDataToken;get openDialogs(){return this._parentDialog?this._parentDialog.openDialogs:this._openDialogsAtThisLevel}get afterOpened(){return this._parentDialog?this._parentDialog.afterOpened:this._afterOpenedAtThisLevel}_getAfterAllClosed(){const t=this._parentDialog;return t?t._getAfterAllClosed():this._afterAllClosedAtThisLevel}afterAllClosed=(0,D.v)(()=>this.openDialogs.length?this._getAfterAllClosed():this._getAfterAllClosed().pipe((0,h.Z)(void 0)));constructor(){this._dialogRefConstructor=S,this._dialogContainerType=$,this._dialogDataToken=q}open(t,e){let n;(e={...this._defaultOptions||new X,...e}).id=e.id||this._idGenerator.getId("mat-mdc-dialog-"),e.scrollStrategy=e.scrollStrategy||this._scrollStrategy();const d=this._dialog.open(t,{...e,positionStrategy:(0,f.f)(this._injector).centerHorizontally().centerVertically(),disableClose:!0,closePredicate:void 0,closeOnDestroy:!1,closeOnOverlayDetachments:!1,disableAnimations:this._animationsDisabled||"0"===e.enterAnimationDuration?.toLocaleString()||"0"===e.exitAnimationDuration?.toString(),container:{type:this._dialogContainerType,providers:()=>[{provide:this.dialogConfigClass,useValue:e},{provide:y,useValue:e}]},templateContext:()=>({dialogRef:n}),providers:(g,u,T)=>(n=new this._dialogRefConstructor(g,e,T),n.updatePosition(e?.position),[{provide:this._dialogContainerType,useValue:T},{provide:this._dialogDataToken,useValue:u.data},{provide:this._dialogRefConstructor,useValue:n}])});return n.componentRef=d.componentRef,n.componentInstance=d.componentInstance,this.openDialogs.push(n),this.afterOpened.next(n),n.afterClosed().subscribe(()=>{const g=this.openDialogs.indexOf(n);g>-1&&(this.openDialogs.splice(g,1),this.openDialogs.length||this._getAfterAllClosed().next())}),n}closeAll(){this._closeDialogs(this.openDialogs)}getDialogById(t){return this.openDialogs.find(e=>e.id===t)}ngOnDestroy(){this._closeDialogs(this._openDialogsAtThisLevel),this._afterAllClosedAtThisLevel.complete(),this._afterOpenedAtThisLevel.complete()}_closeDialogs(t){let e=t.length;for(;e--;)t[e].close()}static \u0275fac=function(e){return new(e||a)};static \u0275prov=o.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})}return a})(),tt=(()=>{class a{_dialogRef=(0,o.WQX)(S,{optional:!0});_elementRef=(0,o.WQX)(s.aKT);_dialog=(0,o.WQX)(B);constructor(){}ngOnInit(){this._dialogRef||(this._dialogRef=function nt(a,r){let t=a.nativeElement.parentElement;for(;t&&!t.classList.contains("mat-mdc-dialog-container");)t=t.parentElement;return t?r.find(e=>e.id===t.id):null}(this._elementRef,this._dialog.openDialogs)),this._dialogRef&&Promise.resolve().then(()=>{this._onAdd()})}ngOnDestroy(){this._dialogRef?._containerInstance&&Promise.resolve().then(()=>{this._onRemove()})}static \u0275fac=function(e){return new(e||a)};static \u0275dir=s.FsC({type:a})}return a})(),et=(()=>{class a extends tt{id=(0,o.WQX)(E._).getId("mat-mdc-dialog-title-");_onAdd(){this._dialogRef._containerInstance?._addAriaLabelledBy?.(this.id)}_onRemove(){this._dialogRef?._containerInstance?._removeAriaLabelledBy?.(this.id)}static \u0275fac=(()=>{let t;return function(n){return(t||(t=s.xGo(a)))(n||a)}})();static \u0275dir=s.FsC({type:a,selectors:[["","mat-dialog-title",""],["","matDialogTitle",""]],hostAttrs:[1,"mat-mdc-dialog-title","mdc-dialog__title"],hostVars:1,hostBindings:function(e,n){2&e&&s.Avn("id",n.id)},inputs:{id:"id"},exportAs:["matDialogTitle"],features:[s.Vt3]})}return a})(),it=(()=>{class a{static \u0275fac=function(e){return new(e||a)};static \u0275dir=s.FsC({type:a,selectors:[["","mat-dialog-content",""],["mat-dialog-content"],["","matDialogContent",""]],hostAttrs:[1,"mat-mdc-dialog-content","mdc-dialog__content"],features:[s.nM4([ct.uv])]})}return a})(),at=(()=>{class a extends tt{align;_onAdd(){this._dialogRef._containerInstance?._updateActionSectionCount?.(1)}_onRemove(){this._dialogRef._containerInstance?._updateActionSectionCount?.(-1)}static \u0275fac=(()=>{let t;return function(n){return(t||(t=s.xGo(a)))(n||a)}})();static \u0275dir=s.FsC({type:a,selectors:[["","mat-dialog-actions",""],["mat-dialog-actions"],["","matDialogActions",""]],hostAttrs:[1,"mat-mdc-dialog-actions","mdc-dialog__actions"],hostVars:6,hostBindings:function(e,n){2&e&&s.AVh("mat-mdc-dialog-actions-align-start","start"===n.align)("mat-mdc-dialog-actions-align-center","center"===n.align)("mat-mdc-dialog-actions-align-end","end"===n.align)},inputs:{align:"align"},features:[s.Vt3]})}return a})();let pt=(()=>{class a{static \u0275fac=function(e){return new(e||a)};static \u0275mod=s.$C({type:a});static \u0275inj=o.G2t({providers:[B],imports:[rt,f.t,m.jc,V.M,V.M]})}return a})()},2544:(Q,k,l)=>{l.d(k,{p:()=>x});var s=l(9417),o=l(357),i=l(7241),m=l(3156),b=l(8822),I=l(6396),A=l(3364),O=l(6863),C=l(8834),D=l(1074),w=l(5781),c=l(3496);function h(_,y){1&_&&(i.j41(0,"div",22)(1,"mat-icon",23),i.EFF(2,"info"),i.k0s(),i.j41(3,"p"),i.EFF(4,"All your recovery codes have been used. You can request to disable 2FA via email verification."),i.k0s()())}function f(_,y){1&_&&(i.j41(0,"mat-icon"),i.EFF(1,"hourglass_empty"),i.k0s())}function E(_,y){1&_&&(i.j41(0,"mat-icon"),i.EFF(1,"email"),i.k0s())}let x=(()=>{class _{constructor(F,v,p,M,P){this.formBuilder=F,this.twoFactorService=v,this.snackBar=p,this.dialogRef=M,this.data=P,this.loading=!1,this.disableForm=this.formBuilder.group({email:[{value:P.email,disabled:!0},[s.k0.required,s.k0.email]],reason:[P.allCodesUsed?"recovery_codes_exhausted":"lost_device",[s.k0.required]]})}onSubmit(){if(this.disableForm.invalid)return;this.loading=!0;const F=this.disableForm.getRawValue();this.twoFactorService.requestDisable2FA(F.email,F.reason).subscribe({next:v=>{this.snackBar.open("Disable confirmation email sent! Check your inbox and click the link to confirm.","Close",{duration:8e3,panelClass:["success-snackbar"]}),this.dialogRef.close({success:!0,response:v}),this.loading=!1},error:v=>{console.error("Failed to request 2FA disable:",v),this.snackBar.open(v.error?.message||"Failed to send disable email. Please try again.","Close",{duration:5e3,panelClass:["error-snackbar"]}),this.loading=!1}})}onCancel(){this.dialogRef.close({success:!1})}static#t=this.\u0275fac=function(v){return new(v||_)(i.rXU(s.ok),i.rXU(m.f),i.rXU(b.UG),i.rXU(o.k),i.rXU(o.e))};static#e=this.\u0275cmp=i.VBU({type:_,selectors:[["app-disable-2fa-dialog"]],standalone:!1,decls:65,vars:7,consts:[[1,"disable-2fa-dialog"],["mat-dialog-title","",1,"dialog-title"],[1,"warning-icon"],[1,"dialog-content"],[1,"security-warning"],[1,"security-icon"],[1,"warning-text"],["class","status-info",4,"ngIf"],[3,"ngSubmit","formGroup"],["appearance","outline",1,"full-width"],["matInput","","formControlName","email","readonly",""],["matSuffix",""],["formControlName","reason","required",""],["value","recovery_codes_exhausted"],["value","lost_device"],["value","other"],[1,"alternatives-section"],[1,"process-info"],[1,"dialog-actions"],["mat-button","",3,"click","disabled"],["mat-raised-button","","color","warn",3,"click","disabled"],[4,"ngIf"],[1,"status-info"],[1,"info-icon"]],template:function(v,p){1&v&&(i.j41(0,"div",0)(1,"h2",1)(2,"mat-icon",2),i.EFF(3,"warning"),i.k0s(),i.EFF(4," Disable Two-Factor Authentication "),i.k0s(),i.j41(5,"mat-dialog-content",3)(6,"div",4)(7,"mat-icon",5),i.EFF(8,"security"),i.k0s(),i.j41(9,"div",6)(10,"h3"),i.EFF(11,"Security Warning"),i.k0s(),i.j41(12,"p"),i.EFF(13,"Disabling 2FA will make your account less secure. Are you sure you want to proceed?"),i.k0s()()(),i.DNE(14,h,5,0,"div",7),i.j41(15,"form",8),i.bIt("ngSubmit",function(){return p.onSubmit()}),i.j41(16,"mat-form-field",9)(17,"mat-label"),i.EFF(18,"Email Address"),i.k0s(),i.nrm(19,"input",10),i.j41(20,"mat-icon",11),i.EFF(21,"email"),i.k0s()(),i.j41(22,"mat-form-field",9)(23,"mat-label"),i.EFF(24,"Reason for Disabling"),i.k0s(),i.j41(25,"mat-select",12)(26,"mat-option",13),i.EFF(27,"All recovery codes used"),i.k0s(),i.j41(28,"mat-option",14),i.EFF(29,"Lost access to authenticator device"),i.k0s(),i.j41(30,"mat-option",15),i.EFF(31,"Other reason"),i.k0s()(),i.j41(32,"mat-icon",11),i.EFF(33,"help_outline"),i.k0s()(),i.j41(34,"div",16)(35,"h4")(36,"mat-icon"),i.EFF(37,"lightbulb"),i.k0s(),i.EFF(38," Consider These Alternatives:"),i.k0s(),i.j41(39,"ul")(40,"li"),i.EFF(41,"Generate new recovery codes if you still have access to your authenticator"),i.k0s(),i.j41(42,"li"),i.EFF(43,"Set up a new authenticator app on a different device"),i.k0s(),i.j41(44,"li"),i.EFF(45,"Use email-based 2FA verification instead"),i.k0s()()(),i.j41(46,"div",17)(47,"h4")(48,"mat-icon"),i.EFF(49,"email"),i.k0s(),i.EFF(50," Email Verification Process:"),i.k0s(),i.j41(51,"ol")(52,"li"),i.EFF(53,"We'll send a secure link to your email address"),i.k0s(),i.j41(54,"li"),i.EFF(55,"Click the link to confirm disabling 2FA"),i.k0s(),i.j41(56,"li"),i.EFF(57,"The link expires in 1 hour for security"),i.k0s()()()()(),i.j41(58,"mat-dialog-actions",18)(59,"button",19),i.bIt("click",function(){return p.onCancel()}),i.EFF(60," Cancel "),i.k0s(),i.j41(61,"button",20),i.bIt("click",function(){return p.onSubmit()}),i.DNE(62,f,2,0,"mat-icon",21)(63,E,2,0,"mat-icon",21),i.EFF(64),i.k0s()()()),2&v&&(i.R7$(14),i.Y8G("ngIf",p.data.allCodesUsed),i.R7$(),i.Y8G("formGroup",p.disableForm),i.R7$(44),i.Y8G("disabled",p.loading),i.R7$(2),i.Y8G("disabled",p.disableForm.invalid||p.loading),i.R7$(),i.Y8G("ngIf",p.loading),i.R7$(),i.Y8G("ngIf",!p.loading),i.R7$(),i.SpI(" ",p.loading?"Sending...":"Send Disable Email"," "))},dependencies:[I.bT,s.qT,s.me,s.BC,s.cb,s.YS,s.j4,s.JD,A.j,A.M,A.g,O.fg,C.$z,D.An,o.b,o.M,o.c,w.g,c.M],styles:[".disable-2fa-dialog[_ngcontent-%COMP%]{max-width:500px;width:100%}.dialog-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;color:#f57c00;margin-bottom:0}.warning-icon[_ngcontent-%COMP%]{color:#ff9800;font-size:24px;width:24px;height:24px}.dialog-content[_ngcontent-%COMP%]{padding:20px 0}.security-warning[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:16px;background:#fff3e0;border:1px solid #ffcc02;border-radius:8px;padding:16px;margin-bottom:20px}.security-icon[_ngcontent-%COMP%]{color:#ff9800;font-size:32px;width:32px;height:32px;flex-shrink:0}.warning-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:#f57c00;font-size:16px}.warning-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#e65100}.status-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;background:#e3f2fd;border:1px solid #2196f3;border-radius:8px;padding:12px;margin-bottom:20px}.info-icon[_ngcontent-%COMP%]{color:#2196f3}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.alternatives-section[_ngcontent-%COMP%], .process-info[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:8px;padding:16px;margin:16px 0}.alternatives-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .process-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin:0 0 12px;color:#1976d2;font-size:14px}.alternatives-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:0;padding-left:20px}.alternatives-section[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:8px;color:#424242}.process-info[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]{margin:0;padding-left:20px}.process-info[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:8px;color:#424242}.dialog-actions[_ngcontent-%COMP%]{padding:16px 0 0;justify-content:flex-end}.dialog-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-left:8px}"]})}return _})()},7097:(Q,k,l)=>{l.d(k,{T:()=>C});var s=l(8810),o=l(9437),i=l(8141),m=l(5312),b=l(8564),I=l(2306),A=l(5333),O=l(4796);let C=(()=>{class D{constructor(c,h,f){this.http=c,this.router=h,this.authService=f,this.oauthProviders=[{name:"google",displayName:"Google",icon:"fab fa-google",color:"#db4437"},{name:"github",displayName:"GitHub",icon:"fab fa-github",color:"#333"},{name:"microsoft",displayName:"Microsoft",icon:"fab fa-microsoft",color:"#00a1f1"}]}getAvailableProviders(){return this.oauthProviders}getOAuthUrl(c){return this.http.get(`${m.c.apiUrl}/auth/oauth/${c}/url`).pipe((0,o.W)(this.handleError))}initiateOAuthLogin(c){this.getOAuthUrl(c).subscribe({next:h=>{sessionStorage.setItem("oauth_provider",c),sessionStorage.setItem("oauth_redirect",this.router.url),window.location.href=h.url},error:h=>{console.error(`Failed to get ${c} OAuth URL:`,h)}})}handleOAuthCallback(c,h){const f=sessionStorage.getItem("oauth_provider"),E=sessionStorage.getItem("oauth_redirect")||"/dashboard";if(!f)return(0,s.$)(()=>new Error("OAuth provider not found in session"));const x={code:c};return h&&(x.state=h),this.http.post(`${m.c.apiUrl}/auth/oauth/${f}/callback`,x).pipe((0,i.M)(_=>{_.token&&(sessionStorage.removeItem("oauth_provider"),sessionStorage.removeItem("oauth_redirect"),this.authService.setToken(_.token),this.router.navigate([E]))}),(0,o.W)(this.handleError))}exchangeAuthorizationCode(c){return console.log("\u{1f504} OAuth Service - Exchanging authorization code for token"),this.http.post(`${m.c.apiUrl}/auth/oauth/exchange-token`,{code:c}).pipe((0,i.M)(h=>{console.log("\u2705 OAuth Service - Token exchange successful")}),(0,o.W)(h=>(console.error("\u274c OAuth Service - Token exchange failed:",h),(0,s.$)(h))))}isOAuthUser(c){return!(!c?.oauthProvider||!(c?.googleId||c?.githubId||c?.microsoftId))}getOAuthProviderName(c){return c?.googleId?"Google":c?.githubId?"GitHub":c?.microsoftId?"Microsoft":"Unknown"}getOAuthProviderIcon(c){return c?.googleId?"fab fa-google":c?.githubId?"fab fa-github":c?.microsoftId?"fab fa-microsoft":"fas fa-user"}getOAuthProviderColor(c){return c?.googleId?"#db4437":c?.githubId?"#333":c?.microsoftId?"#00a1f1":"#6c757d"}linkOAuthAccount(c){this.getOAuthUrl(c).subscribe({next:h=>{sessionStorage.setItem("oauth_action","link"),sessionStorage.setItem("oauth_provider",c),window.location.href=h.url},error:h=>{console.error(`Failed to link ${c} account:`,h)}})}unlinkOAuthAccount(c){const h=this.authService.getAuthHeaders();return this.http.delete(`${m.c.apiUrl}/auth/oauth/${c}/unlink`,{headers:h}).pipe((0,o.W)(this.handleError))}handleError(c){let h="OAuth authentication error occurred";return h=c.error instanceof ErrorEvent?c.error.message:c.error?.message||c.message||`Error Code: ${c.status}`,console.error("OAuth Service Error:",c),(0,s.$)(()=>new Error(h))}static#t=this.\u0275fac=function(h){return new(h||D)(b.KVO(I.Qq),b.KVO(A.Ix),b.KVO(O.u))};static#e=this.\u0275prov=b.jDH({token:D,factory:D.\u0275fac,providedIn:"root"})}return D})()}}]);