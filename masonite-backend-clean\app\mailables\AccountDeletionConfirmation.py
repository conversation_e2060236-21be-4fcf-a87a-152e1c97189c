from masonite.mail import Mailable
from masonite.environment import env


class AccountDeletionConfirmation(Mailable):
    """
    Account Deletion Confirmation Mailable
    Compatible with LoopBack account deletion flow
    """
    
    def __init__(self, user, deletion_record, preferences):
        """Initialize with user, deletion record and preferences"""
        super().__init__()
        self.user = user
        self.deletion_record = deletion_record
        self.preferences = preferences
    
    def build(self):
        """Build the account deletion confirmation email"""
        confirmation_url = f"{env('FRONTEND_URL', 'http://localhost:4200')}/account/confirm-deletion?token={self.deletion_record.confirmation_token}"
        
        # Build preserved data text
        preserved_items = []
        if self.preferences.get('preservePaymentData', False):
            preserved_items.append('Payment data')
        if self.preferences.get('preserveTransactionHistory', False):
            preserved_items.append('Transaction history')
        if self.preferences.get('preserveProfileData', False):
            preserved_items.append('Profile data')
        if self.preferences.get('preserveSecurityLogs', False):
            preserved_items.append('Security logs')
        
        preserved_text = ', '.join(preserved_items) if preserved_items else 'None'
        
        html_content = f"""
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
            <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h2 style="color: #dc3545; text-align: center; margin-bottom: 20px;">🗑️ Account Deletion Confirmation Required</h2>
                
                <p style="color: #333; font-size: 16px;">Hello <strong>{getattr(self.user, 'name', 'User')}</strong>,</p>
                
                <p style="color: #333; font-size: 14px; line-height: 1.6;">
                    You have requested to delete your account. Please review the details below and confirm your decision:
                </p>
                
                <div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #dc3545;">
                    <h3 style="color: #333; margin-top: 0;">⚙️ Deletion Preferences</h3>
                    <ul style="color: #555; line-height: 1.8;">
                        <li><strong>Payment data:</strong> {'✅ Preserve' if self.preferences.get('preservePaymentData', False) else '❌ Delete'}</li>
                        <li><strong>Transaction history:</strong> {'✅ Preserve' if self.preferences.get('preserveTransactionHistory', False) else '❌ Delete'}</li>
                        <li><strong>Profile data:</strong> {'✅ Preserve' if self.preferences.get('preserveProfileData', False) else '❌ Delete'}</li>
                        <li><strong>Security logs:</strong> {'✅ Preserve' if self.preferences.get('preserveSecurityLogs', False) else '❌ Delete'}</li>
                    </ul>
                    <p style="color: #555; margin-bottom: 0;"><strong>Data preserved:</strong> {preserved_text}</p>
                    <p style="color: #555; margin-bottom: 0;"><strong>Retention period:</strong> {self.preferences.get('customRetentionPeriod', 30)} days</p>
                    {f"<p style='color: #555; margin-bottom: 0;'><strong>Reason:</strong> {self.preferences.get('reason', 'Not specified')}</p>" if self.preferences.get('reason') else ''}
                </div>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="{confirmation_url}" 
                       style="background: linear-gradient(135deg, #dc3545, #c82333); color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; font-size: 16px; box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);">
                        🗑️ Confirm Account Deletion
                    </a>
                </div>
                
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 6px; margin: 20px 0;">
                    <p style="color: #856404; margin: 0; font-size: 14px;">
                        ⚠️ <strong>Warning:</strong> This action cannot be undone. Once confirmed, your account will be permanently deleted according to your preferences above.
                    </p>
                </div>
                
                <p style="color: #666; font-size: 13px; line-height: 1.6;">
                    ⏰ <strong>Important:</strong> This confirmation link expires in 24 hours.<br>
                    If you didn't request this deletion, please ignore this email or contact our support team immediately.
                </p>
                
                <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                
                <p style="color: #999; font-size: 12px; text-align: center;">
                    This email was sent regarding your account deletion request.<br>
                    For security reasons, please do not share this confirmation link with anyone.
                </p>
            </div>
        </div>
        """
        
        text_content = f"""
        Account Deletion Confirmation Required
        
        Hello {getattr(self.user, 'name', 'User')},
        
        You have requested to delete your account. Please review the details below:
        
        Deletion Preferences:
        - Payment data: {'Preserve' if self.preferences.get('preservePaymentData', False) else 'Delete'}
        - Transaction history: {'Preserve' if self.preferences.get('preserveTransactionHistory', False) else 'Delete'}
        - Profile data: {'Preserve' if self.preferences.get('preserveProfileData', False) else 'Delete'}
        - Security logs: {'Preserve' if self.preferences.get('preserveSecurityLogs', False) else 'Delete'}
        
        Data preserved: {preserved_text}
        Retention period: {self.preferences.get('customRetentionPeriod', 30)} days
        {f"Reason: {self.preferences.get('reason')}" if self.preferences.get('reason') else ''}
        
        To confirm your account deletion, click the link below:
        {confirmation_url}
        
        WARNING: This action cannot be undone. Once confirmed, your account will be permanently deleted according to your preferences above.
        
        This confirmation link expires in 24 hours.
        
        If you didn't request this deletion, please ignore this email or contact support immediately.
        """
        
        return self.to(self.user.email).subject("⚠️ Confirm Account Deletion Request").view_data({
            'html_content': html_content,
            'text_content': text_content
        }).text(text_content).html(html_content)
