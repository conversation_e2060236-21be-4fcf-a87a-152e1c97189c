#!/usr/bin/env python3
"""
Test script to verify the signup endpoint is working correctly
"""

import requests
import json

def test_signup():
    """Test the /api/auth/signup endpoint"""
    
    # Server URL
    url = "http://localhost:3002/api/auth/signup"
      # Test user data with unique email
    import time
    unique_id = int(time.time())
    test_data = {
        'firstName': 'Test',
        'lastName': 'User',
        'email': f'testuser{unique_id}@example.com',
        'phone': '+1234567890',
        'password': 'Test123@',
        'confirmPassword': 'Test123@',
        'acceptTerms': True
    }
    
    # Headers
    headers = {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:4200'
    }
    
    try:
        print("🔍 Testing signup endpoint...")
        print(f"📤 Sending request to: {url}")
        print(f"📝 Data: {json.dumps(test_data, indent=2)}")
        
        response = requests.post(url, json=test_data, headers=headers)
        
        print(f"\n📥 Response Status: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            response_data = response.json()
            print(f"📄 Response Data: {json.dumps(response_data, indent=2)}")
        else:
            print(f"📄 Response Text: {response.text}")
            
        if response.status_code == 200:
            print("✅ Signup test PASSED!")
            return True
        else:
            print(f"❌ Signup test FAILED with status {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - server may not be running")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    test_signup()
