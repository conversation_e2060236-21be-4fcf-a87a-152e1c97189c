#!/usr/bin/env python3
"""
Comprehensive test for data preservation logic fixes
Tests all scenarios including:
1. No data preservation selected (all 4 checkboxes unchecked)
2. Selective data preservation (only some checkboxes checked)
3. Full data preservation (all checkboxes checked)
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3002/api"

def register_user(email, password):
    """Register a new user"""
    register_data = {
        "email": email,
        "password": password,
        "confirmPassword": password,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+1234567890"
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=register_data)
    if response.status_code in [200, 201]:
        user_data = response.json()
        return {
            'success': True,
            'user_id': user_data.get('userId') or user_data.get('user', {}).get('id'),
            'token': user_data.get('token'),
            'response': user_data
        }
    else:
        return {'success': False, 'error': response.text}

def request_deletion(token, preserve_payment=False, preserve_transactions=False, preserve_profile=False, preserve_security=False):
    """Request account deletion with specific preservation preferences"""
    deletion_data = {
        "preservePaymentData": preserve_payment,
        "preserveTransactionHistory": preserve_transactions,
        "preserveProfileData": preserve_profile,
        "preserveSecurityLogs": preserve_security,
        "customRetentionPeriod": 30,
        "reason": "Testing comprehensive data preservation logic"
    }
    
    response = requests.post(f"{BASE_URL}/account/request-deletion", 
                           json=deletion_data,
                           headers={'Authorization': f'Bearer {token}'})
    
    if response.status_code == 200:
        return {'success': True, 'data': response.json()}
    else:
        return {'success': False, 'error': response.text}

def confirm_deletion(token):
    """Confirm account deletion"""
    response = requests.post(f"{BASE_URL}/account/confirm-deletion", 
                           json={'token': token})
    
    if response.status_code == 200:
        return {'success': True, 'data': response.json()}
    else:
        return {'success': False, 'error': response.text}

def check_preserved_data(email):
    """Check if user has preserved data"""
    response = requests.post(f"{BASE_URL}/account/check-preserved-data", 
                           json={'email': email})
    
    if response.status_code == 200:
        return {'success': True, 'data': response.json()}
    else:
        return {'success': False, 'error': response.text}

def test_scenario(scenario_name, email, preserve_payment=False, preserve_transactions=False, preserve_profile=False, preserve_security=False):
    """Test a specific data preservation scenario"""
    print(f"\n🧪 Testing Scenario: {scenario_name}")
    print("=" * 60)
    
    # Step 1: Register user
    print("📝 Step 1: Registering user...")
    result = register_user(email, "TestPassword123!")
    if not result['success']:
        print(f"❌ Registration failed: {result['error']}")
        return False
    
    user_id = result['user_id']
    token = result['token']
    print(f"✅ User registered with ID: {user_id}")
    
    # Step 2: Request deletion with specific preferences
    print(f"\n📝 Step 2: Requesting deletion with preferences...")
    print(f"   - Preserve Payment Data: {preserve_payment}")
    print(f"   - Preserve Transactions: {preserve_transactions}")
    print(f"   - Preserve Profile Data: {preserve_profile}")
    print(f"   - Preserve Security Logs: {preserve_security}")
    
    result = request_deletion(token, preserve_payment, preserve_transactions, preserve_profile, preserve_security)
    if not result['success']:
        print(f"❌ Deletion request failed: {result['error']}")
        return False
    
    deletion_id = result['data']['deletionId']
    confirmation_token = result['data']['confirmationToken']
    print(f"✅ Deletion requested with ID: {deletion_id}")
    
    # Step 3: Confirm deletion
    print("\n📝 Step 3: Confirming deletion...")
    result = confirm_deletion(confirmation_token)
    if not result['success']:
        print(f"❌ Deletion confirmation failed: {result['error']}")
        return False
    
    print("✅ Deletion confirmed")
    print(f"📄 Preserved data summary: {result['data'].get('preservedDataSummary', {})}")
    
    # Step 4: Check preserved data
    print("\n📝 Step 4: Checking preserved data...")
    result = check_preserved_data(email)
    if not result['success']:
        print(f"❌ Check preserved data failed: {result['error']}")
        return False
    
    has_preserved = result['data'].get('hasPreservedData', False)
    preserved_summary = result['data'].get('preservedDataSummary', {})
    
    print(f"📊 Has preserved data: {has_preserved}")
    print(f"📊 Preserved data summary: {preserved_summary}")
    
    # Step 5: Validate expectations
    print("\n📝 Step 5: Validating expectations...")
    
    # Calculate expected result
    any_data_selected = preserve_payment or preserve_transactions or preserve_profile or preserve_security
    
    if any_data_selected:
        if not has_preserved:
            print(f"❌ FAIL: Expected preserved data but found none")
            return False
        
        # Check specific categories
        expected_categories = []
        if preserve_payment:
            expected_categories.append('payments')
        if preserve_transactions:
            expected_categories.append('transactions')
        if preserve_profile:
            expected_categories.append('profile')
        if preserve_security:
            expected_categories.append('security')
        
        for category in expected_categories:
            if category not in preserved_summary:
                print(f"❌ FAIL: Expected {category} to be preserved but it's not in summary")
                return False
        
        print(f"✅ PASS: Correctly preserved selected categories: {list(preserved_summary.keys())}")
    else:
        if has_preserved:
            print(f"❌ FAIL: Expected no preserved data but found: {preserved_summary}")
            return False
        
        print("✅ PASS: Correctly no preserved data when no categories selected")
    
    # Step 6: Test signup behavior
    print("\n📝 Step 6: Testing signup behavior...")
    result = register_user(email, "TestPassword123!")
    if not result['success']:
        print(f"❌ Second registration failed: {result['error']}")
        return False
    
    response_data = result['response']
    signup_has_preserved = response_data.get('hasPreservedData', False)
    signup_preserved_summary = response_data.get('preservedDataSummary', {})
    
    print(f"📊 Signup response - Has preserved data: {signup_has_preserved}")
    print(f"📊 Signup response - Preserved summary: {signup_preserved_summary}")
    
    # Validate signup behavior matches deletion behavior
    if signup_has_preserved != has_preserved:
        print(f"❌ FAIL: Signup preserved data ({signup_has_preserved}) doesn't match deletion check ({has_preserved})")
        return False
    
    if signup_preserved_summary != preserved_summary:
        print(f"❌ FAIL: Signup preserved summary doesn't match deletion check")
        print(f"   Signup: {signup_preserved_summary}")
        print(f"   Check:  {preserved_summary}")
        return False
    
    print("✅ PASS: Signup behavior matches deletion behavior")
    
    return True

def main():
    """Run comprehensive tests"""
    print("🚀 Starting Comprehensive Data Preservation Logic Tests")
    print("Testing all scenarios to ensure proper data preservation behavior")
    print("=" * 80)
    
    scenarios = [
        {
            'name': 'No Data Preservation (All Unchecked)',
            'email': f'test.none.{int(time.time())}@example.com',
            'preserve_payment': False,
            'preserve_transactions': False,
            'preserve_profile': False,
            'preserve_security': False
        },
        {
            'name': 'Only Payment Data Preserved',
            'email': f'test.payment.{int(time.time())}@example.com',
            'preserve_payment': True,
            'preserve_transactions': False,
            'preserve_profile': False,
            'preserve_security': False
        },
        {
            'name': 'Profile and Security Preserved',
            'email': f'test.profile.{int(time.time())}@example.com',
            'preserve_payment': False,
            'preserve_transactions': False,
            'preserve_profile': True,
            'preserve_security': True
        },
        {
            'name': 'All Data Preserved',
            'email': f'test.all.{int(time.time())}@example.com',
            'preserve_payment': True,
            'preserve_transactions': True,
            'preserve_profile': True,
            'preserve_security': True
        }
    ]
    
    results = []
    
    for scenario in scenarios:
        success = test_scenario(
            scenario['name'],
            scenario['email'],
            scenario['preserve_payment'],
            scenario['preserve_transactions'],
            scenario['preserve_profile'],
            scenario['preserve_security']
        )
        results.append({'scenario': scenario['name'], 'success': success})
    
    # Summary
    print("\n" + "=" * 80)
    print("🏁 TEST RESULTS SUMMARY")
    print("=" * 80)
    
    passed = 0
    failed = 0
    
    for result in results:
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        print(f"{status}: {result['scenario']}")
        if result['success']:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📊 Total: {len(results)} scenarios")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! Data preservation logic is working correctly!")
    else:
        print(f"\n⚠️ {failed} test(s) failed. Data preservation logic needs more work.")

if __name__ == "__main__":
    main()
