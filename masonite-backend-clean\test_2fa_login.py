#!/usr/bin/env python3
"""
Test script for 2FA login functionality
Tests the CORS and authentication fixes
"""

import requests
import json

# Test configuration
BASE_URL = "http://localhost:3002"
FRONTEND_ORIGIN = "http://localhost:4200"

def test_cors_preflight():
    """Test CORS preflight request"""
    print("🔍 Testing CORS preflight request...")
    
    url = f"{BASE_URL}/api/auth/login"
    headers = {
        'Origin': FRONTEND_ORIGIN,
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type, Authorization'
    }
    
    try:
        response = requests.options(url, headers=headers)
        print(f"✅ Preflight Status: {response.status_code}")
        print(f"🔧 CORS Headers: {dict(response.headers)}")
        
        # Check important CORS headers
        cors_headers = [
            'Access-Control-Allow-Origin',
            'Access-Control-Allow-Methods',
            'Access-Control-Allow-Headers',
            'Access-Control-Allow-Credentials'
        ]
        
        for header in cors_headers:
            if header in response.headers:
                print(f"✅ {header}: {response.headers[header]}")
            else:
                print(f"❌ Missing {header}")
                
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Preflight request failed: {e}")
        return False

def test_login_with_invalid_credentials():
    """Test login with invalid credentials to check CORS on error"""
    print("\n🔍 Testing login with invalid credentials...")
    
    url = f"{BASE_URL}/api/auth/login"
    headers = {
        'Content-Type': 'application/json',
        'Origin': FRONTEND_ORIGIN
    }
    
    data = {
        'email': '<EMAIL>',
        'password': 'wrongpassword'
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        print(f"✅ Login Status: {response.status_code}")
        print(f"🔧 Response Headers: {dict(response.headers)}")
        
        # Check CORS headers on error response
        if 'Access-Control-Allow-Origin' in response.headers:
            print(f"✅ CORS Origin: {response.headers['Access-Control-Allow-Origin']}")
        else:
            print("❌ Missing CORS Origin header on error response")
        
        try:
            response_json = response.json()
            print(f"📝 Response Body: {json.dumps(response_json, indent=2)}")
        except:
            print(f"📝 Response Text: {response.text}")
            
        return True
    except Exception as e:
        print(f"❌ Login request failed: {e}")
        return False

def test_login_with_2fa_user():
    """Test login with a user that has 2FA enabled"""
    print("\n🔍 Testing login with 2FA enabled user...")
    
    # First, let's check if we have any users in the database
    print("📊 Checking for test users...")
    
    url = f"{BASE_URL}/api/auth/login"
    headers = {
        'Content-Type': 'application/json',
        'Origin': FRONTEND_ORIGIN
    }
    
    # Try with common test credentials
    test_credentials = [
        {'email': '<EMAIL>', 'password': 'password'},
        {'email': '<EMAIL>', 'password': 'password'},
        {'email': '<EMAIL>', 'password': 'password123'},
    ]
    
    for creds in test_credentials:
        print(f"🔑 Trying {creds['email']}...")
        
        try:
            response = requests.post(url, json=creds, headers=headers)
            print(f"✅ Status: {response.status_code}")
            
            # Check CORS headers
            if 'Access-Control-Allow-Origin' in response.headers:
                print(f"✅ CORS Origin: {response.headers['Access-Control-Allow-Origin']}")
            else:
                print("❌ Missing CORS Origin header")
            
            try:
                response_json = response.json()
                print(f"📝 Response: {json.dumps(response_json, indent=2)}")
                
                # Check if this is a 2FA response
                if response_json.get('requiresTwoFactor'):
                    print("🔐 2FA required - this is working correctly!")
                    return True
                elif response_json.get('token'):
                    print("🔓 Regular login successful")
                    return True
                    
            except:
                print(f"📝 Response Text: {response.text}")
                
        except Exception as e:
            print(f"❌ Request failed: {e}")
    
    return False

def main():
    """Run all tests"""
    print("🚀 Starting 2FA Login and CORS Tests")
    print("=" * 50)
    
    tests = [
        ("CORS Preflight", test_cors_preflight),
        ("Invalid Login", test_login_with_invalid_credentials),
        ("2FA Login", test_login_with_2fa_user),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} Test")
        print("-" * 30)
        results[test_name] = test_func()
    
    print("\n📊 Test Results Summary")
    print("=" * 50)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    if all_passed:
        print("\n🎉 All tests passed! CORS and 2FA authentication are working.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
