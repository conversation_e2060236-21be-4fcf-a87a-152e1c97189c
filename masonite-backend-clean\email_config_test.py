#!/usr/bin/env python3
"""
Email Configuration Test and Fix Script
Diagnoses and provides solutions for email authentication issues
"""

import os
import sys

# Add the masonite project to path
sys.path.append(os.path.join(os.path.dirname(__file__)))

# Initialize Masonite application
from wsgi import application

from masonite.facades import Mail
from masonite.environment import env
from app.mailables.EmailVerification import EmailVerification
from app.models.User import User

def test_email_configuration():
    """Test and diagnose email configuration issues"""
    
    print("🔍 Email Configuration Diagnostics")
    print("=" * 50)
    
    # Check environment variables
    print("📧 Current Email Configuration:")
    print(f"   MAIL_DRIVER: {env('MAIL_DRIVER', 'NOT SET')}")
    print(f"   MAIL_HOST: {env('MAIL_HOST', 'NOT SET')}")
    print(f"   MAIL_PORT: {env('MAIL_PORT', 'NOT SET')}")
    print(f"   MAIL_USERNAME: {env('MAIL_USERNAME', 'NOT SET')}")
    print(f"   MAIL_PASSWORD: {'***' if env('MAIL_PASSWORD') else 'NOT SET'}")
    print(f"   MAIL_FROM: {env('MAIL_FROM', 'NOT SET')}")
    
    print("\n🔧 Recommended Solutions:")
    print("=" * 50)
    
    print("SOLUTION 1: Use Terminal Driver (Development)")
    print("   - Set MAIL_DRIVER=terminal in .env")
    print("   - Emails will be printed to console instead of sent")
    print("   - Safe for development/testing")
    
    print("\nSOLUTION 2: Fix Brevo (Sendinblue) SMTP")
    print("   - Verify Brevo API key is correct")
    print("   - Check if account is active")
    print("   - Ensure sending domain is verified")
    
    print("\nSOLUTION 3: Use Gmail SMTP")
    print("   - MAIL_HOST=smtp.gmail.com")
    print("   - MAIL_PORT=587")
    print("   - Use App Password (not regular password)")
    
    print("\nSOLUTION 4: Use Mailgun")
    print("   - Set up Mailgun account")
    print("   - Configure MAILGUN_DOMAIN and MAILGUN_SECRET")
    print("   - Set MAIL_DRIVER=mailgun")

def apply_terminal_fix():
    """Apply terminal driver fix for immediate resolution"""
    env_file_path = ".env"
    
    try:
        # Read current .env file
        with open(env_file_path, 'r') as f:
            lines = f.readlines()
        
        # Update MAIL_DRIVER to terminal
        updated_lines = []
        driver_updated = False
        
        for line in lines:
            if line.startswith('MAIL_DRIVER='):
                updated_lines.append('MAIL_DRIVER=terminal\n')
                driver_updated = True
                print("✅ Updated MAIL_DRIVER to terminal")
            else:
                updated_lines.append(line)
        
        # If MAIL_DRIVER not found, add it
        if not driver_updated:
            updated_lines.append('MAIL_DRIVER=terminal\n')
            print("✅ Added MAIL_DRIVER=terminal")
        
        # Write back to .env file
        with open(env_file_path, 'w') as f:
            f.writelines(updated_lines)
            
        print("🎉 Terminal driver configuration applied!")
        print("📨 Emails will now be printed to console during development")
        
    except Exception as e:
        print(f"❌ Error updating .env file: {e}")

def test_email_sending():
    """Test email sending with current configuration"""
    try:
        # Find a test user or create one
        test_user = User.where('email', '<EMAIL>').first()
        
        if not test_user:
            print("⚠️  No test user found. Skipping email test.")
            return
        
        print("\n📧 Testing Email Sending...")
        
        # Generate a test token
        verification_token = "test_token_123"
        
        # Try to send email
        Mail.mailable(EmailVerification(test_user, verification_token).to(test_user.email)).send()
        
        print("✅ Email sent successfully!")
        
    except Exception as e:
        print(f"❌ Email sending failed: {e}")
        
        # Provide specific solutions based on error
        error_str = str(e)
        
        if "535" in error_str or "Authentication failed" in error_str:
            print("\n🔧 SMTP Authentication Error Solutions:")
            print("   1. Check if SMTP credentials are correct")
            print("   2. For Gmail: Use App Password instead of regular password")
            print("   3. For Brevo: Verify API key and account status")
            print("   4. Switch to terminal driver for development")
            
        elif "Connection refused" in error_str:
            print("\n🔧 Connection Error Solutions:")
            print("   1. Check if MAIL_HOST and MAIL_PORT are correct")
            print("   2. Verify firewall settings")
            print("   3. Check if SMTP service is running")
            
        elif "timeout" in error_str.lower():
            print("\n🔧 Timeout Error Solutions:")
            print("   1. Check internet connection")
            print("   2. Try different SMTP port (25, 465, 587)")
            print("   3. Verify SMTP server is accessible")

if __name__ == "__main__":
    print("🚀 Starting Email Configuration Test...")
    
    # Run diagnostics
    test_email_configuration()
    
    # Ask user for action
    print("\n" + "=" * 50)
    print("QUICK FIX OPTIONS:")
    print("1. Apply terminal driver fix (recommended for development)")
    print("2. Test current email configuration")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == "1":
        apply_terminal_fix()
        print("\n🔄 Please restart the Masonite server for changes to take effect")
    elif choice == "2":
        test_email_sending()
    else:
        print("👋 Exiting without changes")
