param(
    [string]$Method = "POST",
    [string]$Uri = "http://localhost:3002/api/auth/signup",
    [string]$Body = '{"firstName":"Test","lastName":"User","email":"<EMAIL>","password":"Test123!","confirmPassword":"Test123!"}'
)

try {
    $headers = @{
        'Content-Type' = 'application/json'
        'Origin' = 'http://localhost:4200'
    }
    
    Write-Host "Testing endpoint: $Uri"
    Write-Host "Method: $Method"
    Write-Host "Body: $Body"
    Write-Host "Headers: $($headers | ConvertTo-Json)"
    
    $response = Invoke-WebRequest -Uri $Uri -Method $Method -Body $Body -Headers $headers -UseBasicParsing
    
    Write-Host "Status Code: $($response.StatusCode)"
    Write-Host "Response Headers:"
    $response.Headers | Format-Table
    Write-Host "Response Body:"
    $response.Content
    
} catch {
    Write-Host "Error occurred:"
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.Value__)"
    Write-Host "Status Description: $($_.Exception.Response.StatusDescription)"
    
    if ($_.Exception.Response) {
        $result = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($result)
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody"
    }
}
