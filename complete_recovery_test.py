#!/usr/bin/env python3
"""
Complete Recovery Code Debug Test
Step-by-step test to reproduce and fix the recovery code issue
"""

import requests
import json
import time

# Configuration
BASE_URL = 'http://localhost:3002/api'
EMAIL = '<EMAIL>'
PASSWORD = 'SecurePass123!'

def make_request(method, endpoint, data=None, headers=None):
    """Make HTTP request with error handling"""
    url = f"{BASE_URL}{endpoint}"
    try:
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers, timeout=10)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, headers=headers, timeout=10)
        elif method.upper() == 'PUT':
            response = requests.put(url, json=data, headers=headers, timeout=10)
        else:
            raise ValueError(f"Unsupported method: {method}")
        
        print(f"📊 {method.upper()} {endpoint}")
        print(f"   Status: {response.status_code}")
        try:
            response_data = response.json()
            print(f"   Response: {json.dumps(response_data, indent=2)}")
        except:
            print(f"   Response: {response.text}")
        
        if response.status_code < 300:
            return True, response.json() if response.text else {}
        else:
            return False, response.json() if response.text else response.text
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False, str(e)

def test_complete_recovery_flow():
    """Test complete recovery code flow"""
    print("🧪 Complete Recovery Code Flow Test")
    print("=" * 60)
      # Step 1: Register user
    print("\n🔑 Step 1: Register new user")
    register_data = {
        'name': 'Recovery Test User',
        'firstName': 'Recovery',
        'lastName': 'Test',
        'email': EMAIL,
        'password': PASSWORD,
        'confirmPassword': PASSWORD
    }
    
    status, response = make_request('POST', '/auth/signup', register_data)
    if not status and 'already exists' not in str(response):
        print(f"❌ Registration failed: {response}")
        return False
    
    # Step 2: Login to get token
    print("\n🔑 Step 2: Login to get access token")
    login_data = {
        'email': EMAIL,
        'password': PASSWORD
    }
    
    status, response = make_request('POST', '/auth/login', login_data)
    if not status:
        print(f"❌ Login failed: {response}")
        return False
    
    access_token = response.get('access_token') or response.get('token')
    if not access_token:
        print(f"❌ No access token in response: {response}")
        return False
    
    print(f"✅ Login successful, got token: {access_token[:20]}...")
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    # Step 3: Setup 2FA to generate recovery codes
    print("\n🔐 Step 3: Setup 2FA (should generate recovery codes)")
    status, response = make_request('POST', '/2fa/setup', {}, headers)
    if not status:
        print(f"❌ 2FA setup failed: {response}")
        return False
    
    backup_codes = response.get('backupCodes', [])
    secret = response.get('secret')
    print(f"📊 2FA Setup Response:")
    print(f"   Secret: {secret[:10] if secret else 'None'}...")
    print(f"   Backup codes count: {len(backup_codes)}")
    for i, code in enumerate(backup_codes[:3]):
        print(f"   Code {i+1}: {code}")
    
    if not backup_codes:
        print("❌ No backup codes generated!")
        return False
    
    # Step 4: Enable 2FA (we'll skip TOTP verification for testing and enable manually)
    print("\n🔐 Step 4: Enable 2FA (using dummy token)")
    verify_data = {'token': '123456'}  # This will fail but let's see the error
    status, response = make_request('POST', '/2fa/verify', verify_data, headers)
    print(f"   2FA verification result: {status}")
    
    # Step 5: Check recovery codes status
    print("\n📊 Step 5: Check recovery codes status")
    status, response = make_request('GET', '/2fa/recovery-codes', headers=headers)
    if status:
        print("✅ Recovery codes status retrieved:")
        print(f"   Response: {json.dumps(response, indent=2)}")
    else:
        print(f"❌ Failed to get recovery codes status: {response}")
    
    # Step 6: Logout and test login with recovery code
    print("\n🔄 Step 6: Test login with recovery code")
    
    # Logout first (ignore result)
    make_request('POST', '/auth/logout', {}, headers)
    
    # Try login with recovery code
    recovery_code = backup_codes[0]
    print(f"\n🧪 Testing recovery code: {recovery_code}")
    
    login_with_recovery_data = {
        'email': EMAIL,
        'password': PASSWORD,
        'recoveryCode': recovery_code
    }
    
    status, response = make_request('POST', '/auth/login', login_with_recovery_data)
    if status:
        print(f"✅ Recovery code login successful!")
        print(f"   New token: {response.get('access_token', response.get('token', 'No token'))[:20]}...")
        return True
    else:
        print(f"❌ Recovery code login failed: {response}")
        
        # Analyze the error
        if 'error' in response:
            error_message = response['error'].get('message', '')
            print(f"🎯 ERROR ANALYSIS:")
            print(f"   Error: {error_message}")
            
            if 'No recovery codes remaining' in error_message:
                print("   🐛 BUG IDENTIFIED: System thinks no recovery codes remain")
                print("   🔍 This suggests recovery codes weren't properly stored during setup")
            elif 'Invalid recovery code' in error_message:
                print("   🐛 BUG IDENTIFIED: Recovery code validation failed")
                print("   🔍 This suggests recovery code hashing/comparison issue")
            elif 'Two-factor authentication is not enabled' in error_message:
                print("   🐛 BUG IDENTIFIED: 2FA not properly enabled")
                print("   🔍 User's 2FA status wasn't set correctly")
        
        return False

def debug_database_state():
    """Check what's happening in the database"""
    print("\n" + "=" * 60)
    print("🔍 Database State Debug")
    
    # Login again to check user state
    login_data = {'email': EMAIL, 'password': PASSWORD}
    status, response = make_request('POST', '/auth/login', login_data)
    
    if status:
        token = response.get('access_token') or response.get('token')
        headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
        
        # Check 2FA status
        print("\n📊 Checking 2FA status...")
        status, response = make_request('GET', '/2fa/status', headers=headers)
        if status:
            print(f"   2FA Status: {json.dumps(response, indent=2)}")
        
        # Check recovery codes status
        print("\n📊 Checking recovery codes status...")
        status, response = make_request('GET', '/2fa/recovery-codes', headers=headers)
        if status:
            print(f"   Recovery Codes: {json.dumps(response, indent=2)}")
        else:
            print(f"   Recovery Codes Error: {response}")

if __name__ == '__main__':
    success = test_complete_recovery_flow()
    debug_database_state()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Recovery code test PASSED")
    else:
        print("❌ Recovery code test FAILED - Issue identified for fixing")
