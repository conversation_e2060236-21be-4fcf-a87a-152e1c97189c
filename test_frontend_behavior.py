#!/usr/bin/env python3
"""
Test frontend behavior - what happens when restorePreservedData is not sent
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3002/api"

def test_frontend_behavior():
    """Test when frontend doesn't send restorePreservedData parameter"""
    email = f"test.frontend.{int(time.time())}@example.com"
    password = "TestPassword123!"
    
    print("🧪 Testing Frontend Behavior (no restorePreservedData parameter)")
    print("=" * 70)
    
    # Step 1: Register user
    print("📝 Step 1: Registering user...")
    register_data = {
        "email": email,
        "password": password,
        "confirmPassword": password,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+1234567890"
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=register_data)
    if response.status_code not in [200, 201]:
        print(f"❌ Registration failed: {response.text}")
        return False
    
    user_data = response.json()
    user_id = user_data.get('userId') or user_data.get('user', {}).get('id')
    token = user_data.get('token')
    print(f"✅ User registered with ID: {user_id}")
    
    # Step 2: Request deletion with only payment data preservation
    print("\n📝 Step 2: Requesting deletion with ONLY payment data preservation...")
    deletion_data = {
        "preservePaymentData": True,   # Only preserve payment data
        "preserveTransactionHistory": False,
        "preserveProfileData": False,
        "preserveSecurityLogs": False,
        "customRetentionPeriod": 30,
        "reason": "Testing frontend behavior"
    }
    
    response = requests.post(f"{BASE_URL}/account/request-deletion", 
                           json=deletion_data,
                           headers={'Authorization': f'Bearer {token}'})
    
    if response.status_code != 200:
        print(f"❌ Deletion request failed: {response.text}")
        return False
    
    deletion_response = response.json()
    deletion_id = deletion_response['deletionId']
    confirmation_token = deletion_response['confirmationToken']
    print(f"✅ Deletion requested with ID: {deletion_id}")
    
    # Step 3: Confirm deletion
    print("\n📝 Step 3: Confirming deletion...")
    response = requests.post(f"{BASE_URL}/account/confirm-deletion", 
                           json={'token': confirmation_token})
    
    if response.status_code != 200:
        print(f"❌ Deletion confirmation failed: {response.text}")
        return False
    
    confirm_response = response.json()
    print("✅ Deletion confirmed")
    print(f"📄 Preserved data summary: {confirm_response.get('preservedDataSummary', {})}")
    
    # Step 4: Check preserved data
    print("\n📝 Step 4: Checking preserved data...")
    response = requests.post(f"{BASE_URL}/account/check-preserved-data", 
                           json={'email': email})
    
    if response.status_code != 200:
        print(f"❌ Check preserved data failed: {response.text}")
        return False
    
    check_response = response.json()
    has_preserved = check_response.get('hasPreservedData', False)
    preserved_summary = check_response.get('preservedDataSummary', {})
    
    print(f"📊 Has preserved data: {has_preserved}")
    print(f"📊 Preserved data summary: {preserved_summary}")
    
    if not has_preserved:
        print("❌ FAIL: Expected preserved data but found none")
        return False
    
    # Validate only payment data is preserved
    if 'payments' not in preserved_summary:
        print("❌ FAIL: Payment data should be preserved")
        return False
    
    if 'profile' in preserved_summary or 'security' in preserved_summary or 'transactions' in preserved_summary:
        print(f"❌ FAIL: Only payment data should be preserved, but found: {preserved_summary}")
        return False
    
    print("✅ PASS: Only payment data is preserved as expected")
    
    # Step 5: Test signup WITHOUT restorePreservedData parameter (simulating frontend)
    print("\n📝 Step 5: Testing signup WITHOUT restorePreservedData parameter...")
    signup_data = {
        "email": email,
        "password": password,
        "confirmPassword": password,
        "firstName": "Test",
        "lastName": "User",
        "phone": "+1234567890"
        # NOTE: No restorePreservedData parameter - simulating frontend behavior
    }
    
    response = requests.post(f"{BASE_URL}/auth/signup", json=signup_data)
    
    if response.status_code not in [200, 201]:
        print(f"❌ Second registration failed: {response.text}")
        return False
    
    signup_response = response.json()
    signup_has_preserved = signup_response.get('hasPreservedData', False)
    signup_preserved_summary = signup_response.get('preservedDataSummary', {})
    data_cleaned_up = signup_response.get('dataCleanedUp', False)
    preserved_data_restored = signup_response.get('preservedDataRestored', False)
    message = signup_response.get('message', '')
    
    print(f"📊 Signup response - Has preserved data: {signup_has_preserved}")
    print(f"📊 Signup response - Preserved summary: {signup_preserved_summary}")
    print(f"📊 Signup response - Data cleaned up: {data_cleaned_up}")
    print(f"📊 Signup response - Data restored: {preserved_data_restored}")
    print(f"📊 Signup response - Message: {message}")
    
    # Step 6: Validate expectations
    print("\n📝 Step 6: Validating expectations...")
    
    # When restorePreservedData is not specified, data should NOT be automatically restored
    if preserved_data_restored:
        print("❌ FAIL: Data was automatically restored when no preference was specified")
        print("❌ This is the issue you mentioned - unpreserved data being restored!")
        return False
    
    # Data should NOT be cleaned up when no preference is specified
    if data_cleaned_up:
        print("❌ FAIL: Data was cleaned up when no preference was specified")
        return False
    
    # Preserved data should still be available
    if not signup_has_preserved:
        print("❌ FAIL: Preserved data should still be available when no preference is specified")
        return False
    
    print("✅ PASS: Data was not automatically restored when no preference specified")
    print("✅ PASS: Preserved data is still available for future restoration")
    
    # Step 7: Check preserved data is still there
    print("\n📝 Step 7: Checking preserved data is still available...")
    response = requests.post(f"{BASE_URL}/account/check-preserved-data", 
                           json={'email': email})
    
    if response.status_code != 200:
        print(f"❌ Check preserved data failed: {response.text}")
        return False
    
    final_check_response = response.json()
    final_has_preserved = final_check_response.get('hasPreservedData', False)
    final_preserved_summary = final_check_response.get('preservedDataSummary', {})
    
    print(f"📊 Final check - Has preserved data: {final_has_preserved}")
    print(f"📊 Final check - Preserved summary: {final_preserved_summary}")
    
    # Preserved data should still be there
    if not final_has_preserved:
        print("❌ FAIL: Preserved data should still be available")
        return False
    
    if final_preserved_summary != preserved_summary:
        print(f"❌ FAIL: Preserved data summary changed unexpectedly")
        print(f"   Original: {preserved_summary}")
        print(f"   Final:    {final_preserved_summary}")
        return False
    
    print("✅ PASS: Preserved data is still available for future restoration")
    print("✅ PASS: All validations successful!")
    
    return True

def main():
    """Main test function"""
    print("🚀 Starting Frontend Behavior Test")
    print("Testing when frontend doesn't send restorePreservedData parameter")
    print("=" * 80)
    
    success = test_frontend_behavior()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 TEST PASSED! Frontend behavior is working correctly!")
        print("✅ No automatic restoration of unpreserved data")
    else:
        print("❌ TEST FAILED! Found the issue with unpreserved data restoration!")
    print("🏁 Test completed!")

if __name__ == "__main__":
    main()
