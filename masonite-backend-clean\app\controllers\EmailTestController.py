from masonite.controllers import Controller
from masonite.request import Request
from masonite.response import Response
from masonite.facades import Mail
from app.mailables.OTPMailable import OTPMailable
from app.controllers.CorsController import CorsController


class EmailTestController(Controller):
    """Debug controller to test email functionality"""
    
    def test_otp_email(self, request: Request, response: Response):
        """Test OTP email sending directly"""
        try:
            test_email = request.input('email', '<EMAIL>')
            test_code = '123456'
            
            print(f"🧪 Testing OTP email to: {test_email}")
            
            # Send OTP email using same pattern as forgot password
            Mail.mailable(OTPMailable(test_code, 'test').to(test_email)).send()
            
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'message': f'Test OTP email sent to {test_email}',
                'code': test_code
            })
            
        except Exception as e:
            print(f"❌ Email test error: {str(e)}")
            CorsController.add_cors_headers(response, request.header('Origin'))
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'EmailTestError',
                    'message': str(e)
                }
            }, 500)
