$Body = '{"firstName":"Enhanced","lastName":"Test","email":"<EMAIL>","password":"EnhancedTest123!","confirmPassword":"EnhancedTest123!"}'
$Headers = @{
    'Content-Type' = 'application/json'
    'Origin' = 'http://localhost:4200'
}

try {
    Write-Host "Testing enhanced signup endpoint..."
    $response = Invoke-WebRequest -Uri "http://localhost:3002/api/auth/signup" -Method POST -Body $Body -Headers $Headers -UseBasicParsing
    Write-Host "Status: $($response.StatusCode)"
    Write-Host "Response: $($response.Content)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $result = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($result)
        $responseBody = $reader.ReadToEnd()
        Write-Host "Error Body: $responseBody"
    }
}
