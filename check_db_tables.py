#!/usr/bin/env python3
"""
Quick script to check what tables exist in the database
"""

import sqlite3
import os

# Database path
DB_PATH = "masonite-backend-clean/masonite.sqlite3"

def main():
    if not os.path.exists(DB_PATH):
        print(f"❌ Database not found at {DB_PATH}")
        return
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Get all table names
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📊 Found {len(tables)} tables in the database:")
        for table in tables:
            print(f"   • {table[0]}")
            
        # If no user table, check for similar ones
        if not any('user' in table[0].lower() for table in tables):
            print("\n⚠️  No 'users' table found. Looking for user-related tables...")
            for table in tables:
                if any(word in table[0].lower() for word in ['user', 'account', 'auth']):
                    print(f"   🔍 Found: {table[0]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database: {str(e)}")

if __name__ == "__main__":
    main()
