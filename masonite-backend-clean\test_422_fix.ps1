param(
    [string]$TestEndpoint = "both"
)

Write-Host "🚀 Testing Authentication Endpoints - 422 Error Fix Validation"
Write-Host "=" * 70

$BaseUrl = "http://localhost:3002/api"
$TestEmail = "<EMAIL>"  # Use the user we created earlier
$TestPassword = "EnhancedTest123!"

# Function to test signup endpoint
function Test-SignupEndpoint {
    Write-Host "`n🧪 Testing SIGNUP endpoint..."
    
    $signupData = @{
        firstName = "Final"
        lastName = "Test"
        email = "final.test.$(Get-Random)@example.com"  # Random email to avoid conflicts
        password = "FinalTest123!"
        confirmPassword = "FinalTest123!"
    } | ConvertTo-Json
    
    $headers = @{
        'Content-Type' = 'application/json'
        'Origin' = 'http://localhost:4200'
        'X-Requested-With' = 'XMLHttpRequest'
    }
    
    try {
        Write-Host "📤 Sending signup request..."
        Write-Host "Data: $signupData"
        
        $response = Invoke-WebRequest -Uri "$BaseUrl/auth/signup" -Method POST -Body $signupData -Headers $headers -UseBasicParsing
        
        Write-Host "✅ Signup SUCCESS - Status: $($response.StatusCode)"
        $responseObj = $response.Content | ConvertFrom-Json
        Write-Host "✅ Token received: $($responseObj.token.Substring(0, 20))..."
        Write-Host "✅ User created: $($responseObj.user.firstName) $($responseObj.user.lastName)"
        
        return $true
        
    } catch {
        Write-Host "❌ Signup FAILED:"
        Write-Host "  Status: $($_.Exception.Response.StatusCode.Value__)"
        
        if ($_.Exception.Response.StatusCode.Value__ -eq 422) {
            Write-Host "  This is a 422 Validation Error (expected for invalid data)"
            $result = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($result)
            $responseBody = $reader.ReadToEnd()
            Write-Host "  Error details: $responseBody"
        }
        
        return $false
    }
}

# Function to test change password endpoint
function Test-ChangePasswordEndpoint {
    Write-Host "`n🧪 Testing CHANGE PASSWORD endpoint..."
    
    # First login
    $loginData = @{
        email = $TestEmail
        password = $TestPassword
    } | ConvertTo-Json
    
    $headers = @{
        'Content-Type' = 'application/json'
        'Origin' = 'http://localhost:4200'
        'X-Requested-With' = 'XMLHttpRequest'
    }
    
    try {
        Write-Host "🔐 Step 1: Login..."
        $loginResponse = Invoke-WebRequest -Uri "$BaseUrl/auth/login" -Method POST -Body $loginData -Headers $headers -UseBasicParsing
        $loginObj = $loginResponse.Content | ConvertFrom-Json
        $token = $loginObj.token
        
        if (-not $token) {
            Write-Host "❌ No token received from login"
            return $false
        }
        
        Write-Host "✅ Login successful"
        
        # Now change password
        $changePasswordData = @{
            currentPassword = $TestPassword
            newPassword = "NewEnhancedTest123!"
        } | ConvertTo-Json
        
        $authHeaders = @{
            'Content-Type' = 'application/json'
            'Authorization' = "Bearer $token"
            'Origin' = 'http://localhost:4200'
            'X-Requested-With' = 'XMLHttpRequest'
        }
        
        Write-Host "🔄 Step 2: Change password..."
        Write-Host "Data: $changePasswordData"
        
        $response = Invoke-WebRequest -Uri "$BaseUrl/auth/change-password" -Method POST -Body $changePasswordData -Headers $authHeaders -UseBasicParsing
        
        Write-Host "✅ Change Password SUCCESS - Status: $($response.StatusCode)"
        $responseObj = $response.Content | ConvertFrom-Json
        Write-Host "✅ Message: $($responseObj.message)"
        
        return $true
        
    } catch {
        Write-Host "❌ Change Password FAILED:"
        Write-Host "  Status: $($_.Exception.Response.StatusCode.Value__)"
        Write-Host "  Error: $($_.Exception.Message)"
        
        if ($_.Exception.Response.StatusCode.Value__ -eq 422) {
            Write-Host "  This is a 422 Validation Error"
            $result = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($result)
            $responseBody = $reader.ReadToEnd()
            Write-Host "  Error details: $responseBody"
        }
        
        return $false
    }
}

# Function to test malformed data (should return 422)
function Test-ValidationErrors {
    Write-Host "`n🧪 Testing VALIDATION ERRORS (should return 422)..."
    
    # Test 1: Signup with missing fields
    $malformedSignup = @{
        email = "<EMAIL>"
        firstName = "Test"
        # Missing lastName, password, confirmPassword
    } | ConvertTo-Json
    
    $headers = @{
        'Content-Type' = 'application/json'
        'Origin' = 'http://localhost:4200'
        'X-Requested-With' = 'XMLHttpRequest'
    }
    
    try {
        Write-Host "📤 Testing signup with missing fields..."
        $response = Invoke-WebRequest -Uri "$BaseUrl/auth/signup" -Method POST -Body $malformedSignup -Headers $headers -UseBasicParsing
        Write-Host "❌ Should have failed with 422 but got: $($response.StatusCode)"
        return $false
    } catch {
        if ($_.Exception.Response.StatusCode.Value__ -eq 422) {
            Write-Host "✅ Correctly returned 422 for invalid signup data"
            return $true
        } else {
            Write-Host "❌ Got status $($_.Exception.Response.StatusCode.Value__) instead of 422"
            return $false
        }
    }
}

# Main execution
$results = @{}

switch ($TestEndpoint) {
    "signup" { $results['signup'] = Test-SignupEndpoint }
    "change-password" { $results['change-password'] = Test-ChangePasswordEndpoint }
    "validation" { $results['validation'] = Test-ValidationErrors }
    "both" {
        $results['signup'] = Test-SignupEndpoint
        $results['change-password'] = Test-ChangePasswordEndpoint
        $results['validation'] = Test-ValidationErrors
    }
    default { 
        Write-Host "Usage: .\test_422_fix.ps1 -TestEndpoint [signup|change-password|validation|both]"
        exit 1
    }
}

# Summary
Write-Host "`n" + ("=" * 70)
Write-Host "📊 TEST RESULTS SUMMARY"
Write-Host ("=" * 70)

$allPassed = $true
foreach ($test in $results.Keys) {
    if ($results[$test]) {
        Write-Host "✅ $($test.ToUpper()): PASSED"
    } else {
        Write-Host "❌ $($test.ToUpper()): FAILED"
        $allPassed = $false
    }
}

Write-Host "`n🎯 OVERALL RESULT: $(if ($allPassed) { "✅ ALL TESTS PASSED" } else { "❌ SOME TESTS FAILED" })"

if ($allPassed) {
    Write-Host "🎉 The 422 error fix is working correctly!"
    Write-Host "   - Signup endpoint: Working ✅"
    Write-Host "   - Change password endpoint: Working ✅"
    Write-Host "   - Validation errors: Properly handled ✅"
} else {
    Write-Host "⚠️  Some issues still need to be addressed."
}

Write-Host "`n✅ Test completed!"
