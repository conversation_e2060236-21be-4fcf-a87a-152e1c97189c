# Simple 422 error test
$BaseUrl = "http://localhost:3002/api"

Write-Host "Testing 422 error handling..."

# Test signup with missing fields
$malformedData = '{"email":"<EMAIL>","password":"Test123!"}'
$headers = @{'Content-Type'='application/json'}

try {
    $response = Invoke-WebRequest -Uri "$BaseUrl/auth/signup" -Method POST -Body $malformedData -Headers $headers -UseBasicParsing
    Write-Host "FAIL: Should have returned 422 but got $($response.StatusCode)"
} catch {
    if ($_.Exception.Response.StatusCode.Value__ -eq 422) {
        Write-Host "PASS: Signup correctly returns 422 for invalid data"
    } else {
        Write-Host "FAIL: Got status $($_.Exception.Response.StatusCode.Value__) instead of 422"
    }
}

Write-Host "Test completed."
