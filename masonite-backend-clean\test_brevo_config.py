"""
Quick Brevo API Configuration Test
"""

import os
from masonite.environment import env

def test_brevo_config():
    """Test Brevo API configuration"""
    
    print("🔧 Checking Brevo API Configuration")
    print("=" * 40)
    
    # Check if .env file exists
    env_file = ".env"
    if os.path.exists(env_file):
        print("✅ .env file found")
    else:
        print("❌ .env file not found")
    
    # Check mail configuration
    print(f"MAIL_DRIVER: {env('MAIL_DRIVER', 'NOT_SET')}")
    print(f"MAIL_FROM: {env('MAIL_FROM', 'NOT_SET')}")
    print(f"MAIL_HOST: {env('MAIL_HOST', 'NOT_SET')}")
    print(f"MAIL_PORT: {env('MAIL_PORT', 'NOT_SET')}")
    
    # Check if Brevo API key is configured
    brevo_key = env('BREVO_API_KEY', 'NOT_SET')
    if brevo_key == 'NOT_SET':
        print("⚠️  BREVO_API_KEY not configured - will fall back to SMTP")
    else:
        print(f"✅ BREVO_API_KEY configured (length: {len(brevo_key)})")
    
    print("\n🧪 Testing BrevoEmailService import...")
    try:
        from app.services.BrevoEmailService import BrevoEmailService
        brevo_service = BrevoEmailService()
        print("✅ BrevoEmailService imported successfully")
        print(f"   API Key configured: {brevo_service.api_key != 'your-brevo-api-key-here'}")
        print(f"   From email: {brevo_service.from_email}")
    except Exception as e:
        print(f"❌ BrevoEmailService import failed: {str(e)}")

if __name__ == "__main__":
    test_brevo_config()
