#!/usr/bin/env python3
"""
Simple test to check if the route is working
"""

import requests
import urllib.parse

# Configuration
BASE_URL = "http://localhost:3002/api"

def test_simple_routes():
    """Test simple routes to see what's working"""
    print("🧪 Testing Simple Routes...")
    
    # Test 1: Basic health check
    try:
        response = requests.get(f"{BASE_URL}/auth/profile")
        print(f"✅ /auth/profile Status: {response.status_code}")
        if response.status_code == 401:
            print("✅ Expected 401 for unauthenticated request")
    except Exception as e:
        print(f"❌ /auth/profile error: {str(e)}")
    
    # Test 2: Try with a simple email parameter
    simple_email = "<EMAIL>"
    encoded_email = urllib.parse.quote(simple_email, safe='')
    
    print(f"\n📧 Testing with email: {simple_email}")
    print(f"📧 Encoded email: {encoded_email}")
    
    # Test with encoded email
    try:
        url = f"{BASE_URL}/account/check-preserved-data/{encoded_email}"
        print(f"📡 Testing URL: {url}")
        response = requests.get(url)
        print(f"✅ Encoded email Status: {response.status_code}")
        print(f"📄 Response: {response.text[:200]}...")
    except Exception as e:
        print(f"❌ Encoded email error: {str(e)}")
    
    # Test with unencoded email
    try:
        url = f"{BASE_URL}/account/check-preserved-data/{simple_email}"
        print(f"📡 Testing URL: {url}")
        response = requests.get(url)
        print(f"✅ Unencoded email Status: {response.status_code}")
        print(f"📄 Response: {response.text[:200]}...")
    except Exception as e:
        print(f"❌ Unencoded email error: {str(e)}")

def test_options_request():
    """Test OPTIONS request for CORS"""
    print("\n🧪 Testing OPTIONS Request...")
    
    simple_email = "<EMAIL>"
    encoded_email = urllib.parse.quote(simple_email, safe='')
    
    try:
        url = f"{BASE_URL}/account/check-preserved-data/{encoded_email}"
        response = requests.options(url, headers={
            'Origin': 'http://localhost:4200',
            'Access-Control-Request-Method': 'GET'
        })
        print(f"✅ OPTIONS Status: {response.status_code}")
        print(f"📋 CORS Headers: {dict(response.headers)}")
    except Exception as e:
        print(f"❌ OPTIONS error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting Simple Route Tests")
    print("=" * 50)
    
    test_simple_routes()
    test_options_request()
    
    print("\n" + "=" * 50)
    print("🏁 Tests completed!")
