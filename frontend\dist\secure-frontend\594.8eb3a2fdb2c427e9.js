"use strict";(self.webpackChunksecure_frontend=self.webpackChunksecure_frontend||[]).push([[594],{7594:(L,F,r)=>{r.r(F),r.d(F,{AccountDeletionComponent:()=>Y});var f=r(467),a=r(9417),m=r(6396),u=r(5596),v=r(1074),p=r(8834),h=r(2506),y=r(6687),k=r(6863),_=r(9183),d=r(357),e=r(7241);let x=(()=>{class n{constructor(t,o){this.dialogRef=t,this.data=o}onConfirm(){this.dialogRef.close(!0)}onCancel(){this.dialogRef.close(!1)}static#e=this.\u0275fac=function(o){return new(o||n)(e.rXU(d.k),e.rXU(d.e))};static#t=this.\u0275cmp=e.VBU({type:n,selectors:[["app-confirmation-dialog"]],decls:10,vars:5,consts:[["mat-dialog-content",""],["mat-dialog-title",""],["mat-dialog-actions","","align","end"],["mat-button","",3,"click"],["mat-raised-button","",3,"click","color"]],template:function(o,i){1&o&&(e.j41(0,"div",0)(1,"h2",1),e.EFF(2),e.k0s(),e.j41(3,"p"),e.EFF(4),e.k0s()(),e.j41(5,"div",2)(6,"button",3),e.bIt("click",function(){return i.onCancel()}),e.EFF(7),e.k0s(),e.j41(8,"button",4),e.bIt("click",function(){return i.onConfirm()}),e.EFF(9),e.k0s()()),2&o&&(e.R7$(2),e.JRh(i.data.title),e.R7$(2),e.JRh(i.data.message),e.R7$(3),e.SpI(" ",i.data.cancelText," "),e.R7$(),e.Y8G("color",i.data.isDangerous?"warn":"primary"),e.R7$(),e.SpI(" ",i.data.confirmText," "))},dependencies:[m.MD,d.l,d.b,d.M,d.c,p.Hl,p.$z],styles:[".dialog-content[_ngcontent-%COMP%]{padding:20px}.dialog-actions[_ngcontent-%COMP%]{gap:8px}button[_ngcontent-%COMP%]{margin-left:8px}"]})}return n})();var c=r(8564),D=r(3890),P=r(4796),b=r(5333),E=r(8822),g=r(3364);function j(n,s){1&n&&(e.j41(0,"mat-error"),e.EFF(1," Please enter a valid number between 1 and 365 "),e.k0s())}function R(n,s){if(1&n&&(e.j41(0,"li"),e.EFF(1),e.k0s()),2&n){const t=s.$implicit;e.R7$(),e.JRh(t)}}function M(n,s){if(1&n&&(e.j41(0,"div",24)(1,"h4"),e.EFF(2,"Data Preservation Summary"),e.k0s(),e.j41(3,"ul"),e.DNE(4,R,2,1,"li",25),e.k0s(),e.j41(5,"p",26)(6,"mat-icon"),e.EFF(7,"schedule"),e.k0s(),e.EFF(8," Preserved data will be kept for "),e.j41(9,"strong"),e.EFF(10),e.k0s()()()),2&n){const t=e.XpG(2);e.R7$(4),e.Y8G("ngForOf",t.preservedDataSummary),e.R7$(6),e.JRh(t.retentionPeriodText)}}function T(n,s){1&n&&(e.j41(0,"mat-error"),e.EFF(1," You must confirm before proceeding "),e.k0s())}function S(n,s){1&n&&e.nrm(0,"mat-spinner",27)}function O(n,s){1&n&&(e.j41(0,"mat-icon"),e.EFF(1,"delete_forever"),e.k0s())}function A(n,s){if(1&n){const t=e.RV6();e.j41(0,"div",4)(1,"div",5)(2,"mat-icon",2),e.EFF(3,"info"),e.k0s(),e.j41(4,"p")(5,"strong"),e.EFF(6,"Warning:"),e.k0s(),e.EFF(7," Account deletion is permanent and cannot be undone immediately. Please carefully review your preferences below. "),e.k0s()(),e.j41(8,"form",6),e.bIt("ngSubmit",function(){c.eBV(t);const i=e.XpG();return c.Njj(i.requestDeletion())}),e.j41(9,"h3"),e.EFF(10,"Data Preservation Preferences"),e.k0s(),e.j41(11,"p",7),e.EFF(12," Choose which data you want to preserve for potential restoration: "),e.k0s(),e.j41(13,"div",8)(14,"mat-checkbox",9)(15,"strong"),e.EFF(16,"Preserve Payment Data"),e.k0s(),e.j41(17,"div",10),e.EFF(18,"Keep payment methods and billing information"),e.k0s()(),e.j41(19,"mat-checkbox",11)(20,"strong"),e.EFF(21,"Preserve Transaction History"),e.k0s(),e.j41(22,"div",10),e.EFF(23,"Keep records of past transactions and orders"),e.k0s()(),e.j41(24,"mat-checkbox",12)(25,"strong"),e.EFF(26,"Preserve Profile Backup"),e.k0s(),e.j41(27,"div",10),e.EFF(28,"Keep a backup of your profile information"),e.k0s()(),e.j41(29,"mat-checkbox",13)(30,"strong"),e.EFF(31,"Preserve Security Logs"),e.k0s(),e.j41(32,"div",10),e.EFF(33,"Keep login history and security events"),e.k0s()()(),e.j41(34,"mat-form-field",14)(35,"mat-label"),e.EFF(36,"Data Retention Period (days)"),e.k0s(),e.nrm(37,"input",15),e.j41(38,"mat-hint"),e.EFF(39,"How long to keep your preserved data (1-365 days)"),e.k0s(),e.DNE(40,j,2,0,"mat-error",16),e.k0s(),e.j41(41,"mat-form-field",14)(42,"mat-label"),e.EFF(43,"Reason for Deletion (Optional)"),e.k0s(),e.nrm(44,"textarea",17),e.j41(45,"mat-hint"),e.EFF(46,"This helps us improve our service"),e.k0s()(),e.DNE(47,M,11,2,"div",18),e.j41(48,"mat-checkbox",19)(49,"strong"),e.EFF(50,"I understand that this action cannot be undone immediately"),e.k0s()(),e.DNE(51,T,2,0,"mat-error",16),e.j41(52,"div",20)(53,"button",21),e.bIt("click",function(){c.eBV(t);const i=e.XpG();return c.Njj(i.goToSettings())}),e.j41(54,"mat-icon"),e.EFF(55,"arrow_back"),e.k0s(),e.EFF(56," Back to Settings "),e.k0s(),e.j41(57,"button",22),e.DNE(58,S,1,0,"mat-spinner",23)(59,O,2,0,"mat-icon",16),e.EFF(60," Request Account Deletion "),e.k0s()()()()}if(2&n){let t,o;const i=e.XpG();e.R7$(8),e.Y8G("formGroup",i.deletionForm),e.R7$(32),e.Y8G("ngIf",null==(t=i.deletionForm.get("customRetentionPeriod"))?null:t.invalid),e.R7$(7),e.Y8G("ngIf",i.preservedDataSummary.length>0),e.R7$(4),e.Y8G("ngIf",(null==(o=i.deletionForm.get("confirmDeletion"))?null:o.invalid)&&(null==(o=i.deletionForm.get("confirmDeletion"))?null:o.touched)),e.R7$(6),e.Y8G("disabled",i.isLoading||i.deletionForm.invalid),e.R7$(),e.Y8G("ngIf",i.isLoading),e.R7$(),e.Y8G("ngIf",!i.isLoading)}}function I(n,s){if(1&n){const t=e.RV6();e.j41(0,"div",4)(1,"div",28)(2,"mat-icon",29),e.EFF(3,"mail"),e.k0s(),e.j41(4,"h3"),e.EFF(5,"Check Your Email"),e.k0s(),e.j41(6,"p"),e.EFF(7," We've sent a confirmation email with a link to complete your account deletion. Please check your inbox and follow the instructions. "),e.k0s(),e.j41(8,"p",30)(9,"strong"),e.EFF(10,"Note:"),e.k0s(),e.EFF(11," The confirmation link will expire in 24 hours. "),e.k0s()(),e.j41(12,"div",20)(13,"button",31),e.bIt("click",function(){c.eBV(t);const i=e.XpG();return c.Njj(i.step="preferences")}),e.j41(14,"mat-icon"),e.EFF(15,"edit"),e.k0s(),e.EFF(16," Change Preferences "),e.k0s(),e.j41(17,"button",32),e.bIt("click",function(){c.eBV(t);const i=e.XpG();return c.Njj(i.checkExistingDeletionStatus())}),e.j41(18,"mat-icon"),e.EFF(19,"refresh"),e.k0s(),e.EFF(20," Refresh Status "),e.k0s()()()}}function G(n,s){if(1&n&&(e.j41(0,"div",39)(1,"strong"),e.EFF(2,"Data Expiry:"),e.k0s(),e.EFF(3),e.nI1(4,"date"),e.k0s()),2&n){const t=e.XpG(3);e.R7$(3),e.SpI(" ",e.i5U(4,1,t.currentStatus.deletionRecord.dataExpiryDate,"medium")," ")}}function $(n,s){if(1&n&&(e.j41(0,"div",38)(1,"h4"),e.EFF(2,"Request Details"),e.k0s(),e.j41(3,"div",39)(4,"strong"),e.EFF(5,"Email:"),e.k0s(),e.EFF(6),e.k0s(),e.j41(7,"div",39)(8,"strong"),e.EFF(9,"Requested:"),e.k0s(),e.EFF(10),e.nI1(11,"date"),e.k0s(),e.j41(12,"div",39)(13,"strong"),e.EFF(14,"Status:"),e.k0s(),e.j41(15,"span",40),e.EFF(16),e.nI1(17,"titlecase"),e.k0s()(),e.DNE(18,G,5,4,"div",41),e.k0s()),2&n){const t=e.XpG(2);e.R7$(6),e.SpI(" ",t.currentStatus.deletionRecord.email," "),e.R7$(4),e.SpI(" ",e.i5U(11,4,t.currentStatus.deletionRecord.deletionRequestedAt,"medium")," "),e.R7$(6),e.JRh(e.bMT(17,7,t.currentStatus.deletionRecord.deletionStatus)),e.R7$(2),e.Y8G("ngIf",t.currentStatus.deletionRecord.dataExpiryDate)}}function N(n,s){1&n&&e.nrm(0,"mat-spinner",27)}function w(n,s){1&n&&(e.j41(0,"mat-icon"),e.EFF(1,"cancel"),e.k0s())}function X(n,s){if(1&n){const t=e.RV6();e.j41(0,"div",4)(1,"div",33)(2,"mat-icon",34),e.EFF(3,"pending"),e.k0s(),e.j41(4,"h3"),e.EFF(5,"Deletion Request Pending"),e.k0s(),e.j41(6,"p"),e.EFF(7,"Your account deletion request is currently pending confirmation."),e.k0s()(),e.DNE(8,$,19,9,"div",35),e.j41(9,"div",20)(10,"button",36),e.bIt("click",function(){c.eBV(t);const i=e.XpG();return c.Njj(i.checkExistingDeletionStatus())}),e.j41(11,"mat-icon"),e.EFF(12,"refresh"),e.k0s(),e.EFF(13," Refresh Status "),e.k0s(),e.j41(14,"button",37),e.bIt("click",function(){c.eBV(t);const i=e.XpG();return c.Njj(i.cancelDeletion())}),e.DNE(15,N,1,0,"mat-spinner",23)(16,w,2,0,"mat-icon",16),e.EFF(17," Cancel Deletion Request "),e.k0s()()()}if(2&n){const t=e.XpG();e.R7$(8),e.Y8G("ngIf",t.currentStatus.deletionRecord),e.R7$(6),e.Y8G("disabled",t.isLoading),e.R7$(),e.Y8G("ngIf",t.isLoading),e.R7$(),e.Y8G("ngIf",!t.isLoading)}}let Y=(()=>{class n{constructor(t,o,i,l,C,B){this.fb=t,this.accountDeletionService=o,this.authService=i,this.router=l,this.snackBar=C,this.dialog=B,this.currentStatus=null,this.isLoading=!1,this.step="preferences",this.deletionForm=this.fb.group({preservePaymentData:[!0],preserveTransactionHistory:[!0],preserveProfileData:[!1],preserveSecurityLogs:[!1],customRetentionPeriod:[30,[a.k0.min(1),a.k0.max(365)]],reason:["",a.k0.maxLength(500)],confirmDeletion:[!1,a.k0.requiredTrue]})}ngOnInit(){this.checkExistingDeletionStatus()}checkExistingDeletionStatus(){var t=this;return(0,f.A)(function*(){try{const o=yield t.accountDeletionService.getDeletionStatus().toPromise();o&&o.hasPendingDeletion&&o.deletionRecord&&(t.currentStatus=o,t.step="status")}catch(o){console.error("Error checking deletion status:",o)}})()}requestDeletion(){var t=this;return(0,f.A)(function*(){if(t.deletionForm.invalid)t.markFormGroupTouched();else if(yield t.dialog.open(x,{width:"400px",data:{title:"Confirm Account Deletion",message:"Are you absolutely sure you want to delete your account? This action cannot be undone immediately.",confirmText:"Yes, Delete My Account",cancelText:"Cancel",isDangerous:!0}}).afterClosed().toPromise()){t.isLoading=!0;try{const l={preservePaymentData:t.deletionForm.value.preservePaymentData,preserveTransactionHistory:t.deletionForm.value.preserveTransactionHistory,preserveProfileData:t.deletionForm.value.preserveProfileData,preserveSecurityLogs:t.deletionForm.value.preserveSecurityLogs,customRetentionPeriod:t.deletionForm.value.customRetentionPeriod,reason:t.deletionForm.value.reason?.trim()||void 0};yield t.accountDeletionService.requestAccountDeletion(l).toPromise(),t.snackBar.open("Account deletion requested! Please check your email for confirmation instructions.","Close",{duration:8e3,panelClass:["snack-bar-warning"]}),t.step="confirmation",yield t.checkExistingDeletionStatus()}catch(l){console.error("Error requesting deletion:",l),t.snackBar.open(l.message||"Failed to request account deletion. Please try again.","Close",{duration:5e3,panelClass:["snack-bar-error"]})}finally{t.isLoading=!1}}})()}cancelDeletion(){var t=this;return(0,f.A)(function*(){if(t.currentStatus?.deletionRecord?.id&&(yield t.dialog.open(x,{width:"400px",data:{title:"Cancel Account Deletion",message:"Are you sure you want to cancel the account deletion request?",confirmText:"Yes, Cancel Deletion",cancelText:"Keep Deletion Request"}}).afterClosed().toPromise())){t.isLoading=!0;try{yield t.accountDeletionService.cancelDeletion().toPromise(),t.snackBar.open("Account deletion request has been cancelled.","Close",{duration:5e3,panelClass:["snack-bar-success"]}),t.currentStatus=null,t.step="preferences",t.deletionForm.reset()}catch(l){console.error("Error cancelling deletion:",l),t.snackBar.open(l.message||"Failed to cancel account deletion. Please try again.","Close",{duration:5e3,panelClass:["snack-bar-error"]})}finally{t.isLoading=!1}}})()}goToSettings(){this.router.navigate(["/dashboard/settings"])}markFormGroupTouched(){Object.keys(this.deletionForm.controls).forEach(t=>{this.deletionForm.get(t)?.markAsTouched()})}get preservedDataSummary(){const t=this.deletionForm.value,o=[];return t.preservePaymentData&&o.push("Payment data"),t.preserveTransactionHistory&&o.push("Transaction history"),t.preserveProfileData&&o.push("Profile data"),t.preserveSecurityLogs&&o.push("Security logs"),o}get retentionPeriodText(){const t=this.deletionForm.value.customRetentionPeriod||30;return 1===t?"1 day":t<30?`${t} days`:30===t?"1 month":t<365?`${Math.round(t/30)} months`:"1 year"}static#e=this.\u0275fac=function(o){return new(o||n)(e.rXU(a.ok),e.rXU(D.z),e.rXU(P.u),e.rXU(b.Ix),e.rXU(E.UG),e.rXU(d.h))};static#t=this.\u0275cmp=e.VBU({type:n,selectors:[["app-account-deletion"]],decls:11,vars:3,consts:[[1,"account-deletion-container"],[1,"deletion-card"],["color","warn"],["class","step-content",4,"ngIf"],[1,"step-content"],[1,"warning-message"],[3,"ngSubmit","formGroup"],[1,"section-description"],[1,"preferences-section"],["formControlName","preservePaymentData"],[1,"preference-description"],["formControlName","preserveTransactionHistory"],["formControlName","preserveProfileData"],["formControlName","preserveSecurityLogs"],["appearance","outline",1,"full-width"],["matInput","","type","number","formControlName","customRetentionPeriod","min","1","max","365","placeholder","30"],[4,"ngIf"],["matInput","","formControlName","reason","rows","3","placeholder","Tell us why you're deleting your account..."],["class","summary-section",4,"ngIf"],["formControlName","confirmDeletion",1,"confirmation-checkbox"],[1,"actions"],["mat-button","","type","button",3,"click"],["mat-raised-button","","color","warn","type","submit",3,"disabled"],["diameter","20",4,"ngIf"],[1,"summary-section"],[4,"ngFor","ngForOf"],[1,"retention-info"],["diameter","20"],[1,"confirmation-message"],["color","primary",1,"large-icon"],[1,"email-note"],["mat-button","",3,"click"],["mat-raised-button","","color","primary",3,"click"],[1,"status-message"],["color","warn",1,"large-icon"],["class","status-details",4,"ngIf"],["mat-button","","color","primary",3,"click"],["mat-raised-button","","color","accent",3,"click","disabled"],[1,"status-details"],[1,"detail-item"],[1,"status-badge"],["class","detail-item",4,"ngIf"]],template:function(o,i){1&o&&(e.j41(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"mat-card-title")(4,"mat-icon",2),e.EFF(5,"warning"),e.k0s(),e.EFF(6," Account Deletion "),e.k0s()(),e.j41(7,"mat-card-content"),e.DNE(8,A,61,7,"div",3)(9,I,21,0,"div",3)(10,X,18,4,"div",3),e.k0s()()()),2&o&&(e.R7$(8),e.Y8G("ngIf","preferences"===i.step),e.R7$(),e.Y8G("ngIf","confirmation"===i.step),e.R7$(),e.Y8G("ngIf","status"===i.step&&i.currentStatus))},dependencies:[m.MD,m.Sq,m.bT,m.PV,m.vh,a.X1,a.qT,a.me,a.Q0,a.BC,a.cb,a.VZ,a.zX,a.j4,a.JD,u.Hu,u.RN,u.m2,u.MM,u.dh,v.m_,v.An,p.Hl,p.$z,h.g7,h.So,y.M,g.j,g.M,g.c,g.b,k.fS,k.fg,_.D6,_.LG],styles:[".account-deletion-container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto;padding:20px}.deletion-card[_ngcontent-%COMP%]{margin-bottom:20px}.mat-card-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px}.warning-message[_ngcontent-%COMP%], .confirmation-message[_ngcontent-%COMP%], .status-message[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:15px;padding:15px;border-radius:8px;margin-bottom:20px}.warning-message[_ngcontent-%COMP%]{background-color:#fff3cd;border:1px solid #ffeaa7;color:#856404}.confirmation-message[_ngcontent-%COMP%], .status-message[_ngcontent-%COMP%]{background-color:#e3f2fd;border:1px solid #bbdefb;color:#1565c0;flex-direction:column;text-align:center}.large-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px}.step-content[_ngcontent-%COMP%]{margin:20px 0}.section-description[_ngcontent-%COMP%]{color:#666;margin-bottom:15px}.preferences-section[_ngcontent-%COMP%]{margin:20px 0;display:flex;flex-direction:column;gap:15px}.preference-description[_ngcontent-%COMP%]{font-size:12px;color:#888;margin-top:4px}.full-width[_ngcontent-%COMP%]{width:100%;margin:10px 0}.summary-section[_ngcontent-%COMP%]{background-color:#f8f9fa;padding:15px;border-radius:8px;border:1px solid #e9ecef;margin:20px 0}.summary-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 10px;color:#495057}.summary-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:10px 0;padding-left:20px}.retention-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:5px;margin-top:10px;font-size:14px;color:#6c757d}.confirmation-checkbox[_ngcontent-%COMP%]{margin:20px 0}.status-details[_ngcontent-%COMP%]{background-color:#f8f9fa;padding:15px;border-radius:8px;margin:20px 0}.status-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 15px;color:#495057}.detail-item[_ngcontent-%COMP%]{margin:8px 0;display:flex;gap:10px}.detail-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{min-width:120px}.status-badge[_ngcontent-%COMP%]{background-color:#ffc107;color:#212529;padding:2px 8px;border-radius:12px;font-size:12px;font-weight:500}.actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-top:20px;gap:15px}.actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.email-note[_ngcontent-%COMP%]{font-size:14px;color:#6c757d;margin-top:10px}@media (max-width: 768px){.account-deletion-container[_ngcontent-%COMP%]{padding:10px}.actions[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;justify-content:center}.warning-message[_ngcontent-%COMP%], .confirmation-message[_ngcontent-%COMP%], .status-message[_ngcontent-%COMP%]{flex-direction:column;text-align:center}}"]})}return n})()}}]);