"""AddDataRestoredAtToAccountDeletionRecords Migration."""

from masoniteorm.migrations import Migration


class AddDataRestoredAtToAccountDeletionRecords(Migration):
    def up(self):
        """
        Run the migrations.
        """
        with self.schema.table("account_deletion_records") as table:
            table.timestamp("data_restored_at").nullable()

    def down(self):
        """
        Revert the migrations.
        """
        with self.schema.table("account_deletion_records") as table:
            table.drop_column("data_restored_at")
