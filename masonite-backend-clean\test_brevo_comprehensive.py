#!/usr/bin/env python3
"""
Comprehensive Brevo Email Test
Tests both SMTP and API methods for sending emails
"""

import os
import sys
import requests
import json
import time

# Add the masonite project to path
sys.path.append(os.path.join(os.path.dirname(__file__)))

# Initialize Masonite application
from wsgi import application

from masonite.environment import env
from app.services.BrevoEmailService import BrevoEmailService
from app.models.User import User

def test_brevo_api_directly():
    """Test Brevo API directly without going through registration"""
    
    print("🧪 Testing Brevo API Directly")
    print("=" * 50)
    
    # Check if API key is configured
    api_key = env('BREVO_API_KEY')
    if not api_key or api_key == 'your-brevo-api-key-here':
        print("❌ Brevo API key not configured in .env file")
        print("📝 Please update BREVO_API_KEY in .env with your actual API key")
        return False
    
    # Find a test user
    test_user = User.where('email', 'like', '%@example.com').first()
    if not test_user:
        print("⚠️  No test user found. Creating one...")
        test_user = User.create({
            'name': 'API Test User',
            'email': '<EMAIL>',
            'password': 'hashed_password_here',
            'email_verified_at': None
        })
    
    print(f"👤 Testing with user: {test_user.email}")
    
    try:
        # Test Brevo API service
        brevo_service = BrevoEmailService()
        result = brevo_service.send_verification_email(test_user, 'test_token_123')
        
        if result['success']:
            print("✅ Brevo API test SUCCESSFUL!")
            print(f"📧 Email sent to: {result['to']}")
            print(f"🆔 Message ID: {result['message_id']}")
            print(f"🔧 Method: {result['method']}")
            return True
        else:
            print("❌ Brevo API test FAILED")
            print(f"Error: {result['error']}")
            if 'status_code' in result:
                print(f"Status Code: {result['status_code']}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during API test: {e}")
        return False

def test_registration_with_email():
    """Test registration endpoint to see email sending in action"""
    
    print("\n🧪 Testing Registration with Email Sending")
    print("=" * 50)
    
    # Test data
    timestamp = int(time.time())
    test_data = {
        "firstName": "Brevo",
        "lastName": "Test",
        "email": f"brevotest{timestamp}@example.com",
        "phone": "",
        "password": "BrevoTest123!",
        "confirmPassword": "BrevoTest123!",
        "acceptTerms": True
    }
    
    headers = {
        "Content-Type": "application/json",
        "Origin": "http://localhost:4200"
    }
    
    try:
        print(f"📝 Registering user: {test_data['email']}")
        
        response = requests.post(
            "http://localhost:3002/api/auth/signup",
            json=test_data,
            headers=headers,
            timeout=15
        )
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 201:
            print("✅ Registration SUCCESSFUL!")
            
            try:
                response_data = response.json()
                print(f"🆔 User ID: {response_data.get('user', {}).get('id', 'N/A')}")
                print(f"📧 Email: {response_data.get('user', {}).get('email', 'N/A')}")
                print(f"🔑 Token: {'Present' if response_data.get('token') else 'Missing'}")
                
                print("\n📨 Check server console for email sending logs:")
                print("   Look for: '✅ Email sent via Brevo API' or '✅ Email sent via SMTP'")
                
                return True
                
            except json.JSONDecodeError:
                print("⚠️  Could not parse JSON response")
                print(f"Response: {response.text}")
                
        else:
            print(f"❌ Registration failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Is the Masonite server running?")
        print("💡 Start server with: conda activate masonite-secure-env; python craft serve --port 8001")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def show_configuration_guide():
    """Show configuration guide for both methods"""
    
    print("\n📋 Configuration Guide")
    print("=" * 50)
    
    print("🔧 Current .env Configuration:")
    print(f"   MAIL_DRIVER: {env('MAIL_DRIVER', 'NOT SET')}")
    print(f"   MAIL_HOST: {env('MAIL_HOST', 'NOT SET')}")
    print(f"   MAIL_USERNAME: {env('MAIL_USERNAME', 'NOT SET')}")
    print(f"   BREVO_API_KEY: {'SET' if env('BREVO_API_KEY') and env('BREVO_API_KEY') != 'your-brevo-api-key-here' else 'NOT SET'}")
    
    print("\n🎯 For SMTP Method:")
    print("   MAIL_DRIVER=smtp")
    print("   MAIL_HOST=smtp-relay.brevo.com")
    print("   MAIL_PORT=587")
    print("   MAIL_USERNAME=<EMAIL>")
    print("   MAIL_PASSWORD=your-brevo-api-key")
    print("   MAIL_ENCRYPTION=tls")
    
    print("\n🎯 For API Method (Recommended):")
    print("   BREVO_API_KEY=your-brevo-api-key")
    print("   BREVO_API_URL=https://api.brevo.com/v3/smtp/email")
    
    print("\n🔗 Get your Brevo API key:")
    print("   1. Login to Brevo dashboard")
    print("   2. Go to: SMTP & API → API Keys")
    print("   3. Copy your API key")
    print("   4. Update .env file with the real key")

if __name__ == "__main__":
    print("🚀 Brevo Email Configuration Test")
    print("=" * 50)
    
    # Show current configuration
    show_configuration_guide()
    
    # Test API directly
    api_success = test_brevo_api_directly()
    
    # Test registration
    if api_success:
        print("\n" + "=" * 50)
        registration_success = test_registration_with_email()
        
        if registration_success:
            print("\n🎉 All tests completed successfully!")
            print("📧 Emails should be sent via Brevo API")
        else:
            print("\n⚠️  Registration test failed, but API test passed")
            print("💡 Check server logs and try restarting the server")
    else:
        print("\n⚠️  API test failed. Please check configuration.")
        print("🔧 Update BREVO_API_KEY in .env and try again")
    
    print("\n📝 Next steps:")
    print("   1. Update .env with your real Brevo API key")
    print("   2. Restart Masonite server")
    print("   3. Test registration again")
