#!/usr/bin/env python3
import requests
import json
from datetime import datetime

# Test password reset with the actual account owner email
endpoint_url = 'http://localhost:3002/api/auth/forgot-password'
test_email = '<EMAIL>'

payload = {'email': test_email}
headers = {
    'Content-Type': 'application/json',
    'Origin': 'http://localhost:4200'
}

print(f'🎯 Testing password reset for account owner: {test_email}')
print(f'⏰ Time: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')

try:
    response = requests.post(endpoint_url, json=payload, headers=headers, timeout=15)
    print(f'📊 Status: {response.status_code}')
    
    if response.status_code == 200:
        data = response.json()
        print(f'✅ SUCCESS: {data.get("message", "No message")}')
        print(f'📧 Check inbox: {test_email}')
        print(f'📁 Also check spam/junk folder')
    else:
        print(f'❌ FAILED: {response.text}')
        
except Exception as e:
    print(f'❌ ERROR: {e}')
